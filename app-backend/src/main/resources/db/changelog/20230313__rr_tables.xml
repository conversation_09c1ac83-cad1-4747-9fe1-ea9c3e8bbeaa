<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="107371.1" author="denis.berestinsky">
        <sql><![CDATA[
            create type month_enum as enum(
                'JANUARY',
                'FEBRUARY',
                'MARCH',
                'APRIL',
                'MAY',
                'JUNE',
                'JULY',
                'AUGUST',
                'SEPTEMBER',
                'OCTOBER',
                'NOVEMBER',
                'DECEMBER'
            );

            alter table toro_works add primary key(id);

            create table if not exists rr_dictionary(
                atp_id bigint not null references atp(id),
                toro_work_id varchar not null references toro_works(id),
                tonnage double precision not null,
                year int not null,
                month month_enum not null,
                rate double precision not null,
                created_at timestamptz not null,
                created_by varchar not null,
                updated_at timestamptz,
                updated_by varchar,
                primary key(year, atp_id, tonnage, month, toro_work_id)
            );

            create or replace view rr_dictionary_view(
                year,
                atp_id,
                tonnage,
                month,
                rate
            ) as select
                year,
                atp_id,
                tonnage,
                month,
                sum(rate)
            from rr_dictionary
            group by year, atp_id, tonnage, month;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
