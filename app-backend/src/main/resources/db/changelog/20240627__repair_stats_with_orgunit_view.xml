<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1673607_2.3" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists  repair_stats_org_ut_view;
            create or replace view repair_stats_org_ut_view as
            select org.mr_id
                 , org.mr_name
                 , org.atp_id
                 , org.atp_name
                 , org.atp_type
                 , org.mvz_id
                 , org.mvz_name
                 , org.mvz_type
                 , org.retail_network
                 , rs.vehicle_date
                 , rs.ts_marka
                 , rs.ts_load_wgt
                 , rs.ts_type
                 , rs.ts_group
                 , rs.mileage
                 , rs.mileage_trailer
                 , rs.mileage_full
            from repair_stats rs
                     join organizational_units_timeline org on (rs.mvz_id = org.mvz_id) and
                                                               (org.start_date <= rs.vehicle_date and
                                                                rs.vehicle_date < org.end_date);
        ]]></sql>
    </changeSet>
</databaseChangeLog>
