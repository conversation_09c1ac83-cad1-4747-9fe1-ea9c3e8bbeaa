<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1349867.1" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view drivers_per_vehicle_view;

            alter table hr_drivers_per_vehicle
            alter column value type numeric using value::numeric;

            create view drivers_per_vehicle_view (atp_name, place_name, mr_name, retail_network, atp_type, year, month, value) as
            SELECT a.name  AS atp_name,
                   hp.name AS place_name,
                   mr.name AS mr_name,
                   al.retail_network,
                   al.type AS atp_type,
                   hdpv.year,
                   hdpv.month,
                   hdpv.value
            FROM atp a
                     LEFT JOIN hr_drivers_per_vehicle hdpv ON hdpv.atp_id = a.id
                     LEFT JOIN (SELECT hapl_1.id,
                                       hapl_1.atp_id,
                                       hapl_1.hrp_id,
                                       hapl_1.start_date,
                                       hapl_1.created_at,
                                       hapl_1.created_by,
                                       hapl_1.updated_at,
                                       hapl_1.updated_by,
                                       hapl_1.deleted,
                                       lead(hapl_1.start_date, 1, '9999-12-31'::date)
                                           OVER (PARTITION BY hapl_1.atp_id ORDER BY hapl_1.start_date) AS end_date
                                FROM hr_atp_places_log hapl_1) hapl
                               ON a.id = hapl.atp_id AND hapl.start_date <= CURRENT_DATE AND hapl.end_date >= CURRENT_DATE
                     LEFT JOIN hr_places hp ON hapl.hrp_id = hp.id
                     LEFT JOIN atp_log al ON a.id = al.atp_id AND al.start_date <= CURRENT_DATE AND
                                             (al.end_date IS NULL OR al.end_date >= CURRENT_DATE)
                     LEFT JOIN macro_region mr ON al.mr_id = mr.id;
            ]]></sql>
    </changeSet>
</databaseChangeLog>
