<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1673607_3.1" author="anton.podoprygolov">
        <sql><![CDATA[
            create index if not exists repair_order_id_idx on repair(order_id);
            create index if not exists repair_resources_order_id_idx on repair_resources(order_id);
            create index if not exists repair_mvz_date_idx on repair(vehicle_mvz_id, repair_start_date);
            create index if not exists repair_stats_mvz_date_idx on repair_stats(mvz_id, vehicle_date);
            create index if not exists repair_marka_tonnage_type_idx on repair(ts_marka, ts_load_wgt, ts_type);
            create index if not exists repair_stats_marka_tonnage_type_idx on repair_stats(ts_marka, ts_load_wgt, ts_type);
            create index if not exists repair_resources_order_id_marka_tonnage_type_idx on repair_resources(order_id, ts_marka, ts_load_wgt, ts_type);
            ]]></sql>
    </changeSet>
</databaseChangeLog>
