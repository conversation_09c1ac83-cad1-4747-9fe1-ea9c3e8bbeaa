<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1912871.2" author="ayzhan.zheksembek">
        <sql><![CDATA[
            drop view if exists ts.repair_chart_joined_view;

            CREATE OR REPLACE VIEW ts.repair_chart_joined_view
            AS SELECT r.order_id,
                r.repair_start_date AS start_date,
                o.territory_id,
                COALESCE(o.territory_name, 'N/A'::character varying) as territory_name,
                o.mr_id,
                COALESCE(o.mr_name, 'N/A'::character varying) AS mr_name,
                o.atp_id,
                COALESCE(o.atp_name, 'N/A'::character varying) AS atp_name,
                COALESCE(o.mvz_id, 'N/A'::character varying) AS mvz_id,
                COALESCE(o.mvz_name, 'N/A'::character varying) AS mvz_name,
                COALESCE(r.final_repshop_name, 'N/A'::character varying) AS repair_atp,
                COALESCE(o.retail_network, 'N/A'::character varying) AS retail_network,
                COALESCE(o.atp_type, 'N/A'::character varying) AS atp_type,
                COALESCE(o.mvz_type, 'N/A'::character varying) AS mvz_type,
                ro.repshop_id,
                    CASE
                        WHEN r.our_workshop = true AND r.final_service_mvz_id::text = 'МВЗ не определено'::text THEN 'РЗ не определена'::character varying
                        ELSE ro.repshop_name
                    END AS repshop_name,
                COALESCE(r.repair_kind, 'N/A'::character varying) AS repair_kind,
                COALESCE(
                    CASE
                        WHEN r.repair_kind::text = ANY (ARRAY['X540'::text, 'X542'::text, 'X543'::text]) THEN 'Ремзона'::character varying
                        WHEN r.repair_kind::text = ANY (ARRAY['X541'::text, 'X544'::text]) THEN 'СТО'::character varying
                        ELSE r.repair_kind
                    END, 'N/A'::character varying) AS repair_place,
                COALESCE(date_part('year'::text, r.repair_start_date)::text, 'N/A'::text) AS repair_year,
                COALESCE(date_part('month'::text, r.repair_start_date)::text, 'N/A'::text) AS month,
                COALESCE(date_part('week'::text, r.repair_start_date)::text, 'N/A'::text) AS week,
                COALESCE(to_char(r.repair_start_date::timestamp with time zone, 'MM.YYYY'::text), 'N/A'::text) AS month_year,
                COALESCE(to_char(r.repair_start_date::timestamp with time zone, '"Н "IW.IYYY'::text), 'N/A'::text) AS week_year,
                COALESCE(to_char((r.repair_start_date + 3)::timestamp with time zone, '"ОН "IW.IYYY'::text), 'N/A'::text) AS report_week_year,
                COALESCE(to_char(r.repair_start_date::timestamp with time zone, '"Кв "Q.YYYY'::text), 'N/A'::text) AS quarter_year,
                COALESCE(to_char(r.repair_start_date::timestamp with time zone, 'DD.MM.YYYY'::text), 'N/A'::text) AS day_month_year,
                COALESCE(r.vrt, 'N/A'::character varying) AS vrt,
                COALESCE(COALESCE(r.vrt_name, ('Код ВРТ - '::text || r.vrt::text)::character varying), 'N/A'::character varying) AS vrt_name,
                COALESCE(r.event_id, 'N/A'::character varying) AS event_id,
                COALESCE(r.event_text, 'N/A'::character varying) AS event_text,
                COALESCE(r.repair_type_id::text, 'N/A'::text) AS repair_type,
                coalesce (twt.name, 'N/A') AS repair_type_name,
                COALESCE(r.repair_subtype_id::text, 'N/A'::text) AS repair_subtype,
                coalesce (tws.name, 'N/A') AS repair_subtype_name,
                r.equnr,
                COALESCE(r.ts_license_num, 'N/A'::character varying) AS vehicle_licence,
                COALESCE(r.ts_load_wgt, 0.0) AS vehicle_tonnage,
                COALESCE(r.ts_marka, 'N/A'::character varying) AS vehicle_brand,
                COALESCE(r.ts_model, 'N/A'::character varying) AS vehicle_model,
                COALESCE(r.ts_type, 'N/A'::character varying) AS vehicle_type,
                COALESCE(r.ts_group, 'N/A'::character varying) AS vehicle_group,
                COALESCE(date_part('year'::text, r.ts_create_date)::text, 'N/A'::text) AS vehicle_create_year,
                    CASE
                        WHEN r.ts_gbo = true THEN 'С ГБО'::text
                        ELSE 'Без ГБО'::text
                    END AS vehicle_gbo,
                r.repair_expenses,
                r.part_amount,
                r.part_expenses,
                r.services_amount,
                r.services_expenses
               FROM ts.repair r
                 LEFT JOIN ts.organizational_units_timeline o ON r.vehicle_mvz_id::text = o.mvz_id::text AND r.repair_start_date >= o.start_date AND r.repair_start_date < o.end_date
                 LEFT JOIN ts.organizational_units_timeline ro ON r.repair_start_date >= ro.start_date AND r.repair_start_date < ro.end_date AND r.final_service_mvz_id::text = ro.mvz_id::text
                 LEFT JOIN ts.toro_works tw ON r.vrt::text = tw.id::text
                 LEFT JOIN ts.toro_works_types twt ON tw.subtype_id = twt.id
                 LEFT JOIN ts.toro_works_subtypes tws ON tw.subtype_id = tws.id;
            ]]></sql>
    </changeSet>
</databaseChangeLog>