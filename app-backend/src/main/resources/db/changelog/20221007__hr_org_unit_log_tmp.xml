<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="3" author="i<PERSON><PERSON><PERSON><PERSON>">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="hr_org_unit_log_tmp"/>
            </not>
        </preConditions>
        <sql>
            <![CDATA[
            create table ts.hr_org_unit_log_tmp as (
                select
                    hr_org_unit.id,
                    hr_org_unit_log.hr_place_id,
                    hr_org_unit_log.hr_total_place_id,
                    hr_org_unit_log.start_date,
                    hr_org_unit_log.end_date,
                    hr_org_unit_log.author
                from
                    ts.hr_org_unit
                        join
                    ts.hr_org_unit_log on hr_org_unit.org_unit_id = hr_org_unit_log.org_unit_id
            )
            ]]>
            </sql>
    </changeSet>
</databaseChangeLog>