--liquibase formatted sql

--changeset anton.pod<PERSON><PERSON><PERSON><PERSON>@x5.ru:1954215.1
insert into report_templates as tab(report, "user", name, type, create_time, update_time, deleted, settings)
values ('KIPFACTOR', 'system', 'Факторы снижения КИП', 'public', current_timestamp, current_timestamp, false, '
{
  "sort": [
    {
      "asc": 1,
      "name": "mr",
      "label": "Макрорегион"
    },
    {
      "asc": 1,
      "name": "atp",
      "label": "АТП"
    },
    {
      "asc": 1,
      "name": "kipShare",
      "label": "КИП, %"
    },
    {
      "asc": 1,
      "name": "vehicleLicense",
      "label": "Гос. номер ТС"
    }
  ],
  "columns": [
    {
      "name": "mr",
      "label": "Макрорегион",
      "isPinned": true,
      "isVisible": true
    },
    {
      "name": "atp",
      "label": "АТП",
      "isPinned": true,
      "isVisible": true
    },
    {
      "name": "vehicleLicense",
      "label": "Гос. номер ТС",
      "isPinned": true,
      "isVisible": true
    },
    {
      "name": "mvz",
      "label": "МВЗ",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "mvzName",
      "label": "Название МВЗ",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "retailNetwork",
      "label": "Торговая сеть",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "atpType",
      "label": "Вид деятельности АТП",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "mvzType",
      "label": "Тип МВЗ",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleId",
      "label": "Единица оборудования",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleVin",
      "label": "VIN номер",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleGroup",
      "label": "Вид ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleType",
      "label": "Тип ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleBrand",
      "label": "Марка",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleModel",
      "label": "Модель",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleTonnage",
      "label": "Тоннаж",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleCreateYear",
      "label": "Год ввода в эксплуатацию",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleCount",
      "label": "Ср. кол-во ТС, шт.",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "ktgShare",
      "label": "КТГ, %",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "rgShare",
      "label": "РГ, %",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "kipShare",
      "label": "КИП, %",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "kipPlan",
      "label": "КИП план, мч",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "ktgHours",
      "label": "КТГ, мч",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "rgHours",
      "label": "РГ, мч",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "kipHours",
      "label": "КИП факт, мч",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "factor",
      "label": "Факторы",
      "isPinned": false,
      "isVisible": true
    }
  ],
  "filters": [
    {
      "name": "vehicleGroup",
      "value": [
        "Основное"
      ],
      "readonly": true,
      "condition": "contain"
    },
    {
      "name": "factor",
      "value": [
        "Ремонт 1 день, мч",
        "Ремонт до 7 дней, мч",
        "Долгострой (7 и более дней), мч",
        "Ремонт ДТП, мч",
        "Пропуск блокирован, мч",
        "Прочее, мч",
        "Пропуск просрочен, мч",
        "Полис ОСАГО недействителен, мч",
        "Полис КАСКО недействителен, мч",
        "Резерв, мч",
        "ДТП до оформления, мч",
        "ДТП заявка в сервис, мч",
        "Командировка, мч",
        "Некорректная сцепка, мч",
        "Тахограф недействителен, мч",
        "Истекает срок действия тахографа, мч",
        "БУ Платон недействителен, требуется оформл. маршрутной карты, мч",
        "Истекает срок БУ Платон, мч",
        "Простой 50/50, мч",
        "Нет водителя, мч",
        "Коммерция, мч",
        "Сверх плана, мч",
        "Не подано в ТГ, мч",
        "Готов, нет ПЛ, мч",
        "Рем. ПЛ, мч"
      ],
      "condition": "contain"
    },
    {
      "name": "kipShare",
      "value": 98,
      "condition": "less"
    }
  ],
  "granularity": "Day"
}
'::jsonb) ON CONFLICT DO NOTHING;
