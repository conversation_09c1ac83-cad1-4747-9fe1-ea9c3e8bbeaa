--liquibase formatted sql

--changeset anton.pod<PERSON><PERSON><PERSON><PERSON>@x5.ru:1958331_0 add columns and change name for mileage
update report_templates
set settings = '{
  "sort": [],
  "columns": [
    {
      "name": "atp",
      "label": "АТП",
      "isPinned": true,
      "isVisible": true
    },
    {
      "name": "licenseNum",
      "label": "Гос. номер ТС",
      "isPinned": true,
      "isVisible": true
    },
    {
      "name": "numberWaybillEnd",
      "label": "Дата и время закрытия ПЛ",
      "isPinned": true,
      "isVisible": true
    },
    {
      "name": "waybillNumber",
      "label": "Номер ПЛ",
      "isPinned": true,
      "isVisible": true
    },
    {
      "name": "gbo",
      "label": "ГБО",
      "isPinned": true,
      "isVisible": true
    },
    {
      "name": "numberWaybillStart",
      "label": "Дата и время открытия ПЛ",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "typeWB",
      "label": "Тип ПЛ",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "maintenance",
      "label": "Ремонтный ПЛ",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "runWaybill",
      "label": "Пробег по ПЛ",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "probegWithGas",
      "label": "Пробег с газом",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "mountainMileage",
      "label": "Пробег горный",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "engineHoursFridgeHead",
      "label": "Моточасы ХОУ (г), ч.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "engineHoursAcTrailer",
      "label": "Моточасы ХОУ (п), ч.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "normaDieselOn100Km",
      "label": "Норма ДТ/ 100 км (К1_П), л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "normaFridgeHeadDieselPerHour",
      "label": "Норма ХОУ (г) ДТ/ час, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "normaFridgeTrailerDieselPerHour",
      "label": "Норма ХОУ (п) ДТ / час, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "normaGasOn100Km",
      "label": "Норма Газ/ 100 км, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "winterCoefficient",
      "label": "Зимний коэф. (К1)",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "mountainCoefficient",
      "label": "Горный коэф. (S)",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "coefficientReductionDiesel",
      "label": "Коэф. снижения ДТ (К8)",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "costDieselOnLiter",
      "label": "Стоимость ДТ / л, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "costGasOnLiter",
      "label": "Стоимость Газ / л, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "macroRegion",
      "label": "Макрорегион",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "mvz",
      "label": "Номер МВЗ",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "mvzName",
      "label": "Наименование МВЗ",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleType",
      "label": "Тип ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "vehicleGroup",
      "label": "Вид ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "brand",
      "label": "Марка ТС",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "model",
      "label": "Модель ТС",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "tonnage",
      "label": "Тоннаж ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "fuelTypeFirst",
      "label": "Первичное топливо ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "year",
      "label": "Год выпуска ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "startDate",
      "label": "Год начала эксплуатации ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "tabNum",
      "label": "Табельный номер водителя",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "vin",
      "label": "VIN номер ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "eqUnit",
      "label": "Единица оборудования ТС",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "licenseNumTrailer",
      "label": "Гос. номер прицепа",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "planConsumptionDieselLiter",
      "label": "Пл.расх. ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionHeadDieselLiter",
      "label": "Пл.расх (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionFridgeHeadDieselLiter",
      "label": "Пл.расх ХОУ (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionNoFridgeHeadDieselLiter",
      "label": "Пл.расх. без ХОУ (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionFridgeTrailerDieselLiter",
      "label": "Пл.расх. ХОУ (п) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionDieselLiter",
      "label": "Факт. расх. ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionHeadDieselLiter",
      "label": "Факт. расх. (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionFridgeHeadDieselLiter",
      "label": "Факт. расх. ХОУ (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionNoFridgeHeadDieselLiter",
      "label": "Факт. расх. без ХОУ (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionFridgeTrailerDieselLiter",
      "label": "Факт. расх. ХОУ (п) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "burnoutEconomyDieselLiter",
      "label": "Пережог (+) /Экономия (-) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "burnoutEconomyNoFridgeHeadDieselLiter",
      "label": "Пережог (+) /Экономия (-) без ХОУ (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "burnoutEconomyFridgeTrailerDieselLiter",
      "label": "Пережог (+) /Экономия (-) ХОУ (п) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionOn100DieselLiter",
      "label": "Пл.расх./100 км. ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionOn100NoFridgeHeadDieselLiter",
      "label": "Пл.расх./ 100 км.без ХОУ (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionOn100DieselLiter",
      "label": "Факт.расх./100 км.ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionOn100NoFridgeHeadDieselLiter",
      "label": "Факт.расх./ 100 км.без ХОУ (г) ДТ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionDieselRub",
      "label": "Пл.расх. ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionHeadDieselRub",
      "label": "Пл.расх. (г) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionFridgeHeadDieselRub",
      "label": "Пл.расх. ХОУ (г) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionNoFridgeHeadDieselRub",
      "label": "Пл.расх.без ХОУ (г) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionFridgeTrailerDieselRub",
      "label": "Пл.расх. ХОУ (п) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionDieselRub",
      "label": "Факт.расх. ДТ, руб.",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "factConsumptionHeadDieselRub",
      "label": "Факт.расх. (г) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionFridgeHeadDieselRub",
      "label": "Факт.расх. ХОУ (г) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionNoFridgeHeadDieselRub",
      "label": "Факт.расх.без ХОУ (г) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionFridgeTrailerDieselRub",
      "label": "Факт.расх. ХОУ (п) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "overspendingEconomyDieselRub",
      "label": "Перерасход (+) /Экономия (-) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "overspendingEconomyNoFridgeHeadDieselRub",
      "label": "Перерасход (+) /Экономия (-) без ХОУ (г) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "overspendingEconomyFridgeTrailerDieselRub",
      "label": "Перерасход (+) /Экономия (-) ХОУ (п) ДТ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionDieselRubKm",
      "label": "Пл.расх. ДТ, руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionDieselRubKm",
      "label": "Факт.расх. ДТ, руб/км.",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "overspendingEconomyDieselRubKm",
      "label": "Перерасход (+) /Экономия (-) ДТ, руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionGasLiter",
      "label": "Пл.расх. Газ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionGasLiter",
      "label": "Факт. расход Газ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionOn100GasLiter",
      "label": "Пл.расх./100 км. Газ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionOn100GasLiter",
      "label": "Факт.расх./100 км.Газ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "burnoutEconomyGasLiter",
      "label": "Пережог (+) /Экономия (-) Газ, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionGasRub",
      "label": "Пл.расх. Газ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionGasRub",
      "label": "Факт.расх. Газ, руб.",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "overspendingEconomyGasRub",
      "label": "Перерасход (+) /Экономия (-) Газ, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionGasRubKm",
      "label": "Пл.расх. Газ, руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionGasRubKm",
      "label": "Факт.расх. Газ, руб/км.",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "overspendingEconomyGasRubKm",
      "label": "Перерасход (+) /Экономия (-) Газ, руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionLiter",
      "label": "Пл.расх., л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionLiter",
      "label": "Факт.расх., л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "burnoutEconomyLiter",
      "label": "Пережог (+) /Экономия (-), л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionOn100Liter",
      "label": "Пл.расх./ 100 км., л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionOn100Liter",
      "label": "Факт.расх./ 100 км., л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planConsumptionRub",
      "label": "Пл.расх., руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionRub",
      "label": "Факт.расх., руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "overspendingEconomyRub",
      "label": "Перерасход (+) /Экономия (-), руб.",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "planConsumptionRubKm",
      "label": "Пл.расх., руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factConsumptionRubKm",
      "label": "Факт.расх., руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "overspendingEconomyRubKm",
      "label": "Перерасход (+) /Экономия (-), руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "presumptiveConsumptionDieselOutGbo",
      "label": "Предпол.расход ДТ без ГБО, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planEffectDieselWithGbo",
      "label": "Пл. эффект на ДТ от ГБО, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factEffectDieselWithGbo",
      "label": "Факт. эффект на ДТ с ГБО, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planEffectDieselWithGboOn100",
      "label": "Пл. эффект на ДТ от ГБО /100 км, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factEffectDieselWithGboOn100",
      "label": "Факт. эффект на ДТ с ГБО /100 км, л.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planEffectWithGboRub",
      "label": "Пл. эффект от ГБО, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factEffectWithGboRub",
      "label": "Факт. эффект от ГБО , руб",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "overspendingEconomyPlanEffectWithGbo",
      "label": "Перерасход (+) /Экономия (-) от план.эффекта ГБО, руб.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planEffectWithGboRubKm",
      "label": "Пл. эффект от ГБО, руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factEffectWithGboRubKm",
      "label": "Факт. эффект от ГБО, руб/км.",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "planPercentDeclineDiesel",
      "label": "Пл.% сниж.ДТ, %",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factPercentDeclineDiesel",
      "label": "Факт.% сниж.ДТ, %",
      "isPinned": false,
      "isVisible": true
    },
    {
      "name": "planCoefficientSubstitutionDieselByGas",
      "label": "Пл. Коэф. замещения ДТ Газом",
      "isPinned": false,
      "isVisible": false
    },
    {
      "name": "factCoefficientSubstitutionDieselByGas",
      "label": "Факт. Коэф. замещения ДТ Газом",
      "isPinned": false,
      "isVisible": true
    }
  ],
  "filters": [
    {
      "name": "vehicleGroup",
      "value": [
        "Основное"
      ],
      "readonly": false,
      "condition": "contain"
    }
  ]
}'::jsonb
where report = 'FUEL'
  and type = 'default';