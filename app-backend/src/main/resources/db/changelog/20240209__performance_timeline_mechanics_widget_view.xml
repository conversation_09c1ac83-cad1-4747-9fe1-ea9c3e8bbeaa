<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1477555_3.1" author="podoprygolov.anton">
        <sql><![CDATA[
            drop view if exists mech_performance_timeline;
            create view mech_performance_timeline(day, month, year, daily_value, rs_id) as
            WITH months AS (SELECT months.text_month,
                                   row_number() OVER ()::integer AS month_number
                            FROM (SELECT unnest(enum_range(NULL::month_enum))::text AS text_month) months),
                 prop AS (SELECT repshops_properties.rs_id,
                                 repshops_properties.year,
                                 months.month_number,
                                 repshops_properties.value,
                                 make_date(repshops_properties.year, months.month_number, 1) AS date
                FROM repshops_properties
                JOIN months ON repshops_properties.month::text = months.text_month
                WHERE repshops_properties.property::text = 'mech_performance'::text),
                period AS (SELECT date_part('YEAR'::text, p_1.day)  AS year,
                date_part('MONTH'::text, p_1.day) AS month,
                p_1.day
                FROM (SELECT generate_series(
                '1900-01-01 00:00:00'::timestamp without time zone::timestamp with time zone,
                now() + '3 years'::interval, '1 day'::interval)::date AS day) p_1),
                daily AS (SELECT prop.rs_id,
                prop.year,
                prop.month_number,
                prop.value::double precision / date_part('days'::text, prop.date + '1 mon'::interval -
                prop.date::timestamp without time zone) AS daily_value
                FROM prop)
            SELECT p.day,
                   p.month,
                   p.year,
                   d.daily_value,
                   d.rs_id
            FROM period p
                     LEFT JOIN daily d ON p.year = d.year::double precision AND p.month = d.month_number::double precision
        ]]></sql>
    </changeSet>
</databaseChangeLog>
