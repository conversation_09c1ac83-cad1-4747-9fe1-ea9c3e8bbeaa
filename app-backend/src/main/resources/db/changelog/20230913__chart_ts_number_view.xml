<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="701734_2.2" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists chart_ts_number_view;
            create or replace view chart_ts_number_view as
            select
                vrt.ctid as id,
                vrt.equnr,
                vrt.start_date as vrt_start_date,
                vrt.end_date as vrt_end_date,
                vrt.inactive_date as vrt_inactive_date,
                vrt.mr_id as vrt_mr_id,
                mr.name as mr_name,
                vrt.atp_id as vrt_atp_id,
                vrt.atp_type as vrt_atp_type,
                atp.name as atp_name,
                vrt.mvz_id as vrt_mvz_id,
                vrt.retail_network as vrt_retail_network,
                vrt.mvz_name as vrt_mvz_name,
                vrt.mvz_type as vrt_mvz_type,
                extract(year from td.create_date) as td_create_date_year,
                td.inactive_day as td_inactive_day,
                td.deleted_day as td_deleted_day,
                td.truck_type as td_truck_type,
                td.ts_type as td_ts_type,
                td.marka as td_marka,
                td.model as td_model,
                td.load_wgt as td_load_wgt,
                (case
                    when td.gbo then 'С ГБО'
                    else 'Без ГБО'
                end) as td_gbo,
                td.license_num as td_license_num,
                td.create_date as td_create_date,
                extract(year from vrt.start_date) as vrt_start_date_year,
                extract(month from vrt.start_date) as vrt_start_date_month,
                extract(week from vrt.start_date) as vrt_start_date_week,
                extract(MONTH from vrt.start_date) || '.' ||
                    extract(YEAR from vrt.start_date) as month_year,
                ('Н ' || extract(WEEK from vrt.start_date) || '.' || (case
                     when (extract(MONTH from vrt.start_date) = 1 and
                           extract(WEEK from vrt.start_date) in
                           (52, 53))
                         then extract(YEAR from vrt.start_date) - 1
                     when (extract(MONTH from vrt.start_date) = 12 and
                           extract(WEEK from vrt.start_date) = 1)
                         then extract(YEAR from vrt.start_date) + 1
                     else extract(YEAR from vrt.start_date) end)) as week_year
            from vehicle_region_timeline vrt
            join ts_data td on vrt.equnr = td.equnr
            left join macro_region mr on mr.id = vrt.mr_id
            left join atp on atp.id = vrt.atp_id
            where td.ts_type is not null;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
