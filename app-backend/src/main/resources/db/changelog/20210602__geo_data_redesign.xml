<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1" author="nsmetanin">
        <createTable tableName="mvz_type">
            <column name="type" type="java.sql.Types.VARCHAR">
                <constraints primaryKey="true" primaryKeyName="mvz_type_pk"/>
            </column>
        </createTable>
        <sql>
            insert into mvz_type (type) values ('Административный');
            insert into mvz_type (type) values ('КДК');
            insert into mvz_type (type) values ('Макрорегион');
            insert into mvz_type (type) values ('Основной');
            insert into mvz_type (type) values ('Ремонтный');
            insert into mvz_type (type) values ('Списания');
            insert into mvz_type (type) values ('ЦО');
        </sql>

        <renameTable oldTableName="mvp" newTableName="mvz_log"/>
        <renameColumn tableName="mvz_log" oldColumnName="id" newColumnName="uid"/>

        <addColumn tableName="mvz_log">
            <column name="start_date" type="java.sql.Types.DATE" defaultValueDate="1900-01-01">
                <constraints nullable="false"/>
            </column>
            <column name="is_deleted" type="java.sql.Types.BOOLEAN" defaultValueDate="false">
                <constraints nullable="false"/>
            </column>
            <column name="author" type="java.sql.Types.VARCHAR"/>
            <column name="updateAt" type="java.sql.Types.TIMESTAMP"/>
        </addColumn>

        <createTable tableName="atp_type">
            <column name="type" type="java.sql.Types.VARCHAR">
                <constraints primaryKey="true" primaryKeyName="atp_type_pk"/>
            </column>
        </createTable>
        <sql>
            insert into atp_type (type) values ('ВФ');
            insert into atp_type (type) values ('Лизинг');
            insert into atp_type (type) values ('МФП');
            insert into atp_type (type) values ('Самовывоз');
            insert into atp_type (type) values ('ЦО');
        </sql>

        <createTable tableName="atp_log">
            <column name="atp_id" type="java.sql.Types.BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="mr_id" type="java.sql.Types.BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="java.sql.Types.VARCHAR">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="java.sql.Types.DATE">
                <constraints nullable="false"/>
            </column>
            <column name="is_deleted" type="java.sql.Types.BOOLEAN" defaultValueDate="false">
                <constraints nullable="false"/>
            </column>
            <column name="author" type="java.sql.Types.VARCHAR">
            </column>
            <column name="updateAt" type="java.sql.Types.TIMESTAMP">
            </column>
        </createTable>
        <addForeignKeyConstraint baseTableName="atp_log" baseColumnNames="atp_id"
            referencedTableName="atp" referencedColumnNames="id"
            constraintName="be_atp_log_id_fk"/>
        <addForeignKeyConstraint baseTableName="atp_log" baseColumnNames="mr_id"
            referencedTableName="macro_region" referencedColumnNames="id"
            constraintName="be_atp_log_mr_id_fk"/>
        <addForeignKeyConstraint baseTableName="atp_log" baseColumnNames="type"
            referencedTableName="atp_type" referencedColumnNames="type"
            constraintName="be_atp_log_type_fk"/>

        <sql>
            INSERT INTO atp_log
            (SELECT id, mr_id, atp_type, '1900-01-01' as start_date FROM atp)
        </sql>

        <dropColumn tableName="atp">
            <column name="atp_type"/>
            <column name="mr_id"/>
        </dropColumn>

        <createTable tableName="load_wgt">
            <column name="id" type="java.sql.Types.INTEGER">
                <constraints primaryKey="true" primaryKeyName="load_wgt_pk"/>
            </column>
            <column name="load_wgt" type="java.sql.Types.NUMERIC">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="java.sql.Types.VARCHAR">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="kip_tarif_log">
            <column name="mvz_uid" type="java.sql.Types.VARCHAR">
                <constraints nullable="false"/>
            </column>
            <column name="load_wgt" type="java.sql.Types.INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="java.sql.Types.DATE">
                <constraints nullable="false"/>
            </column>
            <column name="kip_tr" type="java.sql.Types.NUMERIC">
                <constraints nullable="false"/>
            </column>
            <column name="is_deleted" type="java.sql.Types.BOOLEAN" defaultValueDate="false">
                <constraints nullable="false"/>
            </column>
            <column name="author" type="java.sql.Types.VARCHAR"/>
            <column name="updateAt" type="java.sql.Types.TIMESTAMP"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="kip_tarif_log" baseColumnNames="mvz_uid"
            referencedTableName="mvz_log" referencedColumnNames="uid"
            constraintName="kip_tarif_log_mvz_fk"/>
        <addForeignKeyConstraint baseTableName="kip_tarif_log" baseColumnNames="load_wgt"
            referencedTableName="load_wgt" referencedColumnNames="id"
            constraintName="kip_tarif_log_load_wgt_fk"/>
    </changeSet>

</databaseChangeLog>