<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1062143.1" author="igor.belogolovskiy">
        <sql><![CDATA[
            drop view if exists drivers_per_vehicle_view;
            create
            or replace view drivers_per_vehicle_view as
            select a.name            as atp_name
                 , hp.name           as place_name
                 , mr.name           as mr_name
                 , al.retail_network as retail_network
                 , al.type           as atp_type
                 , hdpv.year as year
                 , hdpv.month        as month
                , hdpv.value        as value
            from atp a
                left join hr_drivers_per_vehicle hdpv on hdpv.atp_id = a.id
                left join (select *,
                    lead(hapl.start_date, 1, '9999-12-31':: date)
                    over (partition by hapl.atp_id order by hapl.start_date) as end_date
                from hr_atp_places_log hapl) hapl
                    on a.id = hapl.atp_id and hapl.start_date <= current_date and hapl.end_date >= current_date
                left join hr_places hp on hapl.hrp_id = hp.id
                left join atp_log al on a.id = al.atp_id and al.start_date <= current_date and
                    (al.end_date is null or al.end_date >= current_date)
                left join macro_region mr on al.mr_id = mr.id;
            ]]></sql>
    </changeSet>
</databaseChangeLog>
