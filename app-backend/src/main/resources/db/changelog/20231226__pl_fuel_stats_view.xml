<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1344763_1345541.1" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view pl_fuel_stats_view;
            create or replace view pl_fuel_stats_view as select
                ctid,
                mr_name,
                atp_name,
                model,
                probeg,
                probeg_gorny,
                moto_hours_head,
                moto_hours_trail,
                k1_p,
                norma_hou_head,
                norma_hou_trail,
                norma_gas,
                coef_zimny,
                coef_gorny,
                coef_reduce_fuel,
                fuel_price,
                gas_price,
                rashod_fuel_plan,
                rashod_fuel_plan_head_tot,
                rashod_fuel_plan_head_hou,
                rashod_fuel_plan_head,
                rashod_fuel_plan_trail_hou,
                rashod_fuel_fact,
                rashod_fuel_fact_head_tot,
                rashod_fuel_fact_head_hou,
                rashod_fuel_fact_head,
                rashod_fuel_fact_trail_hou,
                econ_fuel,
                econ_fuel_head,
                econ_fuel_trail_hou,
                rashod_gas_plan,
                rashod_gas_fact,
                econ_gas,
                rashod_fuel_plan_rub,
                rashod_fuel_plan_head_tot_rub,
                rashod_fuel_plan_head_hou_rub,
                rashod_fuel_plan_head_rub,
                rashod_fuel_plan_trail_hou_rub,
                rashod_fuel_fact_rub,
                rashod_fuel_fact_head_tot_rub,
                rashod_fuel_fact_head_hou_rub,
                rashod_fuel_fact_head_rub,
                rashod_fuel_fact_trail_hou_rub,
                econ_fuel_rub,
                econ_fuel_head_rub,
                econ_fuel_trail_hou_rub,
                rashod_gas_plan_rub,
                rashod_gas_fact_rub,
                econ_gas_rub,
                rashod_tot_plan,
                rashod_tot_fact,
                econ_tot,
                rashod_tot_plan_rub,
                rashod_tot_fact_rub,
                econ_tot_rub,
                rashod_fuel_without_gbo_assumed,
                effect_fuel_of_gbo_plan,
                effect_fuel_of_gbo_fact,
                effect_tot_of_gbo_plan_rub,
                effect_tot_of_gbo_fact_rub,
                econ_tot_of_gbo_rub,
                qmnum,
                pl_start_date,
                pl_end_date,
                mvz_id,
                mvz_name,
                ts_type,
                ts_group,
                marka,
                load_wgt,
                model_year,
                extract(year from create_date) as commissioning_year,
                cast(driver_tabnum as varchar) as driver_tabnum,
                license_num,
                chassis_num,
                cast(equnr as varchar) as equnr,
                license_num_pri,
                gbo,
                date,
                mr_id,
                atp_id,
                retail_network,
                atp_type,
                mvz_type
            from pl_fuel_stats;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
