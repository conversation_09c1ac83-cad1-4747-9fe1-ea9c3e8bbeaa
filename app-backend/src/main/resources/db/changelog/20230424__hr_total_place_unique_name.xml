<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="203287.2" author="denis.berestinsky">
        <sql><![CDATA[
            delete from hr_places_tplaces_log where tp_id in (
                select d.id from hr_total_places d
                join hr_total_places m
                on m.name = d.name and m.ctid > d.ctid
            );

            delete from hr_total_places d
            using hr_total_places m
            where m.name = d.name
            and m.ctid > d.ctid;

            create unique index if not exists name_idx on hr_total_places(name);

            select setval('ts.hr_total_places_id_seq', (select max(id) + 1 from hr_total_places));
        ]]></sql>
    </changeSet>
</databaseChangeLog>
