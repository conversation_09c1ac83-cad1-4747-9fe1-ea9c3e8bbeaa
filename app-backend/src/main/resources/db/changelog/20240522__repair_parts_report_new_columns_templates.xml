<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1658639.2" author="anton.podoprygolov">
        <sql><![CDATA[
            delete from report_templates where report = 'PARTS' and name = 'По умолчанию' and type = 'default';
            insert into report_templates as tab(report, "user", name, type, create_time, update_time, deleted, settings)
            values ('PARTS', 'system', 'По умолчанию', 'default', current_timestamp, current_timestamp, false, '{"sort": [{"asc": 0, "name": "partsAmount", "label": "Кол-во, шт"}], "columns": [{"name": "mr", "label": "Макрорегион", "isPinned": false, "isVisible": false}, {"name": "atp", "label": "АТП", "isPinned": false, "isVisible": true}, {"name": "mvzId", "label": "Код МВЗ", "isPinned": false, "isVisible": false}, {"name": "mvzName", "label": "Название МВЗ", "isPinned": false, "isVisible": false}, {"name": "tonnage", "label": "Тоннаж", "isPinned": false, "isVisible": false}, {"name": "brand", "label": "Марка", "isPinned": false, "isVisible": false}, {"name": "model", "label": "Модель", "isPinned": false, "isVisible": false}, {"name": "year", "label": "Год ввода в экспл.", "isPinned": false, "isVisible": false}, {"name": "licenseNum", "label": "Гос. номер", "isPinned": false, "isVisible": true}, {"name": "materialTypeId", "label": "Код типа материала", "isPinned": false, "isVisible": false}, {"name": "materialTypeName", "label": "Название типа материала", "isPinned": false, "isVisible": false}, {"name": "materialGroupId", "label": "Код группы материала", "isPinned": false, "isVisible": false}, {"name": "materialGroupName", "label": "Название группы материала", "isPinned": false, "isVisible": false}, {"name": "materialParentId", "label": "Код род. материала", "isPinned": false, "isVisible": true}, {"name": "materialParentName", "label": "Название род. материала", "isPinned": false, "isVisible": true}, {"name": "materialArticle", "label": "Артикул материала", "isPinned": false, "isVisible": false}, {"name": "partCode", "label": "Код материала", "isPinned": false, "isVisible": false}, {"name": "partName", "label": "Название материала", "isPinned": false, "isVisible": true}, {"name": "materialProduce", "label": "Код производителя", "isPinned": false, "isVisible": false}, {"name": "orderId", "label": "№ заказа ТОРО", "isPinned": false, "isVisible": false}, {"name": "orderCreateDate", "label": "Дата открытия заказа ТОРО", "isPinned": false, "isVisible": false}, {"name": "vrt", "label": "Код ВРТ", "isPinned": false, "isVisible": false}, {"name": "vrtName", "label": "ВРТ", "isPinned": false, "isVisible": false}, {"name": "vrtSubtype", "label": "Подвид ВРТ", "isPinned": false, "isVisible": false}, {"name": "vrtType", "label": "Вид ВРТ", "isPinned": false, "isVisible": false}, {"name": "partsAmount", "label": "Кол-во, шт", "isPinned": false, "isVisible": true}, {"name": "partsCosts", "label": "Затраты, руб", "isPinned": false, "isVisible": false}, {"name": "partsAvgPrice", "label": "Средняя стоимость, руб", "isPinned": false, "isVisible": false}], "filters": [], "granularity": null}'::jsonb);
        ]]></sql>
    </changeSet>
</databaseChangeLog>
