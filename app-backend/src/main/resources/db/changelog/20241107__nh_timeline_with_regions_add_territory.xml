<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1978676.2" author="ayzhan.zheksembek">
        <sql><![CDATA[

            drop view ts.nh_timeline_with_regions;

            CREATE OR REPLACE VIEW ts.nh_timeline_with_regions
            AS WITH dict AS (
                     SELECT nh_plan.vehicle_id,
                        nh_plan.plan_nh,
                        nh_plan.start_date,
                        lead(nh_plan.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY nh_plan.vehicle_id ORDER BY nh_plan.start_date) AS end_date
                       FROM ts.nh_plan
                    )
             SELECT m.one_day,
                m.equnr,
                ou.repshop_id,
                ou.repshop_name,
                ou.atp_id,
                ou.atp_name,
                ou.mr_id,
                ou.mr_name,
                m.mvz_id,
                ou.mvz_name,
                ou.territory_name,
                ou.territory_id,
                COALESCE(d.plan_nh, 0::double precision) / date_part('days'::text, date_trunc('month'::text, m.one_day::timestamp with time zone) + '1 mon'::interval - '1 day'::interval) AS nh_daily_value
               FROM ts.monitoring m
                 JOIN ts.organizational_units_timeline ou on m.mvz_id = ou.mvz_id and m.one_day >= ou.start_date and m.one_day < ou.end_date
                 JOIN ts.ts_data td ON td.equnr = m.equnr
                 JOIN ts.nh_vehicles v ON v.type::text = td.ts_type::text AND v.brand::text = td.marka::text AND v.vehicle_create_year::double precision = date_part('YEAR'::text, td.create_date) AND v.vehicle_tonnage = td.load_wgt
                 LEFT JOIN dict d ON v.id = d.vehicle_id AND m.one_day >= d.start_date AND m.one_day < d.end_date
              WHERE (m.mvz_type::text = ANY (ARRAY['Основной'::character varying::text, 'КДК'::character varying::text])) AND (m.ts_group::text = ANY (ARRAY['Основное'::character varying::text, 'Полуприцеп'::character varying::text, 'Прицеп'::character varying::text]));

        ]]></sql>
    </changeSet>
</databaseChangeLog>
