<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1844746.2" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists repair_resources_view;
            create or replace view repair_resources_view as
            SELECT repair_resources.order_id,
                   repair_resources.repair_start_date,
                   repair_resources.equnr,
                   repair_resources.vehicle_mvz_id,
                   repair_resources.vehicle_mvz_name,
                   repair_resources.vehicle_mvz_type,
                   repair_resources.vehicle_atp_id,
                   repair_resources.vehicle_atp,
                   repair_resources.vehicle_atp_type,
                   repair_resources.vehicle_retail_network,
                   repair_resources.vehicle_mr_id,
                   repair_resources.vehicle_mr,
                   repair_resources.vrt,
                   repair_resources.vrt_name,
                   repair_resources.repair_subtype_id,
                   repair_resources.repair_subtype_name,
                   repair_resources.repair_type_id,
                   repair_resources.repair_type_name,
                   repair_resources.ts_load_wgt,
                   repair_resources.ts_marka,
                   repair_resources.ts_model,
                   repair_resources.ts_create_date,
                   repair_resources.ts_license_num,
                   repair_resources.material_type_id,
                   repair_resources.material_type_name,
                   repair_resources.material_group_id,
                   repair_resources.material_group_name,
                   repair_resources.material_parent_id,
                   repair_resources.material_parent_name,
                   repair_resources.material_article,
                   repair_resources.material_produce,
                   repair_resources.ts_type,
                   repair_resources.ts_group,
                   repair_resources.ts_gbo,
                   repair_resources.material_id,
                   repair_resources.material_name,
                   repair_resources.gl_account,
                   repair_resources.material_amount_plan,
                   repair_resources.material_amount_fact,
                   repair_resources.material_amount,
                   repair_resources.material_expenses,
                   repair_resources.material_price,
                   date_part('year'::text, repair_resources.ts_create_date) AS vehicle_year,
                   repair_resources.repair_kind
            FROM repair_resources;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
