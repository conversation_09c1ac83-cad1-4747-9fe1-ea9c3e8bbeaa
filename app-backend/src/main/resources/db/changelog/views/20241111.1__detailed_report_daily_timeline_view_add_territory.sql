--liquibase formatted sql

--changeset denis.berestinsky:1954251.2
drop view if exists ts.detailed_report_daily_timeline;

CREATE OR REPLACE VIEW ts.detailed_report_daily_timeline
AS
WITH days AS (
    SELECT generate_series('2018-01-01'::date::timestamp with time zone, now() + '3 years'::interval, '1 day'::interval)::date AS day
)
SELECT vrt.equnr,
       days.day,
       vrt.mvz_id,
       org.atp_id,
       org.mr_id,
       org.mvz_type,
       org.mr_name,
       org.atp_name,
       org.atp_type,
       org.retail_network,
       org.mvz_name,
       org.territory_id,
       org.territory_name,
       vrt.is_inactive,
       vrt.start_date,
       vrt.end_date,
       t.ts_group,
       date_part('year'::text, td.create_date) AS commissioning_year,
       td.marka,
       td.model,
       td.baujj AS model_year,
       td.load_wgt,
       td.license_num,
       td.fleet_num,
       td.gbo,
       dm.readg_open,
       td.ts_type,
       vrt.td_no_compart,
       td.create_date as commissioning_date
FROM days
         JOIN ts.vehicle_region_timeline vrt ON vrt.start_date <= days.day AND days.day < vrt.end_date
         JOIN ts.organizational_units_timeline org ON vrt.mvz_id::text = org.mvz_id::text AND org.start_date <= days.day AND days.day < org.end_date
         JOIN ts.ts_data td ON vrt.equnr = td.equnr
         JOIN ts.types t ON td.ts_type::text = t.ts_type::text
         LEFT JOIN ts.daily_mileage dm ON dm.equnr = vrt.equnr AND days.day = dm.mileage_date
WHERE NOT vrt.is_inactive;
