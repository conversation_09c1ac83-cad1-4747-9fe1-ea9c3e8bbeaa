<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1780339.1" author="anton.podoprygolov">
        <sql><![CDATA[
            create table if not exists user_email_subscriptions
            (
                email               VARCHAR not null,
                subject_id          VARCHAR not null,
                subscription_date   date    not null,
                unsubscription_date date,
                created_by          varchar not null,
                changed_by          VARCHAR
            );
            ]]></sql>
    </changeSet>
</databaseChangeLog>