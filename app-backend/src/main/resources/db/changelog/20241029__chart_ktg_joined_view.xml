<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1912871.1" author="ayzhan.zheksembek">
        <sql><![CDATA[
            drop view if exists ts.chart_ktg_joined_view;

            CREATE OR REPLACE VIEW ts.chart_ktg_joined_view
            AS SELECT k.vehicle_date,
                COALESCE(k.equnr::text, 'N/A'::character varying::text) AS equnr,
                COALESCE(k.mvz_id, 'N/A'::character varying) AS mvz_id,
                COALESCE(k.mvz_name, 'N/A'::character varying) AS mvz_name,
                o.atp_id,
                COALESCE(o.atp_name, 'N/A'::character varying) AS atp_name,
                o.territory_id,
                COALESCE(o.territory_name, 'N/A'::character varying) AS territory_name,
                o.mr_id,
                COALESCE(o.mr_name, 'N/A'::character varying) AS mr_name,
                COALESCE(o.atp_type, 'N/A'::character varying) AS atp_type,
                COALESCE(o.retail_network, 'N/A'::character varying) AS retail_network,
                COALESCE(o.mvz_type, 'N/A'::character varying) AS mvz_type,
                o.repshop_id,
                COALESCE(o.repshop_name, 'N/A'::character varying) AS repshop_name,
                COALESCE(k.vehicle_group, 'N/A'::character varying) AS vehicle_group,
                COALESCE(k.vehicle_type, 'N/A'::character varying) AS vehicle_type,
                COALESCE(k.vehicle_brand, 'N/A'::character varying) AS vehicle_brand,
                COALESCE(k.vehicle_model, 'N/A'::character varying) AS vehicle_model,
                k.vehicle_create_year,
                COALESCE(k.vehicle_tonnage, 0.0) AS vehicle_tonnage,
                    CASE
                        WHEN k.vehicle_gbo THEN 'С ГБО'::text
                        ELSE 'Без ГБО'::text
                    END AS vehicle_gbo,
                COALESCE(k.vehicle_license, 'N/A'::character varying) AS vehicle_license,
                coalesce(date_part('year'::text, k.vehicle_date)::text, 'N/A') AS date_year,
                COALESCE(to_char(k.vehicle_date::timestamp with time zone, '"Кв "Q.YYYY'::text), 'N/A'::character varying::text) AS date_quarter,
                COALESCE(to_char(k.vehicle_date::timestamp with time zone, 'MM.YYYY'::text), 'N/A'::character varying::text) AS date_month,
                COALESCE(to_char(k.vehicle_date::timestamp with time zone, '"Н "IW.IYYY'::text), 'N/A'::character varying::text) AS date_week,
                COALESCE(to_char((k.vehicle_date + 3)::timestamp with time zone, '"ОН "IW.IYYY'::text), 'N/A'::character varying::text) AS date_report_week,
                COALESCE(to_char(k.vehicle_date::timestamp with time zone, 'DD.MM.YYYY'::text), 'N/A'::character varying::text) AS date_day,
                COALESCE(k.vehicle_status, '0'::character varying) AS ktg_status_id,
                COALESCE(trs.status, 'Не подано в ТГ'::character varying) AS ktg_status,
                    CASE
                        WHEN k.vehicle_status::text = '5C'::text THEN k.vehicle_reason
                        WHEN k.vehicle_status::text = '5B'::text THEN '-1'::integer
                        ELSE '-2'::integer
                    END AS ktg_reason_id,
                    CASE
                        WHEN k.vehicle_status::text = '5C'::text AND k.vehicle_reason = 2 AND (trh.end_date - trh.start_date) >= 7 THEN 'Долгострой'::character varying
                        WHEN k.vehicle_status::text = '5C'::text THEN trr.reason
                        WHEN k.vehicle_status::text = '5B'::text THEN trs.status
                        ELSE 'Не подано в ТГ'::character varying
                    END AS ktg_reason,
                COALESCE(k.rc_submit_id, 'Не указано'::character varying) AS rc_submit_code,
                COALESCE(k.rc_submit_name, 'Не указано'::character varying) AS rc_submit_name,
                    CASE
                        WHEN k.ktg THEN 1
                        ELSE 0
                    END AS ktg_counter,
                    CASE
                        WHEN k.rg THEN 1
                        ELSE 0
                    END AS rg_counter
               FROM ts.kip k
                 LEFT JOIN ts.organizational_units_timeline o ON k.vehicle_date >= o.start_date AND k.vehicle_date < o.end_date AND k.mvz_id::text = o.mvz_id::text
                 LEFT JOIN ts.ts_ready_statuses trs ON k.vehicle_status::text = trs.id::text
                 LEFT JOIN ts.ts_ready_reasons trr ON k.vehicle_reason = trr.id
                 LEFT JOIN ts.ts_ready_hist trh ON k.vehicle_date >= trh.start_date AND k.vehicle_date < trh.end_date AND k.equnr = trh.equnr;

            ]]></sql>
    </changeSet>
</databaseChangeLog>