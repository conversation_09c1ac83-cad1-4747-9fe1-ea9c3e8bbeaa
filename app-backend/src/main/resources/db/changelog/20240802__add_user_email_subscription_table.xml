<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1755922.2" author="igor.belogolovskiy; anton.podoprygolov">
        <sql><![CDATA[
            drop table if exists user_email_subscription;
            drop table if exists user_email_subscription_subject;

            create table if not exists user_email_subscription_subject
            (
                id           varchar(255) primary key not null,
                subject      varchar(1000)            not null,
                order_number int
                );

            comment on table user_email_subscription_subject is 'Список тем для рассылки';
            comment on column user_email_subscription_subject.id is 'Id темы рассылки';
            comment on column user_email_subscription_subject.subject is 'Тема рассылки';
            comment on column user_email_subscription_subject.order_number is 'Порядковый номер в структуре письма';

            create table if not exists user_email_subscription
            (
                id                  bigserial primary key not null,
                email               varchar(255)          not null,
                subject_id          varchar(255)
                constraint user_email_subscription_subject_id_fk references user_email_subscription_subject (id),
                subscription_date   timestamp             not null,
                unsubscription_date timestamp,
                created_by          varchar(255)          not null,
                changed_by          varchar(255)          not null
                );


            comment on table user_email_subscription is 'Данные о подписках пользователей';
            comment on column user_email_subscription.id is 'Id подписки';
            comment on column user_email_subscription.email is 'Email пользователя';
            comment on column user_email_subscription.subject_id is 'Id темы рассылки';
            comment on column user_email_subscription.subscription_date is 'Дата подписки';
            comment on column user_email_subscription.unsubscription_date is 'Дата отписки';
            comment on column user_email_subscription.created_by is 'Автор создания записи';
            comment on column user_email_subscription.changed_by is 'Автор последнего изменения';
            ]]></sql>
    </changeSet>
    <changeSet id="1927633.1" author="denis.berestinsky">
        <sql><![CDATA[
            alter table user_email_subscription drop constraint user_email_subscription_pkey;
            alter table user_email_subscription drop column id;
            delete from user_email_subscription t using (
                select max(ctid) as ctid, email, subject_id
                from user_email_subscription
                group by email, subject_id having count(*) > 1
            ) dup
            where t.email = dup.email and t.subject_id = dup.subject_id and t.ctid <> dup.ctid;
            alter table user_email_subscription add constraint user_email_subscription_pkey primary key(email, subject_id);
        ]]></sql>
    </changeSet>
</databaseChangeLog>
