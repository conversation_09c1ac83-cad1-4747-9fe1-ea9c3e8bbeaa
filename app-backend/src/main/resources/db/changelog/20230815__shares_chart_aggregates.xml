<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="572074_1.1" author="anton.podoprygolov">
        <sql><![CDATA[
            alter table ts.dict_aggregates
                add shares boolean;
        ]]></sql>
    </changeSet>
    <changeSet id="572074_2.1" author="anton.podoprygolov">
        <sql><![CDATA[
            update dict_aggregates
            set shares = false
            where sys_name in ('order_rub_km', 'order_rub_ts', 'order_repair_count_ts');
        ]]></sql>
    </changeSet>
    <changeSet id="572074_3.1" author="anton.podoprygolov">
        <sql><![CDATA[
            update dict_aggregates
            set shares = true
            where sys_name not in ('order_rub_km', 'order_rub_ts', 'order_repair_count_ts');
        ]]></sql>
    </changeSet>
</databaseChangeLog>
