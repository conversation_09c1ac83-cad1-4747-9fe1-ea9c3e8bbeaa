<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1513137.1" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists atp_hr_places_view;
            create or replace view atp_hr_places_view as
            with
                raw_data as (
                    select
                        hapl.id as id,
                        al.atp_id as atp_id,
                        atp.name as atp_name,
                        hp.id as place_id,
                        hp.name as place_name,
                        hapl.start_date as start_date,
                        lead(hapl.start_date, 1, '9999-12-31'::date) over (partition by hapl.atp_id order by hapl.start_date) as end_date,
                        hapl.deleted as deleted,
                        hapl.updated_by as updated_by,
                        hapl.updated_at as updated_at
                    from
                        atp_log_view al
                        left join hr_atp_places_log hapl on hapl.atp_id = al.atp_id and hapl.deleted != true
                        join atp on al.atp_id = atp.id
                        left join hr_places hp on hapl.hrp_id = hp.id
                    where
                        al.start_date <= current_date
                        and (al.end_date is null or al.end_date >= current_date)
                ),
                unmapped as (
                    select
                        *,
                        1 as ord
                    from raw_data where id is null
                ),
                mapped as (
                    select
                        *,
                        2 as ord
                    from raw_data where id is not null
                ),
                result as (
                    select *
                    from (
                        (select * from unmapped)
                        union
                        (select * from mapped)
                    ) as concatenated
                )
            select row_number() over () as ctid, * from result;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
