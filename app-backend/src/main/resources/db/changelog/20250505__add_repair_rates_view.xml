<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="2274745.4" author="ayzhan.zheksembek">
        <sql><![CDATA[
drop materialized view if exists pls_by_day_with_rate;
drop view if exists ts.repair_rates_view;
CREATE OR REPLACE VIEW ts.repair_rates_view
AS
WITH rr_dictionary_main AS (
         SELECT rr_dictionary_1.atp_id,
            rr_dictionary_1.toro_work_id,
            rr_dictionary_1.tonnage,
            rr_dictionary_1.start_date,
            rr_dictionary_1.end_date,
            structures_1.structure_name,
            rr_dictionary_1.rate * (COALESCE(rsd.rate, 0::numeric) / 100::numeric)::double precision AS rate
           FROM ts.rr_dictionary rr_dictionary_1
             CROSS JOIN ( VALUES ('ourWorkshopServices'::text), ('extWorkshopServices'::text), ('parts'::text), ('tires'::text)) structures_1(structure_name)
             LEFT JOIN ts.rr_structure_dictionary rsd ON rsd.year = rr_dictionary_1.year AND rsd.month = rr_dictionary_1.month AND rsd.atp_id = rr_dictionary_1.atp_id AND rsd.structure_type::text = structures_1.structure_name
        )
            SELECT rr_dictionary_1.start_date,
                   rr_dictionary_1.end_date,
                   rr_dictionary_1.atp_id,
                   rr_dictionary_1.toro_work_id,
                   COALESCE(tw.name, 'N/A'::character varying) AS toro_works,
                   COALESCE(twt.name, 'N/A'::character varying) AS toro_type_name,
                   COALESCE(tws.name, 'N/A'::character varying) AS toro_subtype_name,
                   rr_dictionary_1.tonnage,
                   CASE
                       WHEN rr_dictionary_1.structure_name = 'ourWorkshopServices'::text THEN 'Ремзона'::text
                       WHEN rr_dictionary_1.structure_name = 'extWorkshopServices'::text THEN 'СТО'::text
                       WHEN rr_dictionary_1.structure_name = 'parts'::text THEN 'Запчасти'::text
                       WHEN rr_dictionary_1.structure_name = 'tires'::text THEN 'Шины'::text
                       ELSE NULL::text
                       END AS structure_name,
                   rr_dictionary_1.rate
            FROM rr_dictionary_main rr_dictionary_1
                     LEFT JOIN ts.toro_works tw ON rr_dictionary_1.toro_work_id::text = tw.id::text
             LEFT JOIN ts.toro_works_types twt ON tw.subtype_id = twt.id
                LEFT JOIN ts.toro_works_subtypes tws ON tw.subtype_id = tws.id;

            create materialized view pls_by_day_with_rate as
WITH mvz_log AS (SELECT mvz_log_1.uid                                                                                 AS mvz_id,
                        mvz_log_1.atp_id,
                        mvz_log_1.start_date,
                        lead(mvz_log_1.start_date, 1)
                        OVER (PARTITION BY mvz_log_1.uid ORDER BY mvz_log_1.start_date)                               AS end_date
                 FROM ts.mvz_log mvz_log_1)
            SELECT pbd.qmnum,
                   pbd.vehicle_date,
                   pbd.mvz_id,
                   pbd.mvz_name,
                   pbd.vehicle_license,
                   pbd.marka,
                   pbd.model,
                   pbd.ts_type,
                   pbd.ts_group,
                   pbd.gbo,
                   pbd.gbo_text,
                   pbd.load_wgt,
                   pbd.equnr,
                   pbd.year,
                   pbd.create_date,
                   pbd.fleet_num,
                   pbd.no_compart,
                   pbd.pl_tonnage,
                   pbd.bequi,
                   pbd.trailer_license_num,
                   pbd.trailer_load_wgt,
                   pbd.start_timestamp,
                   pbd.end_timestamp,
                   pbd.usage_time,
                   pbd.avg_speed,
                   pbd.mileage,
                   pbd.model_flag,
                   mvz_log.atp_id,
                   rrv.toro_work_id,
                   rrv.toro_works,
                   rrv.toro_type_name,
                   rrv.toro_subtype_name,
                   rrv.structure_name,
                   rrv.rate
            FROM pls_by_day pbd
                     LEFT JOIN mvz_log ON pbd.mvz_id::text = mvz_log.mvz_id::text AND pbd.vehicle_date >= mvz_log.start_date AND
                              pbd.vehicle_date < mvz_log.end_date
         LEFT JOIN repair_rates_view rrv ON mvz_log.atp_id = rrv.atp_id AND pbd.vehicle_date >= rrv.start_date AND
                pbd.vehicle_date < rrv.end_date AND pbd.pl_tonnage = rrv.tonnage;

            alter materialized view pls_by_day_with_rate owner to stv_bd_avto;

            create index pls_by_day_with_rate_idx
                on pls_by_day_with_rate (mvz_id, vehicle_date, pl_tonnage);
            ]]></sql>
    </changeSet>
</databaseChangeLog>
