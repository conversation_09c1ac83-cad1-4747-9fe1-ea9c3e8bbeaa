<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="383586.1" author="denis.berestinsky">
        <sql><![CDATA[
            insert into dict_aggregates(name, sys_name, ordering)
            values
                ('Руб./км', 'order_rub_km', 9),
                ('Руб./ТС', 'order_rub_ts', 10),
                ('Кол-во ремонтов/ТС', 'order_repair_count_ts', 11)
            on conflict do nothing;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
