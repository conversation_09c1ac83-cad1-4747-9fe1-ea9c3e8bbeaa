--liquibase formatted sql

--changeset <EMAIL>:1962060.0
alter table mvz_log
    drop column if exists name;

alter table mvz_log
    drop column if exists type;

alter table mvz_log
    drop column if exists id_rep;

alter table mvz_log
    drop column if exists name_rep;

alter table mvz_log
    drop column if exists ut;

alter table mvz_log
    drop column if exists end_date;

alter table mvz_log
    drop column if exists is_deleted;

alter table mvz_log
    drop column if exists author;

alter table mvz_log
    drop column if exists updateat;

alter table atp
    drop column if exists repair_name;

alter table atp_log
    drop column if exists type;

alter table atp_log
    drop column if exists end_date;

alter table atp_log
    drop column if exists is_deleted;

alter table atp_log
    drop column if exists author;

alter table atp_log
    drop column if exists updateat;

alter table atp_log
    drop column if exists retail_network;

alter table macro_region
    drop column if exists author;

alter table macro_region
    drop column if exists update_at;
