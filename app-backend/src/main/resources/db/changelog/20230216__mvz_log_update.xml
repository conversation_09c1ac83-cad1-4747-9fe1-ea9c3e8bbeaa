<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="79020.1" author="denis.berestinsky">
        <sql><![CDATA[
            create or replace view mvz_log_view(
                uid,
                name,
                type,
                id_rep,
                name_rep,
                ut,
                atp_id,
                start_date,
                end_date,
                author,
                update_at
            ) as select
                uid,
                name,
                type,
                id_rep,
                name_rep,
                ut,
                atp_id,
                start_date,
                lead(start_date - 1) over (partition by uid order by start_date),
                author,
                updateat
            from mvz_log
            where is_deleted = false;

            create or replace view atp_log_view(
                atp_id,
                mr_id,
                type,
                start_date,
                end_date,
                author,
                update_at,
                retail_network
            ) as select
                atp_id,
                mr_id,
                type,
                start_date,
                lead(start_date - 1) over (partition by atp_id order by start_date),
                author,
                updateat,
                retail_network
            from atp_log
            where is_deleted is null or is_deleted = false;

            create or replace view kip_dictionary_view(
                mvz_id,
                mvz_name,
                atp_id,
                macro_region_id,
                tonnage,
                start_date,
                end_date,
                percentage,
                author
            ) as (
                select
                    kdm.mvz_id,
                    ml.name,
                    ml.atp_id,
                    al.mr_id,
                    kdm.tonnage,
                    kdm.start_date,
                    lead(kdm.start_date - 1) over (partition by kdm.mvz_id, kdm.tonnage order by kdm.start_date),
                    kdm.percentage,
                    kdm.author
                from kip_dictionary_by_mvz kdm
                join mvz_log_view ml on
                    ml.uid = kdm.mvz_id
                    and ml.start_date <= kdm.start_date
                    and (ml.end_date is null or ml.end_date >= kdm.start_date)
                join atp_log_view al on
                    al.atp_id = ml.atp_id
                    and al.start_date <= kdm.start_date
                    and (al.end_date is null or al.end_date >= kdm.start_date)
            ) union all (
                select
                    '',
                    null,
                    kda.atp_id,
                    al.mr_id,
                    kda.tonnage,
                    kda.start_date,
                    lead(kda.start_date - 1) over (partition by kda.atp_id, kda.tonnage order by kda.start_date),
                    kda.percentage,
                    kda.author
                from kip_dictionary_by_atp kda
                join atp_log_view al on
                    al.atp_id = kda.atp_id
                    and al.start_date <= kda.start_date
                    and (al.end_date is null or al.end_date >= kda.start_date)
            );

            drop view if exists kip_dictionary_groups_view;

            delete from mvz_log d
            using mvz_log m
            where m.uid = d.uid
            and m.start_date = d.start_date
            and m.ctid > d.ctid;

            create unique index if not exists uid_date_idx on mvz_log(uid, start_date);

            delete from atp_log d
            using atp_log a
            where a.atp_id = d.atp_id
            and a.start_date = d.start_date
            and a.ctid > d.ctid;

            create unique index if not exists atp_date_idx on atp_log(atp_id, start_date);
        ]]></sql>
    </changeSet>
</databaseChangeLog>
