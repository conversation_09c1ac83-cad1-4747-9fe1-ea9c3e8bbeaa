<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="572091.1" author="igor.belogolovsky">
        <sql><![CDATA[
            drop view if exists airflow_monitoring.airflow_update_status_view;
            create or replace view airflow_monitoring.airflow_update_status_view as
            select full_table_name, table_group, max(update_at) update_at
            from airflow_monitoring.all_tables_info ati
                     join airflow_monitoring.all_tables_update_log atul on ati.full_table_name = atul.table_name
            where is_updated_by_airflow
            group by full_table_name, table_group;
        ]]></sql>


    </changeSet>
</databaseChangeLog>