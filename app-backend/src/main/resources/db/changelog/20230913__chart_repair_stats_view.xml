<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="701734_1.2" author="anton.podoprygolov">
        <sql><![CDATA[
            create or replace view chart_repair_stats_view as
            select
                mr_id,
                atp_id,
                mvz_id,
                vehicle_date,
                mvz_name,
                atp_name,
                mr_name,
                ts_license_num,
                ts_marka,
                ts_model,
                ts_type,
                ts_group,
                ts_gbo,
                ts_load_wgt,
                extract(year from ts_create_date) as ts_create_year,
                mileage,
                retail_network,
                atp_type,
                mvz_type,
                extract(year from vehicle_date) as vehicle_date_year,
                extract(month from vehicle_date) as vehicle_date_month,
                extract(week from vehicle_date) as vehicle_date_week,
                extract(MONTH from vehicle_date) || '.' ||
                  extract(YEAR from vehicle_date) as month_year,
                ('Н ' || extract(WEEK from vehicle_date) || '.' || (case
                   when (extract(MONTH from vehicle_date) = 1 and
                         extract(WEEK from vehicle_date) in
                         (52, 53))
                       then extract(YEAR from vehicle_date) - 1
                   when (extract(MONTH from vehicle_date) = 12 and
                         extract(WEEK from vehicle_date) = 1)
                       then extract(YEAR from vehicle_date) + 1
                   else extract(YEAR from vehicle_date) end)) as week_year
            from repair_stats;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
