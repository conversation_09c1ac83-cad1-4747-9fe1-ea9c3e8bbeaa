<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="2141647.2" author="denis.berestinsky">
        <sql endDelimiter=";--"><![CDATA[
            create or replace function pg_temp.remove_column(idd bigint, col varchar) returns void language plpgsql as $$
            declare
                sett jsonb;
                cols jsonb;
                i int;
                ind integer;
                fnd boolean;
            begin
                select settings into sett from report_templates where id = idd;
                fnd := false;
                for i in 0..(jsonb_array_length(sett->'columns') - 1) loop
                    if sett->'columns'->i->>'name' = col then
                        ind := i;
                        fnd := true;
                    end if;
                end loop;
                if fnd then
                    sett := jsonb_set(sett, '{columns}', (sett->'columns') - ind);
                    update report_templates set settings = sett where id = idd;
                end if;
            end;
            $$;--

            create or replace function pg_temp.remove_column_for(rep varchar, col varchar) returns void language plpgsql as $$
            declare
                r record;
            begin
                for r in select id from report_templates where report = rep loop
                    perform pg_temp.remove_column(r.id, col);
                end loop;
            end;
            $$;--

            do $$
            begin
                perform pg_temp.remove_column_for('REPAIR', 'invoiceDate');
                perform pg_temp.remove_column_for('REPAIR', 'invoiceId');
                perform pg_temp.remove_column_for('REPAIR', 'payoutDate');
            end
            $$;--
        ]]></sql>
    </changeSet>
</databaseChangeLog>
