<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="383586.1" author="igor.belogolovsky">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="repair_base_properties"/>
            </not>
        </preConditions>
        <createTable tableName="repair_base_properties">
            <column name="id" type="bigserial">
                <constraints primaryKey="true" primaryKeyName="rapair_base_properties_pk"/>
            </column>
            <column name="vrt_uid" type="varchar(20)">
                <constraints foreignKeyName="repair_base_properties_vrt_fk" referencedTableName="mvz_codes" referencedColumnNames="uid"/>
            </column>
            <column name="property" type="varchar(50)"/>
            <column name="year" type="integer"/>
            <column name="month" type="varchar(20)"/>
            <column name="value" type="bigint"/>
            <column name="created_at" type="timestamptz"/>
            <column name="created_by" type="varchar(100)"/>
            <column name="updated_at" type="timestamptz"/>
            <column name="updated_by" type="varchar(100)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>