<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1713698.2" author="ayzhan.zheksembek">
        <sql><![CDATA[
            drop view if exists repair_org_unit_timeline_joined_view;
            create or replace view repair_org_unit_timeline_joined_view as (
                select
                    repair.repair_start_date,
                    repair.order_id,
                    repair.repair_expenses_full,
                    repair.order_sys_stat,
                    repair.order_ut,
                    repair.order_mvz,
                    repair.vehicle_mvz,
                    repair.vehicle_repshop_id,
                    repair.vehicle_repshop_name,
                    repair.order_repshop_id,
                    repair.order_repshop_name,
                    repair.req_id,
                    repair.req_type,
                    repair.req_user_stat,
                    repair.req_sys_stat,
                    repair.repair_kind,
                    repair.order_text,
                    repair.our_workshop,
                    repair.vrt,
                    repair.vrt_name,
                    repair.event_id,
                    repair.event_text,
                    repair.equnr,
                    repair.vehicle_bu,
                    repair.main_mechanic_id,
                    repair.main_mechanic_mvz_id,
                    repair.main_mechanic_mvz_name,
                    repair.main_mechanic_mvz_type,
                    repair.main_mechanic_atp_id,
                    repair.main_mechanic_atp_name,
                    repair.main_mechanic_repshop_id,
                    repair.main_mechanic_repshop_name,
                    repair.invoice_number,
                    repair.creditor_number,
                    repair.creditor_name,
                    repair.resources_creditor_number,
                    repair.services_creditor_number,
                    repair.mileage,
                    repair.repair_expenses,
                    repair.repair_expenses_plan,
                    repair.repair_expenses_fact,
                    repair.invoice_date,
                    repair.payout_date,
                    repair.repair_end_date,
                    repair.repair_plan_end_date,
                    repair.deblock_date,
                    repair.repair_subtype_id,
                    repair.repair_subtype_name,
                    repair.repair_subtype_color,
                    repair.repair_type_id,
                    repair.repair_type_name,
                    repair.ts_license_num,
                    repair.ts_marka,
                    repair.ts_model,
                    repair.ts_type,
                    repair.ts_group,
                    repair.ts_gbo,
                    repair.ts_load_wgt,
                    repair.ts_create_date,
                    repair.vehicle_year,
                    repair.vin,
                    repair.part_amount,
                    repair.part_expenses,
                    repair.services_amount,
                    repair.services_expenses,
                    repair.services_avg_price,
                    repair.mechanic_id,
                    repair.services_atp_id,
                    repair.services_atp_name,
                    repair.services_repshop_id,
                    repair.services_repshop_name,
                    repair.services_mvz_id,
                    repair.final_service_mvz_id,
                    repair.final_repshop_id,
                    repair.final_repshop_name,
                    repair.unique_services_repshop_flag,
                    repair.unique_mechanic_id_flag,
                    ou_timeline.mvz_id,
                    ou_timeline.mvz_name,
                    ou_timeline.mvz_type,
                    ou_timeline.atp_id,
                    ou_timeline.atp_name,
                    ou_timeline.atp_type,
                    ou_timeline.retail_network,
                    ou_timeline.mr_id,
                    ou_timeline.mr_name,
                    ou_timeline.repshop_id,
                    ou_timeline.repshop_name,
                    ou_timeline.hr_place_id ,
                    ou_timeline.hr_place_name,
                    ou_timeline.hr_tplace_id,
                    ou_timeline.hr_tplace_name
                from
                    ts.repair
                join ts.organizational_units_timeline ou_timeline
                   on
                    ou_timeline.mvz_id = repair.vehicle_mvz_id
                    and repair.repair_start_date >= ou_timeline.start_date
                    and repair.repair_start_date < ou_timeline.end_date
                ) ;
            ]]></sql>
    </changeSet>
</databaseChangeLog>
