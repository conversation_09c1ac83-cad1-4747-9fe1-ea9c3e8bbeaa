<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="2" author="igor.belogolovsky">
        <sql>
            <![CDATA[
            drop materialized view if exists monitoring;
            CREATE
            MATERIALIZED VIEW monitoring AS
            WITH geo AS (
            SELECT mvz_log.uid AS mvp_id,
            atp.name AS atp_name,
            mvz_log.name AS mvp_name
            FROM atp_log
            JOIN macro_region mr ON atp_log.mr_id = mr.id
            JOIN atp ON atp_log.atp_id = atp.id
            JOIN mvz_log ON atp.id = mvz_log.atp_id
            WHERE atp_log.start_date < current_timestamp
                                       AND mvz_log.start_date < current_timestamp
            )
            , mx AS (SELECT max(coalesce(change_date, start_date)) dt FROM ts_mvz_hist)
            SELECT cast(one_day as date)                        AS one_day
                 , coalesce(types.ts_group, 'Тип ТС не указан') AS ts_group
                 , ts_dt.ts_type
                 , geo.atp_name
                 , geo.mvp_id
                 , geo.mvp_name
                 , ts_dt.chassis_num
                 , ts_dt.marka
                 , ts_dt.model
                 , ts_dt.baujj
                 , ts_dt.load_wgt
                 , ts_dt.license_num
                 , ts_dt.oborud
                 , cast(ts_dt.gbo as boolean)                   AS gbo
                 , ts_dt.fleet_num
                 , hist.mvz
                 , hist.equnr
            FROM ts_mvz_hist AS hist
                     CROSS JOIN generate_series('2011-01-01', (select mx.dt from mx), interval '1' day) AS one_day
                     INNER JOIN ts_data AS ts_dt ON hist.equnr = ts_dt.equnr
                     LEFT JOIN geo ON geo.mvp_id = hist.mvz
                     LEFT JOIN types ON ts_dt.ts_type = types.ts_type
            WHERE one_day BETWEEN start_date AND end_date
              AND ts_dt.system_stat != 'Метка удаления';

            CREATE INDEX be_monitoring_idx_day ON monitoring (one_day);
            CREATE INDEX be_monitoring_idx_mvz ON monitoring (mvz);
            CREATE INDEX be_monitoring_idx_mvz_day ON monitoring (mvz, one_day);
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>