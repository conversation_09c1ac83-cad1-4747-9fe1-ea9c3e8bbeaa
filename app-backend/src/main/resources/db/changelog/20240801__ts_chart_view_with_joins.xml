<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1683877_2.1" author="anton.podoprygolov">
        <sql><![CDATA[
            create or replace view chart_ts_number_joined_view as
            SELECT vrt.ctid                                    AS id,
                   vrt.equnr,
                   vrt.start_date                              AS vrt_start_date,
                   vrt.end_date                                AS vrt_end_date,
                   vrt.inactive_date                           AS vrt_inactive_date,
                   o.mr_id                                     AS vrt_mr_id,
                   o.mr_name                                   AS mr_name,
                   o.atp_id                                    AS vrt_atp_id,
                   o.atp_type                                  AS vrt_atp_type,
                   o.atp_name                                  AS atp_name,
                   o.mvz_id                                    AS vrt_mvz_id,
                   o.retail_network                            AS vrt_retail_network,
                   o.mvz_name                                  AS vrt_mvz_name,
                   o.mvz_type                                  AS vrt_mvz_type,
                   o.repshop_id                                AS repshop_id,
                   o.repshop_name                              AS repshop_name,
                   date_part('year'::text, td.create_date)     AS td_create_date_year,
                   td.inactive_day                             AS td_inactive_day,
                   td.deleted_day                              AS td_deleted_day,
                   td.truck_type                               AS td_truck_type,
                   td.ts_type                                  AS td_ts_type,
                   td.marka                                    AS td_marka,
                   td.model                                    AS td_model,
                   td.load_wgt                                 AS td_load_wgt,
                   CASE
                       WHEN td.gbo THEN 'С ГБО'::text
                       ELSE 'Без ГБО'::text
                       END                                     AS td_gbo,
                   td.license_num                              AS td_license_num,
                   td.create_date                              AS td_create_date,
                   date_part('year'::text, vrt.start_date)     AS vrt_start_date_year,
                   date_part('month'::text, vrt.start_date)    AS vrt_start_date_month,
                   date_part('week'::text, vrt.start_date)     AS vrt_start_date_week,
                   to_char(vrt.start_date, '"Кв "Q.YYYY')      AS quarter_year,
                   to_char(vrt.start_date, 'MM.YYYY')          AS month_year,
                   to_char(vrt.start_date, '"Н "IW.IYYY')      AS week_year,
                   to_char(vrt.start_date + 3, '"ОН "IW.IYYY') AS report_week_year,
                   to_char(vrt.start_date, 'DD.MM.YYYY')       AS day_month_year
            FROM vehicle_region_timeline vrt
                     JOIN ts_data td ON vrt.equnr = td.equnr
                     LEFT JOIN ts.organizational_units_timeline o ON vrt.start_date >= o.start_date
                AND vrt.start_date < o.end_date
                AND vrt.mvz_id = o.mvz_id
            WHERE td.ts_type IS NOT NULL;
            ]]></sql>
    </changeSet>
</databaseChangeLog>