<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1" author="nsmetanin">
        <sql>
            Insert into "load_wgt" ("id","load_wgt","name") values (0,0.0,'Грузоподъемность 0т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (1,1.5,'Грузоподъемность до 1.5т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (2,1.5,'Грузоподъемность до 1.5т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (3,1.5,'Грузоподъемность до 1.5т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (4,1.5,'Грузоподъемность до 1.5т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (5,1.5,'Грузоподъемность до 1.5т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (6,1.5,'Грузоподъемность до 1.5т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (7,1.5,'Грузоподъемность до 1.5т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (8,3.0,'Грузоподъемность до 3т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (9,3.0,'Грузоподъемность до 3т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (10,4.0,'Грузоподъемность до 4т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (11,5.0,'Грузоподъемность до 5т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (12,7.0,'Грузоподъемность до 7т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (13,7.0,'Грузоподъемность до 7т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (14,9.0,'Грузоподъемность до 9т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (15,10.0,'Грузоподъемность до 10т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (16,11.0,'Грузоподъемность до 11т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (17,14.0,'Грузоподъемность до 14т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (18,15.0,'Грузоподъемность до 15т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (19,15.0,'Грузоподъемность до 15т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (20,17.0,'Грузоподъемность до 17т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (21,20.0,'Грузоподъемность до 20т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (22,21.0,'Грузоподъемность до 21т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (23,22.0,'Грузоподъемность до 22т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (24,26.0,'Грузоподъемность до 26т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (25,37.0,'Грузоподъемность до 37т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (26,2.0,'Грузоподъемность до 2т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (27,19.0,'Грузоподъемность до 19т') on conflict do nothing;
            Insert into "load_wgt" ("id","load_wgt","name") values (28,40.0,'Грузоподъемность до 40т') on conflict do nothing;
        </sql>
    </changeSet>
</databaseChangeLog>