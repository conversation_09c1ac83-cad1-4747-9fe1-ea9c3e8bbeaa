<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1" author="igor.belogolovsky">
        <sql>
            <![CDATA[
            DROP TABLE IF EXISTS mvz;
            CREATE MATERIALIZED VIEW mvz AS
            SELECT DISTINCT uid                                                                    AS mvz_id
                          , first_value(name) over (PARTITION BY uid ORDER BY start_date DESC )    AS name
                          , first_value(type) over (PARTITION BY uid ORDER BY start_date DESC)     AS type
                          , first_value(id_rep) over (PARTITION BY uid ORDER BY start_date DESC)   AS id_rep
                          , first_value(name_rep) over (PARTITION BY uid ORDER BY start_date DESC) AS name_rep
                          , first_value(ut) over (PARTITION BY uid ORDER BY start_date DESC)       AS ut
            FROM mvz_log ml
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>