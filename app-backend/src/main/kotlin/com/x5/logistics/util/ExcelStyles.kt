package com.x5.logistics.util

import com.x5.logistics.service.WbBuilder
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.FillPatternType
import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.ss.usermodel.VerticalAlignment

enum class ExcelStyles(val style: (WbBuilder.() -> CellStyle)) {
    Header({ this.style { setFont(wb.createFont().apply { bold = true }) } }),
    SectionHeader({
        this.style {
            fillForegroundColor = IndexedColors.GREY_25_PERCENT.index
            fillPattern = FillPatternType.SOLID_FOREGROUND
            setFont(wb.createFont().apply { bold = true })
        }
    }),
    MultiLineText({
        this.style {
            wrapText = true
            verticalAlignment = VerticalAlignment.TOP
        }
    }),
    General({ this.style { dataFormat = wb.createDataFormat().getFormat("General") } }),
    Text({ this.style { dataFormat = wb.createDataFormat().getFormat("TEXT") } }),
    Tonnage({ this.style { dataFormat = wb.createDataFormat().getFormat("0.0") } }),
    Float({ this.style { dataFormat = wb.createDataFormat().getFormat("0.00;-0.00;0;") } }),
    Date({ this.style { dataFormat = wb.createDataFormat().getFormat("dd.mm.yyyy") } }),
    Percent({ this.style { dataFormat = wb.createDataFormat().getFormat("0.00%") } }),
    RoundedPercent({ this.style { dataFormat = wb.createDataFormat().getFormat("0%") } }),
}

