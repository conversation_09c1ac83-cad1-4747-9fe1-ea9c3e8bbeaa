package com.x5.logistics.util

import java.net.URLEncoder
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import kotlin.math.absoluteValue

private const val EPS = 0.00001F
val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss")
val dateFormatterWithDots: DateTimeFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
val timeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss")
val moscowZoneId: ZoneId = ZoneId.of("Europe/Moscow")

infix fun Number.isAlmost(other: Number): Boolean =
    (this.toFloat() - other.toFloat()).absoluteValue < EPS

val Number?.isAlmostZero: Boolean
    get() = this?.let { it isAlmost 0F } ?: true

val String.urlEncoded: String
    get() = URLEncoder.encode(this, "UTF-8").replace("+", "%20")

fun moscowDateTime(): String = dateFormatter.format(LocalDateTime.now(moscowZoneId))

fun now(): LocalDateTime = LocalDateTime.now(moscowZoneId)
fun String.hashWithPrefix(prefix: String = "hash"): String {
    val hash = this.hashCode().toString().replace("-", "_")
    return "$prefix$hash"
}

