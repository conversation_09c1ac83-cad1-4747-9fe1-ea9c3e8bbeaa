package com.x5.logistics.repository.som

import com.x5.logistics.data.dictionary.DeliveryHoursGoalLogView
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.som.SomTripsAndPointsFullTable
import com.x5.logistics.data.som.SomTripsTable
import com.x5.logistics.data.temperature.TemperatureGoalLog
import com.x5.logistics.repository.*
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.som.*
import com.x5.logistics.rest.exception.WrongRequestDataException
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.between
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.transactions.transaction
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.LocalTime

@Suppress("UNCHECKED_CAST", "USELESS_CAST")
@Component
class SomReportRepo {
    fun getFilterValues(req: SomReportFilterReq): List<LabeledValue> = transaction {
        addLogger(StdOutSqlLogger)
        val dataReq = SomReportReq(
            pageNumber = null,
            pageSize = null,
            from = req.from,
            to = req.to,
            geoFilter = req.geoFilter,
            columns = req.columns,
            sort = emptyList(),
            filters = req.filters
        )
        val aliasCache = AliasCache(dataReq, req.request.name)

        aliasCache.query.withDistinct().asSequence().flatMap { row ->
            val value = aliasCache.filterExp?.let { row[it].toString() }
            mapper.readValue(value, List::class.java).map { it?.toString() }
        }.distinct()
            .filter { found ->
                val fromDB = found ?: "Пусто"
                when (val value = req.request.value) {
                    is String -> fromDB.contains(value, ignoreCase = true)
                    is List<*> -> value.isEmpty() || value.any { filter ->
                        fromDB.contains(filter.toString(), ignoreCase = true)
                    }

                    else -> throw WrongRequestDataException("filter request value should be String or list/array")
                }
            }
            .map {
                LabeledValue(
                    label = it ?: "Пусто",
                    value = it.toString(),
                )
            }
            .sortedBy { it.label }
            .toList()
    }

    fun getReport(req: SomReportReq) = transaction {
        addLogger(StdOutSqlLogger)
        val aliasCache = AliasCache(req)
        val fullTable = aliasCache.fullTable
        var count: Long? = null
        val items = aliasCache.query
            .map { row ->
                if (count == null) {
                    count = row[aliasCache.countAll]
                }
                SomReportItem(
                    vehicleLicense = if (SomColumns.vehicleLicense in req.columns) row[SomColumns.vehicleLicense.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    vehicleGroup = if (SomColumns.vehicleGroup in req.columns) row[SomColumns.vehicleGroup.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    vehicleType = if (SomColumns.vehicleType in req.columns) row[SomColumns.vehicleType.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    vehicleBrand = if (SomColumns.vehicleBrand in req.columns) row[SomColumns.vehicleBrand.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    vehicleModel = if (SomColumns.vehicleModel in req.columns) row[SomColumns.vehicleModel.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    vehicleTonnage = if (SomColumns.vehicleTonnage in req.columns) row[SomColumns.vehicleTonnage.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<Float?>] else null,
                    vehicleCreateYear = if (SomColumns.vehicleCreateYear in req.columns) row[SomColumns.vehicleCreateYear.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<Int?>] else null,
                    vehicleVin = if (SomColumns.vehicleVin in req.columns) row[SomColumns.vehicleVin.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    vehicleId = if (SomColumns.vehicleId in req.columns) row[SomColumns.vehicleId.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    trailerLicenseNum = if (SomColumns.trailerLicenseNum in req.columns) row[SomColumns.trailerLicenseNum.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    ter = if (SomColumns.ter in req.columns) row[SomColumns.ter.getExposedColumn(fullTable) as ExpressionWithColumnType<String?>] else null,
                    mr = if (SomColumns.mr in req.columns) row[SomColumns.mr.getExposedColumn(fullTable) as ExpressionWithColumnType<String?>] else null,
                    atp = if (SomColumns.atp in req.columns) row[SomColumns.atp.getExposedColumn(fullTable) as ExpressionWithColumnType<String?>] else null,
                    mvz = if (SomColumns.mvz in req.columns) row[SomColumns.mvz.getExposedColumn(fullTable) as ExpressionWithColumnType<String?>] else null,
                    mvzName = if (SomColumns.mvzName in req.columns) row[SomColumns.mvzName.getExposedColumn(fullTable) as ExpressionWithColumnType<String?>] else null,
                    retailNetwork = if (SomColumns.retailNetwork in req.columns) row[SomColumns.retailNetwork.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    atpType = if (SomColumns.atpType in req.columns) row[SomColumns.atpType.getExposedColumn(fullTable) as ExpressionWithColumnType<String?>] else null,
                    mvzType = if (SomColumns.mvzType in req.columns) row[SomColumns.mvzType.getExposedColumn(fullTable) as ExpressionWithColumnType<String?>] else null,
                    TMSRouteNum = if (SomColumns.TMSRouteNum in req.columns) row[SomColumns.TMSRouteNum.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    NQRouteNum = if (SomColumns.NQRouteNum in req.columns) row[SomColumns.NQRouteNum.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    retailNetworkOrder = if (SomColumns.retailNetworkOrder in req.columns) row[SomColumns.retailNetworkOrder.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    rcName = if (SomColumns.rcName in req.columns) row[SomColumns.rcName.getExposedColumn(fullTable) as ExpressionWithColumnType<String?>] else null,
                    vehicleArriveDate = if (SomColumns.vehicleArriveDate in req.columns) row[SomColumns.vehicleArriveDate.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalDate?>] else null,
                    vehicleArriveTime = if (SomColumns.vehicleArriveTime in req.columns) row[SomColumns.vehicleArriveTime.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalTime?>] else null,
                    vehicleRcRegDate = if (SomColumns.vehicleRcRegDate in req.columns) row[SomColumns.vehicleRcRegDate.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalDate?>] else null,
                    vehicleRcRegTime = if (SomColumns.vehicleRcRegTime in req.columns) row[SomColumns.vehicleRcRegTime.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalTime?>] else null,
                    ourTripFlag = if (SomColumns.ourTripFlag in req.columns) row[SomColumns.ourTripFlag.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    charaterDelivery = if (SomColumns.charaterDelivery in req.columns) row[SomColumns.charaterDelivery.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    ttCodeSAP = if (SomColumns.ttCodeSAP in req.columns) row[SomColumns.ttCodeSAP.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    ttCodeNQ = if (SomColumns.ttCodeNQ in req.columns) row[SomColumns.ttCodeNQ.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    ttSending = if (SomColumns.ttSending in req.columns) row[SomColumns.ttSending.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    ttArrival = if (SomColumns.ttArrival in req.columns) row[SomColumns.ttArrival.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    ttVisitingWayPlan = if (SomColumns.ttVisitingWayPlan in req.columns) row[SomColumns.ttVisitingWayPlan.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<Int?>] else null,
                    ttVisitingWayFact = if (SomColumns.ttVisitingWayFact in req.columns) row[SomColumns.ttVisitingWayFact.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<Int?>] else null,
                    ttArriveDatePlan = if (SomColumns.ttArriveDatePlan in req.columns) row[SomColumns.ttArriveDatePlan.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalDate?>] else null,
                    ttArriveTimePlan = if (SomColumns.ttArriveTimePlan in req.columns) row[SomColumns.ttArriveTimePlan.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalTime?>] else null,
                    ttArriveDateFact = if (SomColumns.ttArriveDateFact in req.columns) row[SomColumns.ttArriveDateFact.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalDate?>] else null,
                    ttArriveTimeFact = if (SomColumns.ttArriveTimeFact in req.columns) row[SomColumns.ttArriveTimeFact.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalTime?>] else null,
                    ttScanPalletDate = if (SomColumns.ttScanPalletDate in req.columns) row[SomColumns.ttScanPalletDate.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalDate?>] else null,
                    ttScanPalletTime = if (SomColumns.ttScanPalletTime in req.columns) row[SomColumns.ttScanPalletTime.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<LocalTime?>] else null,
                    deliveryInHourGPSStatus = if (SomColumns.deliveryInHourGPSStatus in req.columns) row[SomColumns.deliveryInHourGPSStatus.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    deliveryInHourPalletStatus = if (SomColumns.deliveryInHourPalletStatus in req.columns) row[SomColumns.deliveryInHourPalletStatus.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    carInTime = if (SomColumns.carInTime in req.columns) row[SomColumns.carInTime.getCalculatedExpression(
                        fullTable
                    )]?.times(100.0) else null,
                    carInGPS = if (SomColumns.carInGPS in req.columns) row[SomColumns.carInGPS.getCalculatedExpression(
                        fullTable
                    )]?.times(100.0) else null,
                    deliveryInHourPlan = if (SomColumns.deliveryInHourPlan in req.columns) row[aliasCache.deliveryHoursGoalAlias]?.toDouble() else null,
                    temperaturePlan = if (SomColumns.temperaturePlan in req.columns) row[aliasCache.tGoalAlias]?.toDouble() else null,
                    deliveryInPlan = if (SomColumns.deliveryInPlan in req.columns) row[SomColumns.deliveryInPlan.getCalculatedExpression(
                        fullTable
                    )]?.times(100.0) else null,
                    temperature = if (SomColumns.temperature in req.columns) row[SomColumns.temperature.getCalculatedExpression(
                        fullTable
                    )]?.times(100.0) else null,
                    tempStatus = if (SomColumns.tempStatus in req.columns) row[SomColumns.tempStatus.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    idRcSAP = if (SomColumns.idRcSAP in req.columns) row[SomColumns.idRcSAP.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    logisticsRcName = if (SomColumns.logisticsRcName in req.columns) row[SomColumns.logisticsRcName.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    idLogisticsRcSAP = if (SomColumns.idLogisticsRcSAP in req.columns) row[SomColumns.idLogisticsRcSAP.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    rnAndCharaterDelivery = if (SomColumns.rnAndCharaterDelivery in req.columns) row[SomColumns.rnAndCharaterDelivery.getExposedColumn(
                        fullTable
                    ) as ExpressionWithColumnType<String?>] else null,
                    tripsCount = if (SomColumns.tripsCount in req.columns) row[SomColumns.tripsCount.getCalculatedExpression(
                        fullTable
                    )]?.toLong() else null,
                    pointsCount = if (SomColumns.pointsCount in req.columns) row[SomColumns.pointsCount.getCalculatedExpression(
                        fullTable
                    )]?.toLong() else null,
                )
            }
        SomReportResponse(
            count = count ?: 0,
            pageNumber = req.pageNumber ?: 0,
            pageSize = req.pageSize ?: 0,
            items = items,
            pageTotal = if (count != null && req.pageSize != null) {
                (count!! / req.pageSize).toInt()
            } else {
                0
            }
        )
    }

    private fun SomColumns.getExposedColumn(fullTable: Boolean) =
        if (fullTable) {
            this.somTripsAndPointsFullExp
        } else {
            this.somTripsExp
        }

    private fun SomColumns.getCalculatedExpression(fullTable: Boolean): ExpressionAlias<Double?> =
        if (fullTable) {
            getSomTripsAndPointsFullCalculatedColumnExpression(this)
        } else {
            getSomTripsCalculatedColumnExpression(this)
        }
}

@Suppress("UNCHECKED_CAST")
private class AliasCache(req: SomReportReq, filterRequestedColumn: SomColumns? = null) {
    val fullTable: Boolean = req.columns.any { it.somTripsExp == null && it.grouping }
    private val groupings = req.columns
        .filter { it.grouping }
        .mapNotNull {
            if (fullTable) {
                it.somTripsAndPointsFullExp
            } else {
                it.somTripsExp
            }
        }
    val table: Table = if (fullTable) {
        SomTripsAndPointsFullTable
    } else {
        SomTripsTable
    }
    private val mvzId = if (fullTable) {
        SomTripsAndPointsFullTable.mvzId
    } else {
        SomTripsTable.mvzId
    }
    private val dtRegRcDate = if (fullTable) {
        SomTripsAndPointsFullTable.dtRegRcDate
    } else {
        SomTripsTable.dtRegRcDate
    }
    private val rcRetailNetwork = if (fullTable) {
        SomTripsAndPointsFullTable.rcRetailNetwork
    } else {
        SomTripsTable.rcRetailNetwork
    }
    private val requestedColumn = if (fullTable) {
        filterRequestedColumn?.somTripsAndPointsFullExp
    } else {
        filterRequestedColumn?.somTripsExp
    }
    val filterExp = requestedColumn?.let {
        JsonAgg(Distinct(it as ExpressionWithColumnType<String>))
    }

    val retailNetworks = listOf("ТС5", "ТСХ", "ТСЧ")

    //
    val deliveryHoursGoalForRetailNetwork = retailNetworks.map { retailNetwork ->
        DeliveryHoursGoalLogView.createSubquery(retailNetwork)
    }
    val deliveryHoursGoalQueryAlias = DeliveryHoursGoalLogView.createSubquery()
    val deliveryHoursGoalAlias = Avg(
        Coalesce(
            deliveryHoursGoalQueryAlias[DeliveryHoursGoalLogView.deliveryHoursGoal],
            GreatestFromList(deliveryHoursGoalForRetailNetwork.map { it[DeliveryHoursGoalLogView.deliveryHoursGoal] }).castToDouble()
        ), 2
    ).alias("dlv_goal")

    //
    val tGoalWithForRetailNetwork = retailNetworks.map { retailNetwork ->
        TemperatureGoalLog.tGoalQueryAlias(retailNetwork)
    }
    val tGoalQueryAlias = TemperatureGoalLog.tGoalQueryAlias()
    val tGoalAlias = Avg(
        Coalesce(
            tGoalQueryAlias[TemperatureGoalLog.temperatureGoal],
            GreatestFromList(tGoalWithForRetailNetwork.map { it[TemperatureGoalLog.temperatureGoal] }).castToFloat()
        ), 2
    ).alias("t_goal")

    private val calcExpressions = req.columns
        .filterNot { it.grouping }
        .map {
            if (fullTable) {
                when (it) {
                    SomColumns.deliveryInHourPlan -> deliveryHoursGoalAlias
                    SomColumns.temperaturePlan -> tGoalAlias
                    else -> getSomTripsAndPointsFullCalculatedColumnExpression(it)
                }
            } else {
                when (it) {
                    SomColumns.deliveryInHourPlan -> deliveryHoursGoalAlias
                    SomColumns.temperaturePlan -> tGoalAlias
                    else -> getSomTripsCalculatedColumnExpression(it)
                }
            }
        }
    val countAll = CountAll().alias("count_all")
    val columnsToSelect = if (filterExp == null) {
        groupings + calcExpressions + countAll
    } else {
        listOf(filterExp)
    }

    val query = table
        .join(
            otherTable = OrganizationalUnitsTimelineTable,
            joinType = JoinType.INNER,
            additionalConstraint = {
                OrganizationalUnitsTimelineTable.mvzId eq mvzId and
                        (OrganizationalUnitsTimelineTable.startDate lessEq dtRegRcDate) and
                        (dtRegRcDate less OrganizationalUnitsTimelineTable.endDate)
            }
        ).let {
            if (req.columns.contains(SomColumns.deliveryInHourPlan)) {
                var query = it.join(
                    otherTable = deliveryHoursGoalQueryAlias,
                    joinType = JoinType.LEFT,
                    additionalConstraint = {
                        (deliveryHoursGoalQueryAlias[DeliveryHoursGoalLogView.retailNetwork] eq rcRetailNetwork) and
                                (deliveryHoursGoalQueryAlias[DeliveryHoursGoalLogView.startDate] lessEq dtRegRcDate) and
                                (deliveryHoursGoalQueryAlias[DeliveryHoursGoalLogView.endDate] greater dtRegRcDate)
                    }
                )
                deliveryHoursGoalForRetailNetwork.forEach { networkQueryAlias ->
                    query = query.join(
                        otherTable = networkQueryAlias,
                        joinType = JoinType.LEFT,
                        additionalConstraint = {
                            networkQueryAlias[DeliveryHoursGoalLogView.startDate] lessEq dtRegRcDate and
                                    (dtRegRcDate less networkQueryAlias[DeliveryHoursGoalLogView.endDate])
                        })
                }
                query
            } else {
                it
            }
        }.let {
            if (req.columns.contains(SomColumns.temperaturePlan)) {
                var query = it.join(
                    otherTable = tGoalQueryAlias,
                    joinType = JoinType.LEFT,
                    additionalConstraint = {
                        tGoalQueryAlias[TemperatureGoalLog.retailNetwork] eq rcRetailNetwork and
                                (tGoalQueryAlias[TemperatureGoalLog.startDate] lessEq dtRegRcDate) and
                                (dtRegRcDate less tGoalQueryAlias[TemperatureGoalLog.endDate])
                    }
                )
                tGoalWithForRetailNetwork.forEach { networkQueryAlias ->
                    query = query.join(
                        otherTable = networkQueryAlias,
                        joinType = JoinType.LEFT,
                        additionalConstraint = {
                            networkQueryAlias[TemperatureGoalLog.startDate] lessEq dtRegRcDate and
                                    (dtRegRcDate less networkQueryAlias[TemperatureGoalLog.endDate])
                        })
                }
                query
            } else {
                it
            }
        }
        .select(columnsToSelect)
        .where(dtRegRcDate.between(req.from, req.to))
        .applyGeoFilter(req.geoFilter)
        .apply {
            req.filters.filter { it.name.grouping }
                .forEach { filter ->
                    val exp = if (fullTable) {
                        filter.name.somTripsAndPointsFullExp
                    } else {
                        filter.name.somTripsExp
                    }
                    exp?.let {
                        andWhere {
                            buildFilterPredicate(
                                condition = filter.condition,
                                values = filter.value,
                                type = filter.name.type,
                                exposedExpression = it,
                                strictFilter = true,
                                precision = filter.name.precision
                            )
                        }
                    }
                }
        }
        .groupBy(*groupings.toTypedArray())
        .apply {
            req.filters.filterNot { it.name.grouping }
                .forEach { filter ->
                    val exp = if (fullTable) {
                        when (filter.name) {
                            SomColumns.deliveryInHourPlan -> deliveryHoursGoalAlias
                            SomColumns.temperaturePlan -> tGoalAlias
                            else -> getSomTripsAndPointsFullCalculatedColumnExpression(filter.name)
                        }
                    } else {
                        when (filter.name) {
                            SomColumns.deliveryInHourPlan -> deliveryHoursGoalAlias
                            SomColumns.temperaturePlan -> tGoalAlias
                            else -> getSomTripsCalculatedColumnExpression(filter.name)
                        }
                    }.let {
                        if (filter.name.percentType) {
                            it.delegate.castToDouble() * doubleLiteral(100.0)
                        } else {
                            it.delegate
                        }
                    }
                    andHaving {
                        buildFilterPredicate(
                            condition = filter.condition,
                            values = filter.value,
                            type = filter.name.type,
                            exposedExpression = exp,
                            strictFilter = true,
                            precision = filter.name.precision
                        )
                    }
                }
        }
        .apply {
            req.sort.forEach { sort ->
                val exp = if (fullTable) {
                    when (sort.column) {
                        SomColumns.deliveryInHourPlan -> deliveryHoursGoalAlias
                        SomColumns.temperaturePlan -> tGoalAlias
                        else -> sort.column.somTripsAndPointsFullExp
                            ?: getSomTripsAndPointsFullCalculatedColumnExpression(sort.column)
                    }
                } else {
                    when (sort.column) {
                        SomColumns.deliveryInHourPlan -> deliveryHoursGoalAlias
                        SomColumns.temperaturePlan -> tGoalAlias
                        else -> sort.column.somTripsExp ?: getSomTripsCalculatedColumnExpression(sort.column)
                    }
                }
                orderBy(exp, if (sort.asc) SortOrder.ASC else SortOrder.DESC)
            }
        }
        .apply {
            if (req.pageSize != null && req.pageNumber != null) {
                limit(
                    n = req.pageSize,
                    offset = (req.pageNumber * req.pageSize).toLong()
                )
            }
        }
}

private fun getSomTripsCalculatedColumnExpression(column: SomColumns): ExpressionAlias<Double?> =
    when (column) {
        SomColumns.carInTime -> SomTripsTable.numTripTms.countFiltered(SomTripsTable.carIn eq true, true)
            .castToDouble().safeDiv(SomTripsTable.numTripTms.countDistinct().castToDouble())

        SomColumns.carInGPS -> SomTripsTable.numTripTms.countFiltered(SomTripsTable.carInGps eq true, true)
            .castToDouble().safeDiv(SomTripsTable.numTripTms.countDistinct().castToDouble())


        SomColumns.deliveryInPlan -> SomTripsTable.isDelvIntimeCount.sum().castToDouble()
            .safeDiv(SomTripsTable.pointsCount.sum().castToDouble())


        SomColumns.temperature -> SomTripsTable.numTripTms.countFiltered(
            SomTripsTable.statusTFinal eq true and (SomTripsTable.statusTCalculate eq true),
            true
        ).castToDouble().safeDiv(
            SomTripsTable.numTripTms.countFiltered(SomTripsTable.statusTCalculate eq true, true).castToDouble()
        )

        SomColumns.tripsCount -> SomTripsTable.numTripTms.countDistinct().castToDouble().toNullable()
        SomColumns.pointsCount -> SomTripsTable.pointsCount.sum().castToDouble().toNullable()

        else -> throw IllegalArgumentException("Unsupported calculated column type: $column")
    }.alias(column.name)

private fun getSomTripsAndPointsFullCalculatedColumnExpression(column: SomColumns): ExpressionAlias<Double?> =
    when (column) {
        SomColumns.carInTime -> SomTripsAndPointsFullTable.numTripTms.countFiltered(
            SomTripsAndPointsFullTable.carIn eq true,
            true
        ).castToDouble().safeDiv(SomTripsAndPointsFullTable.numTripTms.countDistinct().castToDouble())

        SomColumns.carInGPS -> SomTripsAndPointsFullTable.numTripTms.countFiltered(
            SomTripsAndPointsFullTable.carInGps eq true,
            true
        ).castToDouble().safeDiv(SomTripsAndPointsFullTable.numTripTms.countDistinct().castToDouble())


        SomColumns.deliveryInPlan -> SomTripsAndPointsFullTable.isDelvIntimeCount.sum().castToDouble()
            .safeDiv(SomTripsAndPointsFullTable.pointsCount.sum().castToDouble())


        SomColumns.temperature -> SomTripsAndPointsFullTable.numTripTms.countFiltered(
            SomTripsAndPointsFullTable.statusTFinal eq true and (SomTripsAndPointsFullTable.statusTCalculate eq true),
            true
        ).castToDouble().safeDiv(
            SomTripsAndPointsFullTable.numTripTms.countFiltered(
                SomTripsAndPointsFullTable.statusTCalculate eq true,
                true
            ).castToDouble()
        )

        SomColumns.tripsCount -> SomTripsAndPointsFullTable.numTripTms.countDistinct().castToDouble().toNullable()
        SomColumns.pointsCount -> SomTripsAndPointsFullTable.pointsCount.sum().castToDouble().toNullable()

        else -> throw IllegalArgumentException("Unsupported calculated column type: $column")
    }.alias(column.name)