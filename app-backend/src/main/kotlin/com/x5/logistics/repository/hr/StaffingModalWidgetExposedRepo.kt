package com.x5.logistics.repository.hr

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.TsData
import com.x5.logistics.data.hr.HrEmployeeFull
import com.x5.logistics.data.hr.KtgDictionary
import com.x5.logistics.data.hr.Types
import com.x5.logistics.data.hr.VehicleRegionTimeline
import com.x5.logistics.repository.DatePart
import com.x5.logistics.repository.Div
import com.x5.logistics.repository.Greatest
import com.x5.logistics.repository.Least
import com.x5.logistics.rest.dto.hr.modal.staffing.StaffingModalAtpData
import com.x5.logistics.rest.dto.hr.modal.staffing.StaffingModalAtpReq
import com.x5.logistics.rest.dto.hr.modal.staffing.StaffingModalMrData
import com.x5.logistics.rest.dto.hr.modal.staffing.StaffingModalMrReq
import com.x5.logistics.rest.dto.hr.modal.staffing.StaffingModalResp
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.IntegerColumnType
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.NoOpConversion
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.div
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.minus
import org.jetbrains.exposed.sql.SqlExpressionBuilder.times
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.castTo
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.javatime.JavaInstantColumnType
import org.jetbrains.exposed.sql.javatime.dateLiteral
import org.jetbrains.exposed.sql.longLiteral
import org.jetbrains.exposed.sql.stringLiteral
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.math.RoundingMode

@Component
class StaffingModalWidgetExposedRepo {
    val logger = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    @ExposedTransactional
    fun getMrData(req: StaffingModalMrReq): StaffingModalResp {
        val prevRate = HrEmployeeFull.prevRate(req.from.minusDays(1))
        val rate = HrEmployeeFull.rate(req.to)
        val hrStartDate = Greatest(HrEmployeeFull.fromDttm, OrganizationalUnitsTimelineTable.startDate)
        val hrEndDate = Greatest(HrEmployeeFull.toDttm, OrganizationalUnitsTimelineTable.endDate)
        val factRateMr = with(HrEmployeeFull) {
            join(
                otherTable = OrganizationalUnitsTimelineTable,
                joinType = JoinType.LEFT,
                additionalConstraint = {
                    (hrStartDate less hrEndDate) and
                            (OrganizationalUnitsTimelineTable.mvzId eq mvzId)
                })
                .select(
                    OrganizationalUnitsTimelineTable.mrId,
                    OrganizationalUnitsTimelineTable.mrName,
                    prevRate,
                    rate
                ).where {
                    (empStatusDesc eq "Активный") and
                            (finCategoryDesc eq "Водители автомобиля УТ") and
                            (OrganizationalUnitsTimelineTable.mrId.isNotNull())
                }.groupBy(
                    OrganizationalUnitsTimelineTable.mrId,
                    OrganizationalUnitsTimelineTable.mrName
                ).alias("fact_rate_mr")
        }

        val tsAgeYears = Div(
            DatePart(
                stringLiteral("day"),
                dateLiteral(req.to).castTo(JavaInstantColumnType()) minus TsData.createDate.castTo(
                    JavaInstantColumnType()
                )
            ).castTo(IntegerColumnType()),
            intLiteral(365)
        ).alias("ts_age_years")

        val tsStartDate = Greatest(VehicleRegionTimeline.startDate, OrganizationalUnitsTimelineTable.startDate).alias("ts_start_date")
        val tsEndDate = Least(VehicleRegionTimeline.endDate, OrganizationalUnitsTimelineTable.endDate).alias("ts_end_date")
        val tsData = with(VehicleRegionTimeline) {
            join(TsData, JoinType.INNER, equnr, TsData.equnr)
                .join(Types, JoinType.INNER, TsData.tsType, Types.tsType)
                .join(KtgDictionary, JoinType.LEFT, KtgDictionary.vehicleAge, tsAgeYears.delegate)
                .join(
                    otherTable = OrganizationalUnitsTimelineTable,
                    joinType = JoinType.LEFT,
                    additionalConstraint = {
                        (tsStartDate.delegate less tsEndDate.delegate) and
                                (OrganizationalUnitsTimelineTable.mvzId eq mvzId)
                    })
                .select(
                    equnr,
                    tsStartDate,
                    tsEndDate,
                    OrganizationalUnitsTimelineTable.mrId,
                    OrganizationalUnitsTimelineTable.atpId,
                    TsData.createDate,
                    tsAgeYears,
                    KtgDictionary.ktgVehicleCount
                ).where {
                    Types.tsGroup eq "Основное" and
                            (TsData.tsInactive eq "false")
                }
        }.alias("ts_data")

        val timeLineRate = (doubleLiteral(2.0) * Sum(
            Case()
                .When(
                    (tsData[tsStartDate] lessEq dateLiteral(req.to)) and (tsData[tsEndDate] greater dateLiteral(req.to)),
                    KtgDictionary.ktgVehicleCount.aliasOnlyExpression()
                )
                .Else(doubleLiteral(0.0).castTo(DoubleColumnType())),
            DoubleColumnType()
        ).castTo(DoubleColumnType())).alias("rate")
        val planRateMr = with(VehicleRegionTimeline) {
            tsData
                .select(
                    tsData[OrganizationalUnitsTimelineTable.mrId],
                    timeLineRate
                )
                .groupBy(tsData[OrganizationalUnitsTimelineTable.mrId])
                .alias("plan_rate_mr")
        }
        val staffingLevelPercentage = Case().When(planRateMr[timeLineRate] eq doubleLiteral(0.0), doubleLiteral(0.0))
            .Else(
                NoOpConversion(
                    factRateMr[rate],
                    DoubleColumnType()
                ) / planRateMr[timeLineRate]
            ).alias("staffing_level_percentage")
        val factRate = factRateMr[rate].alias("fact_rate")
        val deltaRate =
            (NoOpConversion(factRateMr[rate], DoubleColumnType()) - planRateMr[timeLineRate])
                .alias("delta_rate")
        val deltaPrevRate =
            (NoOpConversion(factRateMr[rate], DoubleColumnType()) - factRateMr[prevRate])
                .alias("delta_prev_rate")
        val query = with(HrEmployeeFull) {
            factRateMr.join(
                planRateMr,
                JoinType.INNER,
                factRateMr[OrganizationalUnitsTimelineTable.mrId],
                planRateMr[tsData[OrganizationalUnitsTimelineTable.mrId]]
            ).select(
                factRateMr[OrganizationalUnitsTimelineTable.mrId],
                factRateMr[OrganizationalUnitsTimelineTable.mrName],
                staffingLevelPercentage,
                factRate,
                deltaRate,
                deltaPrevRate
            )
                .orderBy(factRateMr[OrganizationalUnitsTimelineTable.mrId])
        }
        if (logQuery) logger.info(query.prepareSQL(QueryBuilder(false)))

        return StaffingModalResp(0.95, query.map {
            StaffingModalMrData(
                mrId = it[factRateMr[OrganizationalUnitsTimelineTable.mrId]].toBigInteger(),
                mrName = it[factRateMr[OrganizationalUnitsTimelineTable.mrName]],
                driversPercent = (it[staffingLevelPercentage] * 100).toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP),
                driversFact = it[factRate].toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP),
                driversDiff = it[deltaRate].toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP),
                previousPeriodDriversDiff = it[deltaPrevRate].toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP)
            )
        })
    }

    @ExposedTransactional
    fun getAtpData(req: StaffingModalAtpReq): List<StaffingModalAtpData> {
        val prevRate = HrEmployeeFull.prevRate(req.from.minusDays(1))
        val rate = HrEmployeeFull.rate(req.to)
        val hrStartDate = Greatest(HrEmployeeFull.fromDttm, OrganizationalUnitsTimelineTable.startDate)
        val hrEndDate = Least(HrEmployeeFull.toDttm, OrganizationalUnitsTimelineTable.endDate)
        val factRateAtp = with(HrEmployeeFull) {
            join(
                otherTable = OrganizationalUnitsTimelineTable,
                joinType = JoinType.LEFT,
                additionalConstraint = {
                    (hrStartDate less hrEndDate) and
                            (OrganizationalUnitsTimelineTable.mvzId eq mvzId)
                })
                .select(
                    OrganizationalUnitsTimelineTable.mrId,
                    OrganizationalUnitsTimelineTable.mrName,
                    OrganizationalUnitsTimelineTable.atpId,
                    OrganizationalUnitsTimelineTable.atpName,
                    prevRate,
                    rate
                ).where {
                    (empStatusDesc eq "Активный") and
                            (finCategoryDesc eq "Водители автомобиля УТ") and
                            (OrganizationalUnitsTimelineTable.mrId eq longLiteral(req.mrId.toLong()))
                }.groupBy(
                    OrganizationalUnitsTimelineTable.mrId,
                    OrganizationalUnitsTimelineTable.mrName,
                    OrganizationalUnitsTimelineTable.atpId,
                    OrganizationalUnitsTimelineTable.atpName,
                )
        }.alias("fact_rate_atp")

        val tsAgeYears = Div(
            DatePart(
                stringLiteral("day"),
                dateLiteral(req.to).castTo(JavaInstantColumnType()) minus TsData.createDate.castTo(
                    JavaInstantColumnType()
                )
            ).castTo(IntegerColumnType()),
            intLiteral(365)
        ).alias("ts_age_years")

        val tsStartDate = Greatest(VehicleRegionTimeline.startDate, OrganizationalUnitsTimelineTable.startDate).alias("ts_start_date")
        val tsEndDate = Least(VehicleRegionTimeline.endDate, OrganizationalUnitsTimelineTable.endDate).alias("ts_end_date")
        val tsData = with(VehicleRegionTimeline) {
            join(TsData, JoinType.INNER, equnr, TsData.equnr)
                .join(Types, JoinType.INNER, TsData.tsType, Types.tsType)
                .join(KtgDictionary, JoinType.LEFT, KtgDictionary.vehicleAge, tsAgeYears.delegate)
                .join(
                    otherTable = OrganizationalUnitsTimelineTable,
                    joinType = JoinType.LEFT,
                    additionalConstraint = {
                        (tsStartDate.delegate less tsEndDate.delegate) and
                                (OrganizationalUnitsTimelineTable.mvzId eq mvzId)
                    })
                .select(
                    equnr,
                    tsStartDate,
                    tsEndDate,
                    OrganizationalUnitsTimelineTable.mrId,
                    OrganizationalUnitsTimelineTable.mrName,
                    OrganizationalUnitsTimelineTable.atpId,
                    OrganizationalUnitsTimelineTable.atpName,
                    TsData.createDate,
                    tsAgeYears,
                    KtgDictionary.ktgVehicleCount
                ).where {
                    Types.tsGroup eq "Основное" and
                            (TsData.tsDeleted eq "false") and
                            (TsData.tsInactive eq "false") and
                            (tsStartDate.delegate lessEq dateLiteral(req.to)) and
                            (tsEndDate.delegate greater dateLiteral(req.to))
                }
        }.alias("ts_data")


        val timelineRate = (doubleLiteral(2.0) * Sum(
            Case()
                .When(
                    (tsData[tsStartDate] lessEq dateLiteral(req.to)) and (tsData[tsEndDate] greater dateLiteral(req.to)),
                    KtgDictionary.ktgVehicleCount.aliasOnlyExpression()
                )
                .Else(doubleLiteral(0.0).castTo(DoubleColumnType())),
            DoubleColumnType()
        ).castTo(DoubleColumnType())).alias("rate")
        val planRateAtp = with(VehicleRegionTimeline) {
            tsData
                .select(
                    tsData[OrganizationalUnitsTimelineTable.mrId],
                    tsData[OrganizationalUnitsTimelineTable.mrName],
                    tsData[OrganizationalUnitsTimelineTable.atpId],
                    tsData[OrganizationalUnitsTimelineTable.atpName],
                    timelineRate
                ).where {
                    tsData[OrganizationalUnitsTimelineTable.mrId] eq req.mrId.toLong()
                }
                .groupBy(
                    tsData[OrganizationalUnitsTimelineTable.mrId],
                    tsData[OrganizationalUnitsTimelineTable.mrName],
                    tsData[OrganizationalUnitsTimelineTable.atpId],
                    tsData[OrganizationalUnitsTimelineTable.atpName]
                )
                .alias("plan_rate_atp")
        }

        val staffingLevelPercentage = Case().When(planRateAtp[timelineRate] eq doubleLiteral(0.0), doubleLiteral(0.0))
            .Else(NoOpConversion<Double, Double>(factRateAtp[rate], DoubleColumnType()) / planRateAtp[timelineRate])
            .alias("staffing_level_percentage")
        val factRate = factRateAtp[rate]
            .alias("fact_rate")
        val deltaRate =
            (NoOpConversion(factRateAtp[rate], DoubleColumnType()) - planRateAtp[timelineRate])
                .alias("delta_rate")
        val deltaPrevRate =
            (NoOpConversion(factRateAtp[rate], DoubleColumnType()) - factRateAtp[prevRate])
                .alias("delta_prev_rate")
        val query = with(HrEmployeeFull) {
            planRateAtp.join(
                otherTable = factRateAtp,
                joinType = JoinType.INNER,
                onColumn = planRateAtp[tsData[OrganizationalUnitsTimelineTable.atpId]],
                otherColumn = factRateAtp[OrganizationalUnitsTimelineTable.atpId]
            )
                .select(
                    planRateAtp[tsData[OrganizationalUnitsTimelineTable.mrId]],
                    planRateAtp[tsData[OrganizationalUnitsTimelineTable.mrName]],
                    planRateAtp[tsData[OrganizationalUnitsTimelineTable.atpId]],
                    planRateAtp[tsData[OrganizationalUnitsTimelineTable.atpName]],
                    staffingLevelPercentage,
                    factRate,
                    deltaRate,
                    deltaPrevRate,
                )
                .orderBy(planRateAtp[tsData[OrganizationalUnitsTimelineTable.atpName]])
        }

        if (logQuery) logger.info(query.prepareSQL(QueryBuilder(false)))

        return query.map {
            StaffingModalAtpData(
                mrId = it[planRateAtp[tsData[OrganizationalUnitsTimelineTable.mrId]]].toBigInteger(),
                mrName = it[planRateAtp[tsData[OrganizationalUnitsTimelineTable.mrName]]],
                atpId = it[planRateAtp[tsData[OrganizationalUnitsTimelineTable.atpId]]].toBigInteger(),
                atpName = it[planRateAtp[tsData[OrganizationalUnitsTimelineTable.atpName]]],
                driversPercent = (it[staffingLevelPercentage] * 100).toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP),
                driversFact = it[factRate].toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP),
                driversDiff = it[deltaRate].toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP),
                previousPeriodDriversDiff = it[deltaPrevRate].toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP)
            )
        }
    }
}