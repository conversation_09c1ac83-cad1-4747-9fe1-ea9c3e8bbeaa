package com.x5.logistics.repository.deliveryhours

import com.x5.logistics.data.SomTrips
import com.x5.logistics.data.dictionary.DeliveryHoursGoalLogView
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.som.SomTripsTable
import com.x5.logistics.repository.GreatestFromList
import com.x5.logistics.repository.NullIf
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.castToDouble
import com.x5.logistics.repository.sumFiltered
import com.x5.logistics.repository.times
import com.x5.logistics.rest.dto.deliveryhours.DeliveryHoursDesktopWidgetReq
import com.x5.logistics.service.DesktopWidgetsService
import com.x5.logistics.service.temperature.DiagramPointsDto
import com.x5.logistics.service.toDoubleSafely
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.Avg
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.OrOp
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.div
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.sum
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Component
import java.math.RoundingMode
import java.time.LocalDate

@Component
class DeliveryHoursWidgetRepo(
    val desktopWidgetsService: DesktopWidgetsService
) {
    val logger = getLogger()

    suspend fun getDeliveryHoursGoalValue(req: DeliveryHoursDesktopWidgetReq): Double = newSuspendedTransaction {
        val goalForRetailNetwork = req.characterDeliveryForRetailNetwork.map { characterDeliveryForRetailNetwork ->
            DeliveryHoursGoalLogView.createSubquery(characterDeliveryForRetailNetwork.retailNetwork)
        }

        val goalQueryAlias = DeliveryHoursGoalLogView.createSubquery()

        val goalAlias = Avg(
            Coalesce(
                goalQueryAlias[DeliveryHoursGoalLogView.deliveryHoursGoal],
                GreatestFromList(goalForRetailNetwork.map { it[DeliveryHoursGoalLogView.deliveryHoursGoal] }).castToDouble()
            ), 2
        ).alias("dlv_goal")

        var mainQuery = SomTrips.join(
            otherTable = goalQueryAlias,
            joinType = JoinType.LEFT,
            additionalConstraint = {
                goalQueryAlias[DeliveryHoursGoalLogView.retailNetwork] eq SomTrips.rcRetailNetwork and
                        (goalQueryAlias[DeliveryHoursGoalLogView.startDate] lessEq SomTrips.dtRegRcDate) and
                        (SomTrips.dtRegRcDate less goalQueryAlias[DeliveryHoursGoalLogView.endDate])
            })

        goalForRetailNetwork.forEach { queryAlias ->
            mainQuery = mainQuery.join(
                otherTable = queryAlias,
                joinType = JoinType.LEFT,
                additionalConstraint = {
                    queryAlias[DeliveryHoursGoalLogView.startDate] lessEq SomTrips.dtRegRcDate and
                            (SomTrips.dtRegRcDate less queryAlias[DeliveryHoursGoalLogView.endDate])
                })
        }

        mainQuery.join(
            otherTable = OrganizationalUnitsTimelineTable,
            joinType = JoinType.LEFT,
            additionalConstraint = {
                OrganizationalUnitsTimelineTable.mvzId eq SomTrips.mvzId and
                        (OrganizationalUnitsTimelineTable.startDate lessEq SomTrips.dtRegRcDate) and
                        (SomTrips.dtRegRcDate less OrganizationalUnitsTimelineTable.endDate)
            }).select(goalAlias)
            .where {
                SomTrips.dtRegRcDate.between(req.from, req.to) and
                        OrOp(
                            req.characterDeliveryForRetailNetwork.map {
                                (SomTripsTable.rcRetailNetwork eq it.retailNetwork) and
                                        (SomTripsTable.charaterDelvr inList it.characterDelivery)
                            }
                        )            }
            .applyGeoFilter(req.geoFilter)
            .also { logger.debug(it.prepareSQL(QueryBuilder(false))) }
            .single()[goalAlias]?.setScale(1, RoundingMode.HALF_EVEN)?.toDouble() ?: 0.0
    }

    suspend fun getDiagramPoints(req: DeliveryHoursDesktopWidgetReq): DiagramPointsDto = newSuspendedTransaction {
        val metricBuilder = MetricBuilder()

        val baseMetrics = listOf(
            metricBuilder.buildOwnCarsMetric().alias("own_cars"),
            metricBuilder.buildHiredCarsMetric().alias("hired_cars"),
            metricBuilder.buildAllCarsMetric().alias("all_cars"),
        )

        val diagramMetrics = desktopWidgetsService.getDateOffsets(req.from, req.to).flatMapIndexed { index, offset ->
            listOf(
                metricBuilder.buildOwnCarsMetric(offset).alias("own_cars_$index"),
                metricBuilder.buildHiredCarsMetric(offset).alias("hired_cars_$index"),
                metricBuilder.buildAllCarsMetric(offset).alias("all_cars_$index")
            )
        }

        val valuesQuery = SomTrips
            .join(
                otherTable = OrganizationalUnitsTimelineTable,
                joinType = JoinType.LEFT,
                additionalConstraint = {
                    OrganizationalUnitsTimelineTable.mvzId eq SomTrips.mvzId and
                            (OrganizationalUnitsTimelineTable.startDate lessEq SomTrips.dtRegRcDate) and
                            (SomTrips.dtRegRcDate less OrganizationalUnitsTimelineTable.endDate)
                }).select(baseMetrics + diagramMetrics)
            .where {
                SomTrips.dtRegRcDate.between(req.from, req.to) and
                        OrOp(
                            req.characterDeliveryForRetailNetwork.map {
                                (SomTripsTable.rcRetailNetwork eq it.retailNetwork) and
                                        (SomTripsTable.charaterDelvr inList it.characterDelivery)
                            }
                        )            }.applyGeoFilter(req.geoFilter)
            .also { logger.debug(it.prepareSQL(QueryBuilder(false))) }
            .single()

        val ownCars = valuesQuery.getOrNull(baseMetrics[0])?.toDoubleSafely()
        val hiredCars = valuesQuery.getOrNull(baseMetrics[1])?.toDoubleSafely()
        val allCars = when {
            ownCars == null || hiredCars == null -> null
            else -> valuesQuery.getOrNull(baseMetrics[2])?.toDoubleSafely()
        }
        val diagramPointsOwnCars = (diagramMetrics.indices step 3).map {
            valuesQuery.getOrNull(diagramMetrics[it])?.toDoubleSafely()
        }
        val diagramPointsHiredCars = (diagramMetrics.indices step 3).map {
            valuesQuery.getOrNull(diagramMetrics[it + 1])?.toDoubleSafely()
        }
        val diagramPointsAllCars = when {
            diagramPointsOwnCars.isEmpty() || diagramPointsHiredCars.isEmpty() -> emptyList()
            else -> (diagramMetrics.indices step 3).map {
                valuesQuery.getOrNull(diagramMetrics[it + 2])?.toDoubleSafely()
            }
        }
        DiagramPointsDto(
            ownCars = ownCars,
            hiredCars = hiredCars,
            allCars = allCars,
            diagramPointsOwnCars = diagramPointsOwnCars,
            diagramPointsHiredCars = diagramPointsHiredCars,
            diagramPointsAllCars = diagramPointsAllCars
        )
    }

    private inner class MetricBuilder {
        fun buildOwnCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double?> =
            with(SomTrips) {
                val baseCondition = ourTripFlag eq true
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val conditions = listOfNotNull(baseCondition, dateCondition).reduce { acc, expr -> acc and expr }

                val numerator = isDelvInTimeCount.sumFiltered(conditions).castToDouble()
                val denominator = pointsCount.sumFiltered(conditions).castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildHiredCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double?> =
            with(SomTrips) {
                val baseCondition = ourTripFlag eq false
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val conditions = listOfNotNull(baseCondition, dateCondition).reduce { acc, expr -> acc and expr }

                val numerator = isDelvInTimeCount.sumFiltered(conditions).castToDouble()
                val denominator = pointsCount.sumFiltered(conditions).castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildAllCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double?> =
            with(SomTrips) {
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val numerator = when {
                    dateCondition != null -> isDelvInTimeCount.sumFiltered(dateCondition)
                    else -> isDelvInTimeCount.sum()
                }.castToDouble()

                val denominator = when {
                    dateCondition != null -> pointsCount.sumFiltered(dateCondition)
                    else -> pointsCount.sum()
                }.castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }
    }
}