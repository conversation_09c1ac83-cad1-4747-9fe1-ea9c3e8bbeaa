package com.x5.logistics.repository.repaircostfactor

import com.x5.logistics.data.PlsByDay
import com.x5.logistics.data.PlsByDayWithRate
import com.x5.logistics.data.RepairStructures
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksSubtypesTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksTypesTable
import com.x5.logistics.repository.CountAll
import com.x5.logistics.repository.SumOver
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.div
import com.x5.logistics.repository.minus
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.GranularityItem
import com.x5.logistics.rest.dto.GranularitySubQueryData
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.containsOnlyMvz
import com.x5.logistics.rest.dto.parseGranularity
import com.x5.logistics.rest.dto.produceGranularLabel
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportColumn
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportColumnFilter
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportItem
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportMassFilterReq
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportReq
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportResp
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportSortOrder
import com.x5.logistics.util.checkFilterValues
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.ColumnSet
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.ExpressionWithColumnType
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.times
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.transactions.transaction
import org.springframework.stereotype.Component
import java.time.LocalDate

@Component
class RepairCostFactorExposedRepo {
    val logger = getLogger()

    fun getFilterValues(req: RepairCostFactorReportMassFilterReq): List<LabeledValue> = transaction {
        val requestedColumn = req.request.name
        val request = RepairCostFactorReportReq(
            pageNumber = null,
            pageSize = null,
            from = req.from,
            to = req.to,
            geoFilter = req.geoFilter,
            columns = (listOf(requestedColumn) + req.columns).distinct(),
            sort = listOf(RepairCostFactorReportSortOrder(requestedColumn, true)),
            filters = req.filters + listOf(
                RepairCostFactorReportColumnFilter(
                    name = requestedColumn,
                    condition = FilterCondition.contain,
                    value = req.request.value
                )
            ),
            granularity = null
        )
        val queryData = prepareQueries(
            req = request,
            totals = false,
            strictFilters = false
        )

        logger.debug(queryData.query.prepareSQL(QueryBuilder(false)))

        queryData.query.map {
            it[queryData.columns[requestedColumn]!!]
        }.distinct().map {
            LabeledValue(
                it?.toString() ?: "Пусто",
                it.toString()
            )
        }
    }

    fun checkMassFilter(req: RepairCostFactorReportMassFilterReq): MassFilterResp = transaction {
        val requestedColumn = req.request.name
        val request = RepairCostFactorReportReq(
            pageNumber = null,
            pageSize = null,
            from = req.from,
            to = req.to,
            geoFilter = req.geoFilter,
            columns = (listOf(requestedColumn) + req.columns).distinct(),
            sort = listOf(RepairCostFactorReportSortOrder(requestedColumn, true)),
            filters = req.filters + listOf(
                RepairCostFactorReportColumnFilter(
                    name = requestedColumn,
                    condition = FilterCondition.equal,
                    value = req.request.value
                )
            ),
            granularity = null
        )
        val queryData = prepareQueries(request, false)
        logger.debug(queryData.query.prepareSQL(QueryBuilder(false)))

        val values = queryData.query.map { it[queryData.columns[requestedColumn]!!]?.toString() ?: "Пусто" }
        val userValues = req.request.value
        checkFilterValues(userValues, values)
    }

    @Suppress("Unchecked_cast")
    fun getReport(req: RepairCostFactorReportReq): RepairCostFactorReportResp = transaction {
        val queryData = prepareQueries(req)
        var count: Long? = null
        var totalRepairExpensesFull: Double? = null
        var totalRepairRubFact: Double? = null

        val items = queryData.query.map { row ->
            if (count == null && queryData.countAlias != null) {
                count = row[queryData.countAlias]
            }

            if (totalRepairExpensesFull == null && queryData.totalRepairExpensesFullAlias != null) {
                totalRepairExpensesFull = row[queryData.totalRepairExpensesFullAlias]
            }

            if (totalRepairRubFact == null && queryData.totalRepairRubFactAlias != null) {
                totalRepairRubFact = row[queryData.totalRepairRubFactAlias]
            }

            val granularMileageUsed = queryData.granularMileage != null && req.granularity != null &&
                    row[queryData.granularMileage] != null && (RepairCostFactorReportColumn.repairRubPlan in req.columns ||
                    RepairCostFactorReportColumn.repairRub in req.columns ||
                    RepairCostFactorReportColumn.repairRubDeviation in req.columns)

            val granularRepairExpensesFullPlanUsed = queryData.granularRate != null && req.granularity != null &&
                    row[queryData.granularRate] != null && (RepairCostFactorReportColumn.repairExpensesFullPlan in req.columns ||
                    RepairCostFactorReportColumn.repairRubPlan in req.columns ||
                    RepairCostFactorReportColumn.repairExpensesFullDeviation in req.columns ||
                    RepairCostFactorReportColumn.repairRubDeviation in req.columns)

            val granularRepairExpensesFullUsed = req.granularity != null && queryData.granularRepair != null &&
                    row[queryData.granularRepair] != null && (RepairCostFactorReportColumn.repairExpensesFull in req.columns ||
                    RepairCostFactorReportColumn.repairRub in req.columns ||
                    RepairCostFactorReportColumn.repairExpensesFullDeviation in req.columns ||
                    RepairCostFactorReportColumn.repairRubDeviation in req.columns)

            val granularRepairExpensesFullDeviationUsed = req.granularity != null && queryData.granularRate != null &&
                    queryData.granularRepair != null &&
                    RepairCostFactorReportColumn.repairExpensesFullDeviation in req.columns

            val granularRepairRubPlanUsed = req.granularity != null && queryData.granularRate != null &&
                    queryData.granularMileage != null && (RepairCostFactorReportColumn.repairRubPlan in req.columns ||
                    RepairCostFactorReportColumn.repairRubDeviation in req.columns)

            val granularRepairRubUsed = req.granularity != null && queryData.granularRepair != null &&
                    queryData.granularMileage != null && (RepairCostFactorReportColumn.repairRub in req.columns ||
                    RepairCostFactorReportColumn.repairRubDeviation in req.columns)

            val granularRepairRubDeviationUsed = req.granularity != null && queryData.granularRepair != null &&
                    queryData.granularMileage != null && queryData.granularRate != null &&
                    RepairCostFactorReportColumn.repairRubDeviation in req.columns

            val granularMileage = if (granularMileageUsed) {
                row[queryData.granularMileage!!]
                    .parseGranularity(req.granularity, req.from, req.to) {
                        val date = LocalDate.parse(get("f1").asText())
                        (produceGranularLabel(date, req.granularity!!) to (get("f2").asDouble()))
                    }
            } else null

            val granularRepairExpensesFullPlan = if (granularRepairExpensesFullPlanUsed) {
                row[queryData.granularRate!!]
                    .parseGranularity(req.granularity, req.from, req.to) {
                        val date = LocalDate.parse(get("f1").asText())
                        (produceGranularLabel(date, req.granularity!!) to (get("f2").asDouble()))
                    }
            } else null

            val granularRepairExpensesFull = if (granularRepairExpensesFullUsed) {
                row[queryData.granularRepair!!]
                    .parseGranularity(req.granularity, req.from, req.to) {
                        val date = LocalDate.parse(get("f1").asText())
                        (produceGranularLabel(date, req.granularity!!) to (get("f2").asDouble()))
                    }
            } else null

            val granularRepairExpensesFullDeviation = if (granularRepairExpensesFullDeviationUsed) {
                calculateGranularity(
                    granularRepairExpensesFull,
                    granularRepairExpensesFullPlan
                ) { repair, rate -> rate?.let { repair?.minus(it) } }
            } else null

            val granularRepairRubPlan = if (granularRepairRubPlanUsed) {
                calculateGranularity(
                    granularRepairExpensesFullPlan,
                    granularMileage
                ) { rate, mileage -> if (mileage != null && mileage != 0.0) rate?.div(mileage) else null }
            } else null

            val granularRepairRub = if (granularRepairRubUsed) {
                calculateGranularity(
                    granularRepairExpensesFull,
                    granularMileage
                ) { repair, mileage -> if (mileage != null && mileage != 0.0) repair?.div(mileage) else null }
            } else null

            val granularRepairRubDeviation = if (granularRepairRubDeviationUsed) {
                calculateGranularity(
                    granularRepairRub,
                    granularRepairRubPlan
                ) { fact, plan -> plan?.let { fact?.minus(it) } }
            } else null

            RepairCostFactorReportItem(
                ter = if (RepairCostFactorReportColumn.ter in req.columns) row[queryData.columns[RepairCostFactorReportColumn.ter] as ExpressionAlias<String?>] else null,
                mr = if (RepairCostFactorReportColumn.mr in req.columns) row[queryData.columns[RepairCostFactorReportColumn.mr] as ExpressionAlias<String?>] else null,
                atp = if (RepairCostFactorReportColumn.atp in req.columns) row[queryData.columns[RepairCostFactorReportColumn.atp] as ExpressionAlias<String?>] else null,
                mvz = if (RepairCostFactorReportColumn.mvz in req.columns) row[queryData.columns[RepairCostFactorReportColumn.mvz] as ExpressionAlias<String?>] else null,
                mvzName = if (RepairCostFactorReportColumn.mvzName in req.columns) row[queryData.columns[RepairCostFactorReportColumn.mvzName] as ExpressionAlias<String?>] else null,
                atpType = if (RepairCostFactorReportColumn.atpType in req.columns) row[queryData.columns[RepairCostFactorReportColumn.atpType] as ExpressionAlias<String?>] else null,
                mvzType = if (RepairCostFactorReportColumn.mvzType in req.columns) row[queryData.columns[RepairCostFactorReportColumn.mvzType] as ExpressionAlias<String?>] else null,
                vehicleLicense = if (RepairCostFactorReportColumn.vehicleLicense in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleLicense] as ExpressionAlias<String?>] else null,
                vehicleGroup = if (RepairCostFactorReportColumn.vehicleGroup in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleGroup] as ExpressionAlias<String?>] else null,
                vehicleType = if (RepairCostFactorReportColumn.vehicleType in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleType] as ExpressionAlias<String?>] else null,
                vehicleBrand = if (RepairCostFactorReportColumn.vehicleBrand in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleBrand] as ExpressionAlias<String?>] else null,
                vehicleModel = if (RepairCostFactorReportColumn.vehicleModel in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleModel] as ExpressionAlias<String?>] else null,
                vehicleTonnage = if (RepairCostFactorReportColumn.vehicleTonnage in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleTonnage] as ExpressionAlias<Double?>] else null,
                vehicleYear = if (RepairCostFactorReportColumn.vehicleYear in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleYear] as ExpressionAlias<Int?>] else null,
                vehicleCreateDate = if (RepairCostFactorReportColumn.vehicleCreateDate in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleCreateDate] as ExpressionAlias<LocalDate?>] else null,
                vehicleVin = if (RepairCostFactorReportColumn.vehicleVin in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vehicleVin] as ExpressionAlias<String?>] else null,
                equnr = if (RepairCostFactorReportColumn.equnr in req.columns) row[queryData.columns[RepairCostFactorReportColumn.equnr] as ExpressionAlias<String?>] else null,
                gbo = if (RepairCostFactorReportColumn.gbo in req.columns) row[queryData.columns[RepairCostFactorReportColumn.gbo] as ExpressionAlias<String?>] else null,
                compartAmount = if (RepairCostFactorReportColumn.compartAmount in req.columns) row[queryData.columns[RepairCostFactorReportColumn.compartAmount] as ExpressionAlias<Int?>] else null,
                trailerLicenseNum = if (RepairCostFactorReportColumn.trailerLicenseNum in req.columns) row[queryData.columns[RepairCostFactorReportColumn.trailerLicenseNum] as ExpressionAlias<String?>] else null,
                repairExpensesFull = if (RepairCostFactorReportColumn.repairExpensesFull in req.columns) row[queryData.repairExpensesFullAlias as ExpressionAlias<Double?>] else null,
                mileage = if (RepairCostFactorReportColumn.mileage in req.columns) row[queryData.mileageAlias as ExpressionAlias<Double?>] else null,
                repairRub = if (RepairCostFactorReportColumn.repairRub in req.columns) row[queryData.repairRubAlias as ExpressionAlias<Double?>] else null,
                structureName = if (RepairCostFactorReportColumn.structureName in req.columns) row[queryData.columns[RepairCostFactorReportColumn.structureName] as ExpressionAlias<String?>] else null,
                repairPlace = if (RepairCostFactorReportColumn.repairPlace in req.columns) row[queryData.columns[RepairCostFactorReportColumn.repairPlace] as ExpressionAlias<String?>] else null,
                reqType = if (RepairCostFactorReportColumn.reqType in req.columns) row[queryData.columns[RepairCostFactorReportColumn.reqType] as ExpressionAlias<String?>] else null,
                reqSubtype = if (RepairCostFactorReportColumn.reqSubtype in req.columns) row[queryData.columns[RepairCostFactorReportColumn.reqSubtype] as ExpressionAlias<String?>] else null,
                vrt = if (RepairCostFactorReportColumn.vrt in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vrt] as ExpressionAlias<String?>] else null,
                vrtName = if (RepairCostFactorReportColumn.vrtName in req.columns) row[queryData.columns[RepairCostFactorReportColumn.vrtName] as ExpressionAlias<String?>] else null,
                eventId = if (RepairCostFactorReportColumn.eventId in req.columns) row[queryData.columns[RepairCostFactorReportColumn.eventId] as ExpressionAlias<String?>] else null,
                eventText = if (RepairCostFactorReportColumn.eventText in req.columns) row[queryData.columns[RepairCostFactorReportColumn.eventText] as ExpressionAlias<String?>] else null,
                repairExpensesFullPlan = if (RepairCostFactorReportColumn.repairExpensesFullPlan in req.columns) row[queryData.repairExpensesFullPlanAlias as ExpressionAlias<Double?>] else null,
                repairExpensesFullDeviation = if (RepairCostFactorReportColumn.repairExpensesFullDeviation in req.columns) row[queryData.repairExpensesFullDeviationAlias as ExpressionAlias<Double?>] else null,
                repairRubPlan = if (RepairCostFactorReportColumn.repairRubPlan in req.columns) row[queryData.repairRubPlanAlias as ExpressionAlias<Double?>] else null,
                repairRubDeviation = if (RepairCostFactorReportColumn.repairRubDeviation in req.columns) row[queryData.repairRubDeviationAlias as ExpressionAlias<Double?>] else null,
                granularityRepairExpensesFullPlan = if (RepairCostFactorReportColumn.repairExpensesFullPlan in req.columns && req.granularity != null) granularRepairExpensesFullPlan else null,
                granularityRepairExpensesFull = if (RepairCostFactorReportColumn.repairExpensesFull in req.columns && req.granularity != null) granularRepairExpensesFull else null,
                granularityRepairExpensesFullDeviation = if (RepairCostFactorReportColumn.repairExpensesFullDeviation in req.columns && req.granularity != null) granularRepairExpensesFullDeviation else null,
                granularityRepairRubPlan = if (RepairCostFactorReportColumn.repairRubPlan in req.columns && req.granularity != null) granularRepairRubPlan else null,
                granularityRepairRub = if (RepairCostFactorReportColumn.repairRub in req.columns && req.granularity != null) granularRepairRub else null,
                granularityRepairRubDeviation = if (RepairCostFactorReportColumn.repairRubDeviation in req.columns && req.granularity != null) granularRepairRubDeviation else null
            )
        }

        RepairCostFactorReportResp(
            count = count?.toInt() ?: 0,
            pageNumber = req.pageNumber ?: 0,
            pageSize = req.pageSize ?: 0,
            totalRepairExpensesFull = totalRepairExpensesFull ?: 0.0,
            totalRepairRubFact = totalRepairRubFact ?: 0.0,
            items = items
        )
    }

    private fun prepareQueries(
        req: RepairCostFactorReportReq,
        totals: Boolean = true,
        strictFilters: Boolean = true
    ): RepairCostFactorQueryData {
        val resolver = QueryDataResolver(req, strictFilters)

        val (sourceQuery, groupings) = resolver.prepareQuery()

        val mainQueryFilters = req.filters.filter { it.name.aggregated }

        val count = CountAll().alias("count")

        val columnsToSelect = (
                groupings.values.filterNotNull()
                ) + listOfNotNull(
            resolver.granularMileage,
            resolver.granularRate,
            resolver.granularRepair,
            resolver.mileageAlias.takeIf { RepairCostFactorReportColumn.mileage in req.columns },
            resolver.repairExpensesFullPlanAlias.takeIf { RepairCostFactorReportColumn.repairExpensesFullPlan in req.columns },
            resolver.repairRubPlanAlias.takeIf { RepairCostFactorReportColumn.repairRubPlan in req.columns },
            resolver.repairExpensesFullAlias.takeIf { RepairCostFactorReportColumn.repairExpensesFull in req.columns },
            resolver.repairRubAlias.takeIf { RepairCostFactorReportColumn.repairRub in req.columns },
            resolver.repairExpensesFullDeviationAlias.takeIf { RepairCostFactorReportColumn.repairExpensesFullDeviation in req.columns },
            resolver.repairRubDeviationAlias.takeIf { RepairCostFactorReportColumn.repairRubDeviation in req.columns },
            count.takeIf { totals },
            resolver.totalRepairExpensesFullAlias,
            resolver.totalRepairRubAlias,
        )

        val query = sourceQuery.select(
            columnsToSelect
        ).apply {
            mainQueryFilters.forEach {
                val exp = when (it.name) {
                    RepairCostFactorReportColumn.mileage -> resolver.mileageAlias.delegate
                    RepairCostFactorReportColumn.repairExpensesFull -> resolver.repairExpensesFullAlias.delegate
                    RepairCostFactorReportColumn.repairExpensesFullPlan -> resolver.repairExpensesFullPlanAlias?.delegate
                    RepairCostFactorReportColumn.repairRub -> resolver.repairRubAlias.delegate
                    RepairCostFactorReportColumn.repairRubPlan -> resolver.repairRubPlanAlias!!.delegate
                    RepairCostFactorReportColumn.repairRubDeviation -> resolver.repairRubDeviationAlias!!.delegate
                    else -> resolver.repairExpensesFullDeviationAlias!!.delegate
                }

                andWhere {
                    buildFilterPredicate(
                        condition = it.condition,
                        values = it.value,
                        type = it.name.type,
                        exposedExpression = exp!!
                    )
                }
            }
        }.apply {
            if (req.pageSize != null && req.pageNumber != null) {
                limit(
                    n = req.pageSize,
                    offset = (req.pageNumber * req.pageSize).toLong()
                )
            }
        }.apply {
            req.sort.forEach { sort ->
                val exp = when (sort.column) {
                    RepairCostFactorReportColumn.mileage -> resolver.mileageAlias.aliasOnlyExpression()
                    RepairCostFactorReportColumn.repairExpensesFullPlan -> resolver.repairExpensesFullPlanAlias!!.aliasOnlyExpression()
                    RepairCostFactorReportColumn.repairExpensesFull -> resolver.repairExpensesFullAlias.aliasOnlyExpression()
                    RepairCostFactorReportColumn.repairRub -> resolver.repairRubAlias.aliasOnlyExpression()
                    RepairCostFactorReportColumn.repairRubPlan -> resolver.repairRubPlanAlias!!.aliasOnlyExpression()
                    RepairCostFactorReportColumn.repairRubDeviation -> resolver.repairRubDeviationAlias!!.aliasOnlyExpression()
                    RepairCostFactorReportColumn.repairExpensesFullDeviation -> resolver.repairExpensesFullDeviationAlias!!.aliasOnlyExpression()
                    else -> groupings[sort.column]?.aliasOnlyExpression()
                }

                exp?.let { orderBy(exp, if (sort.asc) SortOrder.ASC else SortOrder.DESC) }
            }
        }.also { logger.debug(it.prepareSQL(QueryBuilder(false))) }

        return RepairCostFactorQueryData(
            query = query,
            countAlias = count,
            totalRepairExpensesFullAlias = resolver.totalRepairExpensesFullAlias,
            totalRepairRubFactAlias = resolver.totalRepairRubAlias,
            columns = groupings,
            mileageAlias = resolver.mileageAlias,
            repairExpensesFullPlanAlias = resolver.repairExpensesFullPlanAlias,
            repairRubPlanAlias = resolver.repairRubPlanAlias,
            repairExpensesFullAlias = resolver.repairExpensesFullAlias,
            repairRubAlias = resolver.repairRubAlias,
            repairExpensesFullDeviationAlias = resolver.repairExpensesFullDeviationAlias,
            repairRubDeviationAlias = resolver.repairRubDeviationAlias,
            granularMileage = resolver.granularMileage,
            granularRate = resolver.granularRate,
            granularRepair = resolver.granularRepair,
        )
    }

    private fun calculateGranularity(
        list1: List<GranularityItem>?,
        list2: List<GranularityItem>?,
        operation: (Double?, Double?) -> Double?
    ): List<GranularityItem>? {
        return list1?.map {
            val val2 = list2?.find { l2 ->
                l2.label == it.label
            }?.value as Double?

            GranularityItem(
                label = it.label,
                value = operation(it.value as Double?, val2),
                partPeriod = it.partPeriod
            )
        }
    }

    private class QueryData(
        val query: QueryAlias,
        val columns: Map<RepairCostFactorReportColumn, ExpressionAlias<*>?>?,
        val exp: ExpressionAlias<Double?>?,
        val granularExp: ExpressionAlias<String>?
    )

    private class QueryDataResolver(
        val req: RepairCostFactorReportReq,
        strictFilters: Boolean
    ) {
        val userColumns = (req.columns + req.filters.map { it.name }).distinct()

        val repairExpensesFullPlanUsed = userColumns.any {
            it in setOf(
                RepairCostFactorReportColumn.repairExpensesFullPlan,
                RepairCostFactorReportColumn.repairRubPlan,
                RepairCostFactorReportColumn.repairExpensesFullDeviation,
                RepairCostFactorReportColumn.repairRubDeviation
            )
        }

        val repairRubPlanUsed = userColumns.any {
            it in setOf(
                RepairCostFactorReportColumn.repairRubPlan,
                RepairCostFactorReportColumn.repairRubDeviation
            )
        }

        val repairExpensesFullDeviationUsed = RepairCostFactorReportColumn.repairExpensesFullDeviation in userColumns

        val repairRubDeviationUsed = RepairCostFactorReportColumn.repairRubDeviation in userColumns

        val orgUnitColumns = hashSetOf(
            RepairCostFactorReportColumn.ter,
            RepairCostFactorReportColumn.mr,
            RepairCostFactorReportColumn.atp,
            RepairCostFactorReportColumn.atpType,
            RepairCostFactorReportColumn.mvzType,
        )

        val mileageColumns = hashSetOf(
            RepairCostFactorReportColumn.mileage,
            RepairCostFactorReportColumn.repairRubPlan,
            RepairCostFactorReportColumn.repairRub,
            RepairCostFactorReportColumn.repairRubDeviation,
            RepairCostFactorReportColumn.vehicleLicense,
            RepairCostFactorReportColumn.vehicleGroup,
            RepairCostFactorReportColumn.vehicleType,
            RepairCostFactorReportColumn.vehicleBrand,
            RepairCostFactorReportColumn.vehicleModel,
            RepairCostFactorReportColumn.vehicleTonnage,
            RepairCostFactorReportColumn.vehicleYear,
            RepairCostFactorReportColumn.vehicleCreateDate,
            RepairCostFactorReportColumn.vehicleVin,
            RepairCostFactorReportColumn.equnr,
            RepairCostFactorReportColumn.gbo,
            RepairCostFactorReportColumn.compartAmount,
            RepairCostFactorReportColumn.mvz,
            RepairCostFactorReportColumn.mvzName,
            RepairCostFactorReportColumn.mvzType,
            RepairCostFactorReportColumn.ter,
            RepairCostFactorReportColumn.mr,
            RepairCostFactorReportColumn.atp,
            RepairCostFactorReportColumn.atpType,
            RepairCostFactorReportColumn.trailerLicenseNum,
        )

        val rateColumns = hashSetOf(
            RepairCostFactorReportColumn.repairExpensesFullPlan,
            RepairCostFactorReportColumn.repairRubPlan,
            RepairCostFactorReportColumn.repairExpensesFullDeviation,
            RepairCostFactorReportColumn.repairRubDeviation,
            RepairCostFactorReportColumn.vehicleLicense,
            RepairCostFactorReportColumn.vehicleGroup,
            RepairCostFactorReportColumn.vehicleType,
            RepairCostFactorReportColumn.vehicleBrand,
            RepairCostFactorReportColumn.vehicleModel,
            RepairCostFactorReportColumn.vehicleTonnage,
            RepairCostFactorReportColumn.vehicleYear,
            RepairCostFactorReportColumn.vehicleCreateDate,
            RepairCostFactorReportColumn.vehicleVin,
            RepairCostFactorReportColumn.equnr,
            RepairCostFactorReportColumn.gbo,
            RepairCostFactorReportColumn.compartAmount,
            RepairCostFactorReportColumn.mvz,
            RepairCostFactorReportColumn.mvzName,
            RepairCostFactorReportColumn.mvzType,
            RepairCostFactorReportColumn.ter,
            RepairCostFactorReportColumn.mr,
            RepairCostFactorReportColumn.atp,
            RepairCostFactorReportColumn.atpType,
            RepairCostFactorReportColumn.trailerLicenseNum,
            RepairCostFactorReportColumn.reqType,
            RepairCostFactorReportColumn.reqSubtype,
            RepairCostFactorReportColumn.structureName,
            RepairCostFactorReportColumn.vrt,
            RepairCostFactorReportColumn.vrtName
        )

        val repairColumns = hashSetOf(
            RepairCostFactorReportColumn.repairExpensesFull,
            RepairCostFactorReportColumn.repairRub,
            RepairCostFactorReportColumn.repairExpensesFullDeviation,
            RepairCostFactorReportColumn.repairRubDeviation,
            RepairCostFactorReportColumn.reqType,
            RepairCostFactorReportColumn.reqSubtype,
            RepairCostFactorReportColumn.structureName,
            RepairCostFactorReportColumn.repairPlace,
            RepairCostFactorReportColumn.vrt,
            RepairCostFactorReportColumn.vrtName,
            RepairCostFactorReportColumn.eventId,
            RepairCostFactorReportColumn.eventText,
            RepairCostFactorReportColumn.trailerLicenseNum,
            RepairCostFactorReportColumn.vehicleLicense,
            RepairCostFactorReportColumn.vehicleGroup,
            RepairCostFactorReportColumn.vehicleType,
            RepairCostFactorReportColumn.vehicleBrand,
            RepairCostFactorReportColumn.vehicleModel,
            RepairCostFactorReportColumn.vehicleTonnage,
            RepairCostFactorReportColumn.vehicleYear,
            RepairCostFactorReportColumn.vehicleCreateDate,
            RepairCostFactorReportColumn.vehicleVin,
            RepairCostFactorReportColumn.equnr,
            RepairCostFactorReportColumn.gbo,
            RepairCostFactorReportColumn.compartAmount,
            RepairCostFactorReportColumn.mvz,
            RepairCostFactorReportColumn.mvzName,
            RepairCostFactorReportColumn.mvzType,
            RepairCostFactorReportColumn.ter,
            RepairCostFactorReportColumn.mr,
            RepairCostFactorReportColumn.atp,
            RepairCostFactorReportColumn.atpType
        )

        val orgUnitColumnsSelected = orgUnitColumns.any { req.columns.contains(it) } || !req.geoFilter.containsOnlyMvz

        val repairTypeColumns = userColumns.filter {
            it in listOf(RepairCostFactorReportColumn.reqSubtype, RepairCostFactorReportColumn.reqType)
        }

        private val plData = getMileageData(req, strictFilters)
        private val repairData = getRepairStructureData(req, strictFilters)
        private val rateData = getRateData(req, strictFilters)

        val mileageAlias = plData.query[plData.exp!!].alias("mileage")

        val repairExpensesFullPlanAlias = if (repairExpensesFullPlanUsed) {
            rateData.query[rateData.exp!!].alias("repairExpensesFullPlan")
        } else null

        val repairRubPlanAlias = if (repairRubPlanUsed) {
            (rateData.query[rateData.exp!!] / plData.query[plData.exp!!]).alias("repairRubPlan")
        } else null

        val repairExpensesFullAlias = repairData.query[repairData.exp!!].alias("repairExpensesFull")
        val totalRepairExpensesFullAlias = SumOver(repairData.query[repairData.exp!!]).alias("totalRepairExpensesFull")

        val repairRubAlias = (repairData.query[repairData.exp!!] / plData.query[plData.exp!!]).alias("repairRub")
        val totalRepairRubAlias = SumOver(repairData.query[repairData.exp!!] / plData.query[plData.exp!!])
            .alias("totalRepairRub")

        val repairExpensesFullDeviationAlias = if (repairExpensesFullDeviationUsed) {
            (repairExpensesFullAlias.delegate - repairExpensesFullPlanAlias!!.delegate)
                .alias("repairExpensesFullDeviation")
        } else null

        val repairRubDeviationAlias = if (repairRubDeviationUsed) {
            (repairRubAlias.delegate - repairRubPlanAlias!!.delegate).alias("repairRubDeviation")
        } else null

        val granularMileage = if (req.granularity != null && plData.granularExp != null) {
            plData.query[plData.granularExp].alias("granularMileage")
        } else null

        val granularRepair = if (req.granularity != null && repairData.granularExp != null) {
            repairData.query[repairData.granularExp].alias("granularRepair")
        } else null

        val granularRate = if (req.granularity != null && rateData.granularExp != null) {
            rateData.query[rateData.granularExp].alias("granularRate")
        } else null

        fun prepareQuery(): Pair<ColumnSet, Map<RepairCostFactorReportColumn, ExpressionAlias<*>?>> {
            val allSourcesUsed = repairExpensesFullPlanUsed || repairRubPlanUsed

            val joinToRateCondition = mutableListOf<Op<Boolean>>()
            val joinToRepairCondition = mutableListOf<Op<Boolean>>()

            val groupingsAliases = userColumns.filterNot {
                it.aggregated
            }.associate { column ->
                val plExpAlias = plData.columns?.get(column)?.let { plData.query[it] }
                val rateExpAlias = rateData.columns?.get(column)?.let { rateData.query[it] }
                val repairExpAlias = repairData.columns?.get(column)?.let { repairData.query[it] }

                when {
                    allSourcesUsed && plExpAlias != null && rateExpAlias != null && repairExpAlias != null -> {
                        //also generate conditions to join
                        joinToRateCondition.add(
                            plExpAlias eq rateExpAlias
                        )

                        joinToRepairCondition.add(
                            plExpAlias eq repairExpAlias
                        )

                        column to Coalesce(
                            plExpAlias as ExpressionWithColumnType,
                            rateExpAlias,
                            repairExpAlias,
                        ).alias(column.name)
                    }

                    allSourcesUsed && rateExpAlias != null && repairExpAlias != null -> {
                        joinToRepairCondition.add(
                            rateExpAlias eq repairExpAlias
                        )

                        column to when {
                            column in repairTypeColumns ->
                                Coalesce(
                                    repairExpAlias as ExpressionWithColumnType,
                                    rateExpAlias,
                                    stringLiteral("-")
                                )

                            else -> Coalesce(
                                repairExpAlias as ExpressionWithColumnType,
                                rateExpAlias
                            )
                        }.alias(column.name)
                    }

                    !allSourcesUsed && plExpAlias != null && repairExpAlias != null -> {
                        joinToRepairCondition.add(
                            plExpAlias eq repairExpAlias
                        )

                        column to Coalesce(
                            plExpAlias as ExpressionWithColumnType,
                            repairExpAlias,
                        ).alias(column.name)
                    }

                    repairExpAlias != null -> column to repairExpAlias.alias(column.name)

                    else -> throw IllegalArgumentException("Unsupported column: $column")
                }
            }

            val sourceQuery =
                when {
                    allSourcesUsed ->
                        plData.query.join(
                            otherTable = rateData.query,
                            joinType = if (joinToRateCondition.isEmpty()) JoinType.CROSS else JoinType.FULL,
                            additionalConstraint = {
                                joinToRateCondition.reduce { acc, expr -> acc and expr }
                            }
                        ).join(
                            otherTable = repairData.query,
                            joinType = if (joinToRepairCondition.isEmpty()) JoinType.CROSS else JoinType.FULL,
                            additionalConstraint = {
                                joinToRepairCondition.reduce { acc, expr -> acc and expr }
                            }
                        )

                    else ->
                        plData.query.join(
                            otherTable = repairData.query,
                            joinType = if (joinToRepairCondition.isEmpty()) JoinType.CROSS else JoinType.FULL,
                            additionalConstraint = {
                                joinToRepairCondition.reduce { acc, expr -> acc and expr }
                            }
                        )
                }

            return sourceQuery to groupingsAliases
        }

        private fun getRepairStructureData(
            req: RepairCostFactorReportReq,
            strictFilters: Boolean
        ): QueryData {
            val filters = req.filters.filter { repairColumns.contains(it.name) && !it.name.aggregated }

            val repairExpensesFullAlias = Sum(
                Coalesce(RepairStructures.structureExpense, doubleLiteral(0.0)),
                DoubleColumnType()
            ).alias("repairExpensesFull")

            var sourceQuery = when {
                orgUnitColumnsSelected -> RepairStructures.join(
                    otherTable = OrganizationalUnitsTimelineTable,
                    joinType = JoinType.LEFT,
                    additionalConstraint = {
                        RepairStructures.vehicleMvz eq OrganizationalUnitsTimelineTable.mvzId and
                                (RepairStructures.repairStartDate greaterEq OrganizationalUnitsTimelineTable.startDate) and
                                (RepairStructures.repairStartDate less OrganizationalUnitsTimelineTable.endDate)
                    }
                )

                else -> RepairStructures
            }

            if (userColumns.containsAll(repairTypeColumns)) {
                sourceQuery = sourceQuery
                    .join(ToroWorksTable, JoinType.LEFT, RepairStructures.vrt, ToroWorksTable.id)
                    .join(ToroWorksSubtypesTable, JoinType.LEFT, ToroWorksTable.subtypeId, ToroWorksSubtypesTable.id)
                    .join(ToroWorksTypesTable, JoinType.LEFT, ToroWorksTable.typeId, ToroWorksTypesTable.id)
            } else if (RepairCostFactorReportColumn.reqSubtype in userColumns) {
                sourceQuery = sourceQuery
                    .join(ToroWorksTable, JoinType.LEFT, RepairStructures.vrt, ToroWorksTable.id)
                    .join(ToroWorksSubtypesTable, JoinType.LEFT, ToroWorksTable.subtypeId, ToroWorksSubtypesTable.id)
            } else if (RepairCostFactorReportColumn.reqType in userColumns) {
                sourceQuery = sourceQuery
                    .join(ToroWorksTable, JoinType.LEFT, RepairStructures.vrt, ToroWorksTable.id)
                    .join(ToroWorksTypesTable, JoinType.LEFT, ToroWorksTable.typeId, ToroWorksTypesTable.id)
            }

            val subQueryColumns = repairColumns.filter {
                userColumns.contains(it) && !it.aggregated
            }.associateWith { it.getRepairAlias() ?: it.getAlias() }

            val (granularExpAlias, granularStartDay) = run {
                when {
                    req.granularity != null -> {
                        val data = GranularitySubQueryData(
                            req.granularity,
                            repairExpensesFullAlias.aliasOnlyExpression(),
                            req.from,
                            req.to
                        )

                        sourceQuery = sourceQuery.join(
                            otherTable = data.daysQuery,
                            joinType = JoinType.INNER,
                            additionalConstraint = {
                                RepairStructures.repairStartDate greaterEq data.daysQuery[data.startDay] and
                                        (RepairStructures.repairStartDate less data.daysQuery[data.endDay])
                            })

                        data.granularExp.alias("repairExpensesFull_granularity") to data.daysQuery[data.startDay]
                    }

                    else -> null to null
                }
            }

            val subQuery = sourceQuery
                .select(
                    subQueryColumns.values.filterNotNull() + listOfNotNull(
                        repairExpensesFullAlias, granularStartDay
                    )
                ).where {
                    RepairStructures.repairStartDate.between(req.from, req.to)
                }.apply {
                    if (orgUnitColumnsSelected) {
                        this.applyGeoFilter(req.geoFilter)
                    }
                }.apply {
                    req.geoFilter.mvz?.let { andWhere { RepairStructures.vehicleMvz inList it } }

                    filters.forEach {
                        andWhere {
                            buildFilterPredicate(
                                condition = it.condition,
                                values = it.value,
                                type = it.name.type,
                                strictFilter = strictFilters,
                                exposedExpression = it.name.getRepairAlias()?.delegate ?: it.name.getAlias()!!.delegate
                            )
                        }
                    }
                }.groupBy(*(subQueryColumns.values.filterNotNull() + listOfNotNull(granularStartDay)).toTypedArray())
                .alias(if (req.granularity != null) "granular_repair" else "repair")

            val totalRepairExpensesFullAlias = if (req.granularity != null) {
                Sum(subQuery[repairExpensesFullAlias], DoubleColumnType()).alias("repairExpensesFullPlan")
            } else null

            val columns = subQueryColumns.filterNot {
                it.value == null
            }.map { (k, v) -> k to subQuery[v!!].alias(k.name) }.toMap()

            val query = when {
                req.granularity != null ->
                    subQuery.select(
                        columns.values +
                                listOfNotNull(totalRepairExpensesFullAlias, granularExpAlias)
                    ).groupBy(*columns.values.toTypedArray())
                        .alias("repair")

                else -> subQuery
            }

            return QueryData(
                query = query,
                columns = if (req.granularity != null) columns else subQueryColumns,
                exp = totalRepairExpensesFullAlias ?: repairExpensesFullAlias,
                granularExp = granularExpAlias
            )
        }

        private fun getRateData(
            req: RepairCostFactorReportReq,
            strictFilters: Boolean
        ): QueryData {
            val subQueryColumns = rateColumns.filter {
                userColumns.contains(it) && !it.aggregated
            }.associateWith { it.getRateAlias() }

            val filters = req.filters.filter { rateColumns.contains(it.name) && !it.name.aggregated }

            val repairExpensesFullPlanAlias = if (repairExpensesFullPlanUsed) {
                Sum(
                    Coalesce((PlsByDayWithRate.mileage * PlsByDayWithRate.rate), doubleLiteral(0.0)),
                    DoubleColumnType()
                ).alias("repairExpensesFullPlan")
            } else null

            var sourceQuery = when {
                orgUnitColumnsSelected -> PlsByDayWithRate.join(
                    otherTable = OrganizationalUnitsTimelineTable,
                    joinType = JoinType.LEFT,
                    additionalConstraint = {
                        PlsByDayWithRate.mvz eq OrganizationalUnitsTimelineTable.mvzId and
                                (PlsByDayWithRate.vehicleDate greaterEq OrganizationalUnitsTimelineTable.startDate) and
                                (PlsByDayWithRate.vehicleDate less OrganizationalUnitsTimelineTable.endDate)
                    }
                )

                else -> PlsByDayWithRate
            }

            val (granularExpAlias, granularStartDay) = run {
                when {
                    req.granularity != null && repairExpensesFullPlanAlias != null -> {
                        val data = GranularitySubQueryData(
                            req.granularity,
                            repairExpensesFullPlanAlias.aliasOnlyExpression(),
                            req.from,
                            req.to
                        )

                        sourceQuery = sourceQuery.join(
                            otherTable = data.daysQuery,
                            joinType = JoinType.INNER,
                            additionalConstraint = {
                                PlsByDayWithRate.vehicleDate greaterEq data.daysQuery[data.startDay] and
                                        (PlsByDayWithRate.vehicleDate less data.daysQuery[data.endDay])
                            })

                        data.granularExp.alias("repairExpensesFullPlan_granularity") to data.daysQuery[data.startDay]
                    }

                    else -> null to null
                }
            }

            val subQuery = sourceQuery
                .select(
                    subQueryColumns.values.filterNotNull() + listOfNotNull(
                        repairExpensesFullPlanAlias, granularStartDay
                    )
                ).where {
                    PlsByDayWithRate.vehicleDate.between(req.from, req.to)
                }.apply {
                    if (orgUnitColumnsSelected) {
                        this.applyGeoFilter(req.geoFilter)
                    }
                }.apply {
                    req.geoFilter.mvz?.let { andWhere { PlsByDayWithRate.mvz inList it } }

                    filters.forEach {
                        andWhere {
                            buildFilterPredicate(
                                condition = it.condition,
                                values = it.value,
                                type = it.name.type,
                                strictFilter = strictFilters,
                                exposedExpression = it.name.getRateAlias()!!.delegate
                            )
                        }
                    }
                }.groupBy(*(subQueryColumns.values.filterNotNull() + listOfNotNull(granularStartDay)).toTypedArray())
                .alias(if (req.granularity != null) "granular_rate" else "rate")

            val totalRepairExpensesFullPlanAlias = if (repairExpensesFullPlanUsed && req.granularity != null) {
                Sum(subQuery[repairExpensesFullPlanAlias!!], DoubleColumnType()).alias("repairExpensesFullPlan")
            } else null

            val columns = subQueryColumns.filterNot {
                it.value == null
            }.map { (k, v) -> k to subQuery[v!!].alias(k.name) }.toMap()

            val query = when {
                req.granularity != null ->
                    subQuery.select(
                        columns.values +
                                listOfNotNull(totalRepairExpensesFullPlanAlias, granularExpAlias)
                    ).groupBy(*columns.values.toTypedArray())
                        .alias("rate")

                else -> subQuery
            }

            return QueryData(
                query = query,
                columns = if (req.granularity != null) columns else subQueryColumns,
                exp = totalRepairExpensesFullPlanAlias ?: repairExpensesFullPlanAlias,
                granularExp = granularExpAlias
            )
        }

        private fun getMileageData(
            req: RepairCostFactorReportReq,
            strictFilters: Boolean
        ): QueryData {
            val subQueryColumns = mileageColumns.filter {
                userColumns.contains(it) && !it.aggregated
            }.associateWith { it.getPlAlias() }

            val filters = req.filters.filter { mileageColumns.contains(it.name) && !it.name.aggregated }

            val mileageAlias =
                Sum(Coalesce(PlsByDay.mileage, doubleLiteral(0.0)), DoubleColumnType()).alias("mileage")

            var sourceQuery = when {
                orgUnitColumnsSelected -> PlsByDay.join(
                    otherTable = OrganizationalUnitsTimelineTable,
                    joinType = JoinType.LEFT,
                    additionalConstraint = {
                        PlsByDay.mvz eq OrganizationalUnitsTimelineTable.mvzId and
                                (PlsByDay.vehicleDate greaterEq OrganizationalUnitsTimelineTable.startDate) and
                                (PlsByDay.vehicleDate less OrganizationalUnitsTimelineTable.endDate)
                    }
                )

                else -> PlsByDay
            }

            val (granularExpAlias, granularStartDay) = run {
                when {
                    req.granularity != null -> {
                        val data = GranularitySubQueryData(
                            req.granularity,
                            mileageAlias.aliasOnlyExpression(),
                            req.from,
                            req.to
                        )

                        sourceQuery = sourceQuery.join(
                            otherTable = data.daysQuery,
                            joinType = JoinType.INNER,
                            additionalConstraint = {
                                PlsByDay.vehicleDate greaterEq data.daysQuery[data.startDay] and
                                        (PlsByDay.vehicleDate less data.daysQuery[data.endDay])
                            })

                        data.granularExp.alias("mileage_granularity") to data.daysQuery[data.startDay]
                    }

                    else -> null to null
                }
            }

            val subQuery = sourceQuery
                .select(
                    subQueryColumns.values.filterNotNull() + listOfNotNull(mileageAlias, granularStartDay)
                ).where {
                    PlsByDay.vehicleDate.between(req.from, req.to)
                }.apply {
                    if (orgUnitColumnsSelected) {
                        this.applyGeoFilter(req.geoFilter)
                    }
                }.apply {
                    req.geoFilter.mvz?.let { andWhere { PlsByDay.mvz inList it } }

                    filters.forEach {
                        andWhere {
                            buildFilterPredicate(
                                condition = it.condition,
                                values = it.value,
                                type = it.name.type,
                                strictFilter = strictFilters,
                                exposedExpression = it.name.getPlAlias()!!.delegate
                            )
                        }
                    }
                }.groupBy(*(subQueryColumns.values.filterNotNull() + listOfNotNull(granularStartDay)).toTypedArray())
                .alias(if (req.granularity != null) "granular_mileage" else "mileage")

            val totalMileageAlias = if (req.granularity != null) {
                Sum(subQuery[mileageAlias], DoubleColumnType()).alias("mileage")
            } else null

            val columns = subQueryColumns.filterNot {
                it.value == null
            }.map { (k, v) -> k to subQuery[v!!].alias(k.name) }.toMap()

            val query = when {
                req.granularity != null ->
                    subQuery.select(
                        columns.values + listOfNotNull(totalMileageAlias, granularExpAlias)
                    ).groupBy(*columns.values.toTypedArray())
                        .alias("mileage")

                else -> subQuery
            }

            return QueryData(
                query = query,
                columns = if (req.granularity != null) columns else subQueryColumns,
                exp = totalMileageAlias ?: mileageAlias,
                granularExp = granularExpAlias
            )
        }
    }

    private class RepairCostFactorQueryData(
        val query: Query,
        val countAlias: ExpressionAlias<Long>?,
        val totalRepairExpensesFullAlias: ExpressionAlias<Double>?,
        val totalRepairRubFactAlias: ExpressionAlias<Double>?,
        val columns: Map<RepairCostFactorReportColumn, ExpressionAlias<*>?>,
        val mileageAlias: ExpressionAlias<Double?>?,
        val repairExpensesFullPlanAlias: ExpressionAlias<Double?>?,
        val repairRubPlanAlias: ExpressionAlias<Double?>?,
        val repairExpensesFullAlias: ExpressionAlias<Double?>?,
        val repairRubAlias: ExpressionAlias<Double?>?,
        val repairExpensesFullDeviationAlias: ExpressionAlias<Double?>?,
        val repairRubDeviationAlias: ExpressionAlias<Double?>?,
        val granularMileage: ExpressionAlias<String>?,
        val granularRate: ExpressionAlias<String>?,
        val granularRepair: ExpressionAlias<String>?
    )
}