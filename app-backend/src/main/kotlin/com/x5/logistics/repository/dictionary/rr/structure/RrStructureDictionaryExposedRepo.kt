package com.x5.logistics.repository.dictionary.rr.structure

import com.x5.logistics.data.dictionary.org.AtpEntity
import com.x5.logistics.data.dictionary.org.AtpTable
import com.x5.logistics.data.dictionary.rr.Month
import com.x5.logistics.data.dictionary.rr.structure.RrStructureDictionary
import com.x5.logistics.data.dictionary.rr.structure.RrStructureDictionaryView
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.castToInt
import com.x5.logistics.rest.dto.dictionary.DictionaryInfoDto
import com.x5.logistics.rest.dto.dictionary.rr.RrImportResponse
import com.x5.logistics.rest.dto.dictionary.rr.structure.AtpDto
import com.x5.logistics.rest.dto.dictionary.rr.structure.GetAtpResp
import com.x5.logistics.rest.dto.dictionary.rr.structure.RepairType
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryItem
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryItemDetails
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryItemValue
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryListReqWithYearList
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryResp
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryRow
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryUpdateReq
import com.x5.logistics.service.dictionary.DictionaryUpdateInfo
import com.x5.logistics.service.streamWorkbook
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.moscowDateTime
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.LiteralOp
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andHaving
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.javatime.JavaInstantColumnType
import org.jetbrains.exposed.sql.not
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.sum
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.union
import org.jetbrains.exposed.sql.upsert
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Component
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.math.RoundingMode
import java.net.URLEncoder
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*
import java.util.zip.ZipException

@Component
class RrStructureDictionaryExposedRepo : DictionaryUpdateInfo {
    private val sheetName = "Доля рубкм ремонтов"
    val logger = getLogger()

    fun getDictionary(req: RrStructureDictionaryListReq): RrStructureDictionaryResp {
        val (items, count) = getData(
            req = RrStructureDictionaryListReqWithYearList(
                pageSize = req.pageSize,
                pageNumber = req.pageNumber,
                sort = req.sort,
                filters = req.filters,
                year = listOf(req.year)
            ),
            total = true
        )

        return RrStructureDictionaryResp(
            count = count ?: 0,
            pageSize = req.pageSize,
            pageNumber = req.pageNumber,
            items = items,
            year = req.year
        )
    }

    fun getData(
        req: RrStructureDictionaryListReqWithYearList,
        total: Boolean = false
    ): Pair<List<RrStructureDictionaryItem>, Int?> = transaction {
        val countAlias = RrStructureDictionaryView.atpId.count().over().alias("count").takeIf { total }
        var count: Int? = null

        val atpQuery = with(RrStructureDictionaryView) {
            select(listOfNotNull(atpId, name, countAlias))
                .where { year inList req.year }
                .apply {
                    req.filters.forEach { filter ->
                        andWhere {
                            buildFilterPredicate(
                                condition = filter.condition,
                                values = filter.value,
                                type = filter.name.type,
                                exposedExpression = filter.name.expression,
                                strictFilter = true
                            )
                        }
                    }
                }.withDistinct(true)
                .groupBy(atpId, name)
                .apply {
                    when {
                        req.sort.isEmpty() -> orderBy(name)
                        else -> req.sort.forEach { sort ->
                            orderBy(sort.column.expression, if (sort.asc) SortOrder.ASC else SortOrder.DESC)
                        }
                    }
                }
                .limit(req.pageSize, (req.pageNumber * req.pageSize).toLong())
        }.alias("atp")

        val items = with(RrStructureDictionaryView) {
            join(atpQuery, JoinType.INNER, atpId, atpQuery[atpId])
                .select(
                    listOfNotNull(
                        atpId, type, year, month, name, updatedBy, updatedAt, rate,
                        countAlias?.let { atpQuery[it] }
                    )
                )
                .where { year inList req.year }
                .apply {
                    req.filters.forEach { filter ->
                        andWhere {
                            buildFilterPredicate(
                                condition = filter.condition,
                                values = filter.value,
                                type = filter.name.type,
                                exposedExpression = filter.name.expression,
                                strictFilter = true
                            )
                        }
                    }
                }.apply {
                    when {
                        req.sort.isEmpty() -> orderBy(name)
                        else -> req.sort.forEach { sort ->
                            orderBy(sort.column.expression, if (sort.asc) SortOrder.ASC else SortOrder.DESC)
                        }
                    }
                }
                .also { logger.debug(it.prepareSQL(QueryBuilder(false))) }
                .map { row ->
                    if (count == null && countAlias != null) count = row[atpQuery[countAlias]].toInt()

                    RrStructureDictionaryRow(
                        atpId = row[atpId].value,
                        structureType = row[type],
                        year = row[year],
                        month = row[month],
                        name = row[name],
                        updatedBy = row[updatedBy],
                        updatedAt = row[updatedAt],
                        rate = row[rate]
                    )
                }
                .groupBy { it.year to it.atpId }
                .map { (pair, atpDetails) ->
                    val lastUpdatedByAtp = atpDetails.filterNot { it.updatedAt == null }
                        .maxByOrNull { it.updatedAt!! }?.updatedBy
                    RrStructureDictionaryItem(
                        atpId = pair.second,
                        atpName = atpDetails.first().name,
                        updatedBy = lastUpdatedByAtp,
                        year = pair.first,
                        details = atpDetails.groupBy { it.structureType }
                            .map { (type, details) ->
                                val lastUpdatedByType = details.filterNot { it.updatedAt == null }
                                    .maxByOrNull { it.updatedAt!! }?.updatedBy
                                RrStructureDictionaryItemDetails(
                                    name = type,
                                    label = RepairType.entries.first { it.name == type }.label,
                                    updatedBy = lastUpdatedByType,
                                    values = details.map { detail ->
                                        RrStructureDictionaryItemValue(
                                            month = Month.entries.first { it.name == detail.month.uppercase() },
                                            value = detail.rate
                                        )
                                    },
                                )
                            }
                    )
                }
        }

        items to count
    }

    fun updateRates(
        req: RrStructureDictionaryUpdateReq,
        username: String
    ) = transaction {
        req.details.forEach { structure ->
            structure.values.forEach { value ->
                val updateCondition = RrStructureDictionary.atpId eq req.atpId and
                        (RrStructureDictionary.year eq req.year) and
                        (RrStructureDictionary.structureType eq structure.name) and
                        (RrStructureDictionary.month eq value.month.name.uppercase())

                val keys = sequenceOf(
                    RrStructureDictionary.atpId,
                    RrStructureDictionary.structureType,
                    RrStructureDictionary.year,
                    RrStructureDictionary.month
                ).toList().toTypedArray()

                val now = LocalDateTime.now()

                val onUpdateList = listOf(
                    RrStructureDictionary.rate to doubleLiteral(value.value ?: 0.0),
                    RrStructureDictionary.updatedBy to stringLiteral(username),
                    RrStructureDictionary.updatedAt to LiteralOp(
                        JavaInstantColumnType(),
                        now.atZone(ZoneId.systemDefault()).toInstant()
                    )
                )

                val onUpdateExcludeList = sequenceOf(
                    RrStructureDictionary.atpId,
                    RrStructureDictionary.structureType,
                    RrStructureDictionary.year,
                    RrStructureDictionary.month,
                    RrStructureDictionary.createdBy,
                    RrStructureDictionary.createdAt,
                    RrStructureDictionary.startDate,
                    RrStructureDictionary.endDate
                ).toList()

                val startDateValue = LocalDate.of(req.year, value.month.ordinal + 1, 1)
                RrStructureDictionary.upsert(
                    keys = keys,
                    onUpdate = onUpdateList,
                    onUpdateExclude = onUpdateExcludeList,
                    where = { updateCondition }) {
                    it[atpId] = req.atpId
                    it[structureType] = structure.name
                    it[year] = req.year
                    it[month] = value.month.name.uppercase()
                    it[rate] = value.value
                    it[createdBy] = username
                    it[updatedBy] = username
                    it[createdAt] = now
                    it[updatedAt] = now
                    it[startDate] = startDateValue
                    it[endDate] = startDateValue.plusMonths(1)
                }
            }
        }
    }

    fun getAtp(): GetAtpResp = transaction {
        GetAtpResp(
            AtpEntity.find { AtpTable.deleted eq false }
                .orderBy(AtpTable.name to SortOrder.ASC)
                .map { AtpDto(atpId = it.id.value, atpName = it.name) })
    }

    fun checkNew(): List<String> = transaction {
        val idQuery = AtpTable.select(AtpTable.id).where {
            AtpTable.id notInSubQuery (RrStructureDictionary.select(RrStructureDictionary.atpId)) and
                    (AtpTable.deleted eq false)
        }

        val structureIdQuery = with(RrStructureDictionary) {
            select(atpId).where {
                month eq LocalDateTime.now().plusMonths(1).month.name.uppercase()
            }.groupBy(atpId)
                .andHaving {
                    not(rate.sum().castToInt() eq 100)
                }
        }

        AtpTable.select(AtpTable.name).where {
            AtpTable.id inSubQuery (structureIdQuery.union(idQuery))
        }.withDistinct(true)
            .also { logger.debug(it.prepareSQL(QueryBuilder(false))) }
            .map { it[AtpTable.name] }
    }

    fun export(
        req: RrStructureDictionaryListReqWithYearList,
        forImport: Boolean = false
    ): ResponseEntity<Resource> {
        val totalCount = getData(req.copy(pageSize = 1, pageNumber = 0)).first.size

        val out = streamWorkbook {
            val headerStyle = ExcelStyles.Header.style(this)
            val generalStyle = ExcelStyles.General.style(this)
            val percentStyle = ExcelStyles.RoundedPercent.style(this)
            style {
                alignment = HorizontalAlignment.CENTER
                verticalAlignment = VerticalAlignment.CENTER
            }
            sheet(sheetName) {
                header {
                    currStyle = headerStyle
                    head("АТП")
                    head("Год")
                    head("Месяц")
                    head(RepairType.ourWorkshopServices.label)
                    head(RepairType.extWorkshopServices.label)
                    head(RepairType.parts.label)
                    head(RepairType.tires.label)
                    if (!forImport) {
                        head("Сумма")
                        head("Пользователь")
                    }
                }
                for (pageNumber in 0..(totalCount / EXPORT_BATCH_SIZE)) {
                    val data = getData(req.copy(pageNumber = pageNumber, pageSize = EXPORT_BATCH_SIZE))
                    data.first.groupBy { it.year }.forEach { (year, items) ->
                        items.forEach { item ->
                            Month.entries.forEach { month ->
                                row {
                                    currStyle = generalStyle
                                    cell(item.atpName)
                                    cell(year)
                                    cell(month.ordinal + 1)

                                    currStyle = percentStyle
                                    val ourWorkshopRate =
                                        item.details.find { it.name == RepairType.ourWorkshopServices.name }
                                            ?.values?.find { it.month == month }?.value?.div(100)
                                    val extWorkshopRate =
                                        item.details.find { it.name == RepairType.extWorkshopServices.name }
                                            ?.values?.find { it.month == month }?.value?.div(100)
                                    val partsRate = item.details.find { it.name == RepairType.parts.name }?.values
                                        ?.find { it.month == month }?.value?.div(100)
                                    val tiresRate = item.details.find { it.name == RepairType.tires.name }?.values
                                        ?.find { it.month == month }?.value?.div(100)
                                    cell(ourWorkshopRate)
                                    cell(extWorkshopRate)
                                    cell(partsRate)
                                    cell(tiresRate)
                                    if (!forImport) {
                                        cell(
                                            if ((ourWorkshopRate ?: 0.0) + (extWorkshopRate ?: 0.0) +
                                                (tiresRate ?: 0.0) + (partsRate ?: 0.0) == 0.0
                                            ) {
                                                null
                                            } else {
                                                (ourWorkshopRate ?: 0.0) + (extWorkshopRate ?: 0.0) +
                                                        (tiresRate ?: 0.0) + (partsRate ?: 0.0)
                                            }
                                        )
                                        currStyle = generalStyle
                                        cell(item.updatedBy)
                                    }
                                }
                            }
                        }
                    }
                    (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
                }
            }.autosize()
        }.toInputStream()

        val fileName = "Структура тарифа ремонт ${moscowDateTime()}.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(out))
    }

    fun import(body: InputStream, author: String): RrImportResponse {
        val workbook = try {
            XSSFWorkbook(body)
        } catch (e: IllegalArgumentException) {
            return RrImportResponse(dataReadingSuccessFlag = false, error = "Ошибка импорта данных. Невалидный файл")
        } catch (e: ZipException) {
            return RrImportResponse(
                dataReadingSuccessFlag = false,
                error = "Ошибка импорта данных. Формат файла не соответствует требуемому: .xls или .xlsx"
            )
        }
        val sheet = workbook.getSheet(sheetName)
            ?: return RrImportResponse(
                dataReadingSuccessFlag = false,
                error = "Ошибка импорта данных. Лист \"Доля рубкм ремонтов\" не найден"
            )

        val headRow = sheet.getRow(0)
        val sumColumn = 7
        val statusColumn = 8
        val headerStyle = workbook.createCellStyle().apply {
            setFont(
                workbook.createFont().apply {
                    bold = true
                }
            )
        }

        val percentStyle = workbook.createCellStyle().apply {
            dataFormat = workbook.createDataFormat().getFormat("0%")
        }

        headRow.rowStyle = headerStyle
        headRow.createCell(sumColumn).apply {
            cellStyle = headerStyle
            setCellValue("Сумма")
        }
        headRow.createCell(statusColumn).apply {
            cellStyle = headerStyle
            setCellValue("Результат")
        }
        val headers = mutableMapOf<String, Int?>(
            "АТП" to null,
            "Год" to null,
            "Месяц" to null,
            RepairType.ourWorkshopServices.label to null,
            RepairType.extWorkshopServices.label to null,
            RepairType.parts.label to null,
            RepairType.tires.label to null,
        )
        for (idx in 0..6) {
            headRow.getCell(idx)?.stringCellValue?.let { headers[it] = idx }
        }
        val notFoundHeaders = headers.filter { it.value == null }.map { it.key }
        if (notFoundHeaders.isNotEmpty()) {
            return RrImportResponse(
                dataReadingSuccessFlag = false,
                error = "Ошибка импорта данных. Не найдены заголовки: ${notFoundHeaders.joinToString(", ")}"
            )
        }

        val totalRows = sheet.lastRowNum
        var errorsCount = 0
        val allErrors = mutableSetOf<String>()
        val allItems = mutableSetOf<DictStructureItem>()

        val atpList = getAtp().atp

        sheet.asSequence()
            .drop(1)
            .forEach { row ->
                logger.debug("process row: ${row.rowNum}")
                val errors = mutableSetOf<String>()

                val atpName = row.getCell(headers["АТП"]!!)?.stringCellValue ?: ""
                val year = try {
                    row.getCell(headers["Год"]!!)?.numericCellValue?.toInt()
                } catch (_: Exception) {
                    null
                }
                val month = try {
                    row.getCell(headers["Месяц"]!!)?.numericCellValue?.toInt()
                } catch (_: Exception) {
                    null
                }

                if (year == null || year < 2018 || year > (LocalDate.now().year + 1)) {
                    errors.add("Указан некорректный год.")
                }

                if (month !in 1..12) {
                    errors.add("Указан некорректный месяц.")
                    allErrors.addAll(errors)
                    writeErrors(row, errors)
                    errorsCount++
                    return@forEach
                }

                val atp = atpList.firstOrNull { it.atpName == atpName }

                if (atp == null) {
                    errors.add("АТП не найдено.")
                    allErrors.addAll(errors)
                    writeErrors(row, errors)
                    errorsCount++
                    return@forEach
                }

                val ourWorkshopServicesRate = try {
                    when {
                        row.getCell(headers[RepairType.ourWorkshopServices.label]!!).cellType == CellType.STRING &&
                                row.getCell(headers[RepairType.ourWorkshopServices.label]!!)?.stringCellValue == "" -> null

                        else -> row.getCell(headers[RepairType.ourWorkshopServices.label]!!)?.numericCellValue?.times(
                            100
                        )
                    }
                } catch (e: IllegalStateException) {
                    errors.add("Указано некорректное значение доли тарифа.")
                    null
                }

                val extWorkshopServicesRate = try {
                    when {
                        row.getCell(headers[RepairType.extWorkshopServices.label]!!).cellType == CellType.STRING &&
                                row.getCell(headers[RepairType.extWorkshopServices.label]!!)?.stringCellValue == "" -> null

                        else -> row.getCell(headers[RepairType.extWorkshopServices.label]!!)?.numericCellValue?.times(
                            100
                        )
                    }
                } catch (e: IllegalStateException) {
                    errors.add("Указано некорректное значение доли тарифа.")
                    null
                }

                val partsRate = try {
                    when {
                        row.getCell(headers[RepairType.parts.label]!!).cellType == CellType.STRING &&
                                row.getCell(headers[RepairType.parts.label]!!)?.stringCellValue == "" -> null

                        else -> row.getCell(headers[RepairType.parts.label]!!)?.numericCellValue?.times(100)
                    }
                } catch (e: IllegalStateException) {
                    errors.add("Указано некорректное значение доли тарифа.")
                    null
                }

                val tiresRate = try {
                    when {
                        row.getCell(headers[RepairType.tires.label]!!).cellType == CellType.STRING &&
                                row.getCell(headers[RepairType.tires.label]!!)?.stringCellValue == "" -> null

                        else -> row.getCell(headers[RepairType.tires.label]!!)?.numericCellValue?.times(100)
                    }
                } catch (e: IllegalStateException) {
                    errors.add("Указано некорректное значение доли тарифа.")
                    null
                }

                val sumRates = ((ourWorkshopServicesRate ?: 0.0) + (extWorkshopServicesRate ?: 0.0) +
                        (partsRate ?: 0.0) + (tiresRate ?: 0.0))
                    .toBigDecimal().setScale(1, RoundingMode.HALF_EVEN).toDouble()

                when {
                    sumRates > 100 -> errors.add("Контрольная сумма более 100.")
                    sumRates < 100 -> errors.add("Контрольная сумма менее 100.")
                }

                row.createCell(sumColumn).apply {
                    cellStyle = percentStyle
                    setCellValue(sumRates / 100)
                }

                val item = DictStructureItem(
                    atpName = atpName,
                    year = year!!,
                    month = month!!,
                    ourWorkshopServicesRate = ourWorkshopServicesRate,
                    extWorkshopServicesRate = extWorkshopServicesRate,
                    partsRate = partsRate,
                    tiresRate = tiresRate
                )

                if (!allItems.add(item)) {
                    errors.add("Дублирующая строка.")
                    allErrors.addAll(errors)
                    writeErrors(row, errors)
                    errorsCount++
                    return@forEach
                }

                if (errors.isNotEmpty()) {
                    allErrors.addAll(errors)
                    writeErrors(row, errors)
                    errorsCount++
                    return@forEach
                }

                updateRates(
                    req = RrStructureDictionaryUpdateReq(
                        atpId = atp.atpId!!,
                        year = year,
                        details = listOf(
                            RrStructureDictionaryItemDetails(
                                name = RepairType.ourWorkshopServices.name,
                                label = RepairType.ourWorkshopServices.label,
                                updatedBy = author,
                                values = listOf(
                                    RrStructureDictionaryItemValue(
                                        month = Month.entries[month - 1],
                                        value = ourWorkshopServicesRate
                                    )
                                )
                            ),
                            RrStructureDictionaryItemDetails(
                                name = RepairType.extWorkshopServices.name,
                                label = RepairType.extWorkshopServices.label,
                                updatedBy = author,
                                values = listOf(
                                    RrStructureDictionaryItemValue(
                                        month = Month.entries[month - 1],
                                        value = extWorkshopServicesRate
                                    )
                                )
                            ),
                            RrStructureDictionaryItemDetails(
                                name = RepairType.parts.name,
                                label = RepairType.parts.label,
                                updatedBy = author,
                                values = listOf(
                                    RrStructureDictionaryItemValue(
                                        month = Month.entries[month - 1],
                                        value = partsRate
                                    )
                                )
                            ),
                            RrStructureDictionaryItemDetails(
                                name = RepairType.tires.name,
                                label = RepairType.tires.label,
                                updatedBy = author,
                                values = listOf(
                                    RrStructureDictionaryItemValue(
                                        month = Month.entries[month - 1],
                                        value = tiresRate
                                    )
                                )
                            )
                        )
                    ),
                    username = author
                )

                row.createCell(statusColumn).setCellValue("Данные импортированы успешно.")
            }

        sheet.autoSizeColumn(sumColumn)
        sheet.autoSizeColumn(statusColumn)

        return RrImportResponse(
            dataReadingSuccessFlag = true,
            totalRowsCount = totalRows.toLong(),
            errorsCount = errorsCount.toLong(),
            error = if (allErrors.isNotEmpty()) joinErrors(allErrors) else null,
            body = Base64.getEncoder().encodeToString(
                ByteArrayOutputStream().also { workbook.write(it) }.toByteArray()
            )
        )
    }

    override fun getLastUpdateInfo(): DictionaryInfoDto = transaction {
        val query = with(RrStructureDictionary) {
            select(updatedAt, updatedBy).where {
                not(updatedBy eq "system")
            }.orderBy(updatedAt, SortOrder.DESC).limit(1)
        }.singleOrNull()

        DictionaryInfoDto(
            name = "rr_structure_dictionary",
            updatedBy = query?.get(RrStructureDictionary.updatedBy),
            updatedAt = query?.get(RrStructureDictionary.updatedAt),
        )
    }

    private fun writeErrors(row: Row, errors: Set<String>, idx: Int = 8) {
        row.createCell(idx).setCellValue(joinErrors(errors))
    }

    private fun joinErrors(errors: Set<String>) = "Импорт не выполнен. ${errors.joinToString("\n ")}"

    private data class DictStructureItem(
        val atpName: String,
        val year: Int,
        val month: Int,
        val ourWorkshopServicesRate: Double?,
        val extWorkshopServicesRate: Double?,
        val partsRate: Double?,
        val tiresRate: Double?
    )
}
