package com.x5.logistics.repository.dictionary.hrplaces

import com.x5.logistics.data.dictionary.hrplaces.HrPlaceDao
import com.x5.logistics.rest.dto.dictionary.DictionaryInfoDto
import com.x5.logistics.service.dictionary.DictionaryUpdateInfo
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import jakarta.persistence.Tuple
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import org.springframework.stereotype.Service
import java.sql.Timestamp

@Repository
interface HrPlacesRepo: JpaRepository<HrPlaceDao, Long>, HrPlacesRepoCustom

interface HrPlacesRepoCustom {
    fun <T> getByJpa(jpaFunction: EntityManager.() -> T): T
}

@Service
class HrPlacesRepoCustomImpl : HrPlacesRepoCustom, DictionaryUpdateInfo {
    @PersistenceContext
    private lateinit var entityManager: EntityManager

    override fun <T> getByJpa(jpaFunction: EntityManager.() -> T): T =
        entityManager.jpaFunction()

    override fun getLastUpdateInfo(): DictionaryInfoDto {
        val tupleRes = (entityManager.createNativeQuery(
            """
                (select updated_at as updatedat, updated_by as author
                 from hr_places
                 where updated_by != 'system'
                 order by updated_at desc nulls last 
                 limit 1)
                union
                (select created_at as updatedat, created_by as author
                 from hr_places
                 where created_by != 'system'
                 order by created_at desc nulls last 
                 limit 1)
                order by updatedat desc nulls last 
                limit 1
            """.trimIndent(), Tuple::class.java
        ).resultList as List<Tuple>).firstOrNull()
        return DictionaryInfoDto(
            name = "hr",
            updatedBy = (tupleRes?.get("author") as String?),
            updatedAt = (tupleRes?.get("updatedat") as Timestamp?)?.toLocalDateTime(),
        )
    }
}