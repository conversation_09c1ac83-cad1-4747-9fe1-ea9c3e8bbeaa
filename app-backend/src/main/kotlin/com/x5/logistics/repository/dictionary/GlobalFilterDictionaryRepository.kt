package com.x5.logistics.repository.dictionary

import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.ParentLabeledValue
import com.x5.logistics.rest.dto.dictionary.GlobalFilterDictionaryValuesDto
import com.x5.logistics.util.getLogger
import jakarta.persistence.EntityManager
import jakarta.persistence.Tuple
import org.springframework.stereotype.Repository

@Repository
class GlobalFilterDictionaryRepository(val em: EntityManager) {
    val log = getLogger()

    private val mrQuery = """
        select distinct id as values, name as labels from ts.macro_region
        order by name
    """.trimIndent()

    private val atpQuery = """
        select distinct id as values, name as labels from ts.atp
        order by name
    """.trimIndent()

    private val mvzQuery = """
        select uid as values, name as labels from ts.mvz_codes
        order by name
    """.trimIndent()

    private val retailNetworkQuery = """
        select distinct retail_network as values, retail_network as labels from atp
        where retail_network is not null
        order by retail_network
    """.trimIndent()

    private val atpTypeQuery = """
        select distinct type as values, type as labels from atp
        where type is not null
        order by type
    """.trimIndent()

    private val mvzTypeQuery = """
        select distinct type as values, type as labels from ts.mvz_codes
        where type is not null
        order by type
    """.trimIndent()

    private val territoryQuery = """
        select id as values, name as labels from ts.territory
    """.trimIndent()

    fun getGlobalFilterDictionary(): GlobalFilterDictionaryValuesDto =
        GlobalFilterDictionaryValuesDto(
            mr = ParentLabeledValue(
                label = "Макрорегион",
                value = "",
                children = getValues(mrQuery).map { it.toLabeledValue() }.toMutableList()
            ),
            atp = ParentLabeledValue(
                label = "АТП",
                value = "",
                children = getValues(atpQuery).map { it.toLabeledValue() }.toMutableList()
            ),
            mvz = ParentLabeledValue(
                label = "МВЗ",
                value = "",
                children = getValues(mvzQuery).map { it.toLabeledValue() }.toMutableList()
            ),
            retailNetwork = ParentLabeledValue(
                label = "Торговая сеть АТП",
                value = "",
                children = getValues(retailNetworkQuery).map { it.toLabeledValue() }.toMutableList()
            ),
            atpType = ParentLabeledValue(
                label = "Вид деятельности АТП ∙ ВД",
                value = "",
                children = getValues(atpTypeQuery).map { it.toLabeledValue() }.toMutableList()
            ),
            mvzType = ParentLabeledValue(
                label = "Тип МВЗ",
                value = "",
                children = getValues(mvzTypeQuery).map { it.toLabeledValue() }.toMutableList()
            ),
            territory = ParentLabeledValue(
                label = "Территория",
                value = "",
                children = getValues(territoryQuery).map { it.toLabeledValue() }.toMutableList()
            )
        )

    @Suppress("UNCHECKED_CAST")
    private fun getValues(query: String): List<Tuple> =
        em.createNativeQuery(query, Tuple::class.java).resultList as List<Tuple>

    private fun Tuple.toLabeledValue(): LabeledValue =
        LabeledValue(
            label = prepareLabel(this["labels"]?.toString()),
            value = this["values"]
        )

    private fun prepareLabel(value: String?): String =
        when (value) {
            null, "null" -> "Не указано"
            "нет" -> "Не указано"
            else -> value
        }
}
