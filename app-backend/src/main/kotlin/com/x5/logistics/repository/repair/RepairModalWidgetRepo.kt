package com.x5.logistics.repository.repair

import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksSubtypesTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksTable
import com.x5.logistics.data.repair.PlsByDayGroup
import com.x5.logistics.data.repair.RepairRatesView
import com.x5.logistics.data.repair.RepairStructures
import com.x5.logistics.repository.CountFilteredDistinct
import com.x5.logistics.repository.SumFiltered
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.times
import com.x5.logistics.rest.dto.repair.modal.RepairModalWidgetReq
import com.x5.logistics.rest.dto.repair.modal.RepairType
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.addLogger
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.sum
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class RepairModalWidgetRepo {
    suspend fun getOtherTypes(req: RepairModalWidgetReq, type: RepairType): List<Pair<String, Double>> = newSuspendedTransaction {
        val subType = ToroWorksSubtypesTable.name
        val exp = RepairStructures.structureExpense.sum()
        val typeFilter = when (type) {
            RepairType.PARTS -> RepairStructures.structureName eq "Запчасти"
            RepairType.OUR_WORKSHOP_SERVICES -> RepairStructures.structureName eq "Ремзона"
            RepairType.EXT_WORKSHOP_SERVICES -> RepairStructures.structureName eq "СТО"
            RepairType.TOTAL,
            RepairType.TIRES -> throw IllegalArgumentException("${type.name} has it's own func")
        }
        RepairStructures
            .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                (OrganizationalUnitsTimelineTable.mvzId eq RepairStructures.mvzId) and
                        (OrganizationalUnitsTimelineTable.startDate lessEq RepairStructures.startDate) and
                        (RepairStructures.startDate less OrganizationalUnitsTimelineTable.endDate)
            }
            .join(ToroWorksTable, JoinType.INNER) {
                RepairStructures.vrt eq ToroWorksTable.id
            }
            .join(ToroWorksSubtypesTable, JoinType.INNER) {
                ToroWorksTable.subtypeId eq ToroWorksSubtypesTable.id
            }
            .select(subType, exp)
            .where { RepairStructures.startDate.between(req.from, req.to) and typeFilter }
            .applyGeoFilter(req.geoFilter)
            .groupBy(subType)
            .orderBy(exp, SortOrder.DESC_NULLS_LAST)
            .map {
                (it[subType]) to (it[exp] ?: 0.0)
            }
    }

    suspend fun getTypeTires(req: RepairModalWidgetReq): List<Pair<String, Double>> = newSuspendedTransaction {
        val tires = RepairStructures.tiresExpenses.sum()
        val tireParts = RepairStructures.tiresPartsExpenses.sum()
        val tireServices = RepairStructures.tiresServicesExpenses.sum()

        RepairStructures
            .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                (OrganizationalUnitsTimelineTable.mvzId eq RepairStructures.mvzId) and
                        (OrganizationalUnitsTimelineTable.startDate lessEq RepairStructures.startDate) and
                        (RepairStructures.startDate less OrganizationalUnitsTimelineTable.endDate)
            }
            .select(tires, tireParts, tireServices)
            .where { RepairStructures.startDate.between(req.from, req.to) }
            .applyGeoFilter(req.geoFilter)
            .single()
            .let {
                listOf(
                    "Шины" to (it[tires] ?: 0.0),
                    "Сопут. материалы" to (it[tireParts] ?: 0.0),
                    "Работы по ШМ" to (it[tireServices] ?: 0.0),
                )
            }
    }

    suspend fun getTypeTotal(req: RepairModalWidgetReq): List<Pair<String, Double>> = newSuspendedTransaction {
        val parts = SumFiltered(
            RepairStructures.structureExpense,
            RepairStructures.structureName eq "Запчасти",
            DoubleColumnType()
        )
        val tires = SumFiltered(
            RepairStructures.structureExpense,
            RepairStructures.structureName eq "Шины",
            DoubleColumnType()
        )
        val ourWorkshopServices = SumFiltered(
            RepairStructures.structureExpense,
            RepairStructures.structureName eq "Ремзона",
            DoubleColumnType()
        )
        val extWorkshopServices = SumFiltered(
            RepairStructures.structureExpense,
            RepairStructures.structureName eq "СТО",
            DoubleColumnType()
        )

        RepairStructures
            .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                (OrganizationalUnitsTimelineTable.mvzId eq RepairStructures.mvzId) and
                        (OrganizationalUnitsTimelineTable.startDate lessEq RepairStructures.startDate) and
                        (RepairStructures.startDate less OrganizationalUnitsTimelineTable.endDate)
            }
            .select(parts, tires, ourWorkshopServices, extWorkshopServices)
            .where { RepairStructures.startDate.between(req.from, req.to) }
            .applyGeoFilter(req.geoFilter)
            .single()
            .let {
                listOf(
                    "Запчасти" to (it[parts] ?: 0.0),
                    "Шины" to (it[tires] ?: 0.0),
                    "Ремзона" to (it[ourWorkshopServices] ?: 0.0),
                    "СТО" to (it[extWorkshopServices] ?: 0.0),
                )
            }
    }

    suspend fun getTotalTariffs(req: RepairModalWidgetReq): Double =
        getTariffs(req, null, false).sumOf { it.second }

    suspend fun getTotalMainTariffs(req: RepairModalWidgetReq): Double =
        getTariffs(req, VehicleGroup.MAIN, false).sumOf { it.second }

    suspend fun getTotalSubTariffs(req: RepairModalWidgetReq): Double =
        getTariffs(req, VehicleGroup.SUB, false).sumOf { it.second }

    suspend fun getMainTariffs(req: RepairModalWidgetReq): Map<Vehicle, Double> =
        getTariffs(req, VehicleGroup.MAIN, true).filter { it.first != null }.associate { it.first!! to it.second }

    suspend fun getSubTariffs(req: RepairModalWidgetReq): Map<Vehicle, Double> =
        getTariffs(req, VehicleGroup.SUB, true).filter { it.first != null }.associate { it.first!! to it.second }

    private suspend fun getTariffs(req: RepairModalWidgetReq, group: VehicleGroup?, grouped: Boolean): List<Pair<Vehicle?, Double>> =
        newSuspendedTransaction {
            addLogger(StdOutSqlLogger)
            val planExpenses =
                Sum(
                    PlsByDayGroup.mileage * Coalesce(RepairRatesView.rate, doubleLiteral(0.0)),
                    DoubleColumnType()
                )

            val typePredicate = when (req.repairType) {
                RepairType.TOTAL -> null
                RepairType.TIRES -> RepairRatesView.structureName eq "Шины"
                RepairType.PARTS -> RepairRatesView.structureName eq "Запчасти"
                RepairType.OUR_WORKSHOP_SERVICES -> RepairRatesView.structureName eq "Ремзона"
                RepairType.EXT_WORKSHOP_SERVICES -> RepairRatesView.structureName eq "СТО"
            }
            val groupPredicate = when (group) {
                VehicleGroup.MAIN -> PlsByDayGroup.tsGroup.inList(listOf("Основное", "Полуприцеп", "Прицеп"))
                VehicleGroup.SUB -> PlsByDayGroup.tsGroup.inList(listOf("Вспомогательное"))
                null -> null
            }
            val columns = if (grouped) {
                listOf(PlsByDayGroup.marka, PlsByDayGroup.plTonnage, planExpenses,)
            } else {
                listOf(planExpenses)
            }

            PlsByDayGroup
                .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                    (OrganizationalUnitsTimelineTable.mvzId eq PlsByDayGroup.mvzId) and
                            (OrganizationalUnitsTimelineTable.startDate lessEq PlsByDayGroup.vehicleDate) and
                            (PlsByDayGroup.vehicleDate less OrganizationalUnitsTimelineTable.endDate)
                }
                .join(RepairRatesView, JoinType.LEFT) {
                    OrganizationalUnitsTimelineTable.atpId eq RepairRatesView.atpId and
                            (RepairRatesView.startDate lessEq PlsByDayGroup.vehicleDate) and
                            (RepairRatesView.endDate greater PlsByDayGroup.vehicleDate) and
                            (RepairRatesView.tonnage eq PlsByDayGroup.plTonnage)
                }
                .select(columns)
                .where { PlsByDayGroup.vehicleDate.between(req.from, req.to) }
                .apply { 
                    typePredicate?.let { andWhere { it } }
                    groupPredicate?.let { andWhere { it } }
                }
                .applyGeoFilter(req.geoFilter)
                .apply {
                    if (grouped) {
                        groupBy(
                            PlsByDayGroup.marka,
                            PlsByDayGroup.plTonnage,
                        )
                    }
                }
                .map {
                    if (grouped) {
                        Vehicle(
                            marka = it[PlsByDayGroup.marka],
                            tonnage = it[PlsByDayGroup.plTonnage],
                        )
                    } else {
                       null 
                    } to (it[planExpenses] ?: 0.0)
                }
        }

    suspend fun getMainMileage(req: RepairModalWidgetReq): Map<Vehicle, Double> = newSuspendedTransaction {
        getMileage(req, VehicleGroup.MAIN)
    }

    suspend fun getSubMileage(req: RepairModalWidgetReq): Map<Vehicle, Double> = newSuspendedTransaction {
        getMileage(req, VehicleGroup.SUB)
    }

    private suspend fun getMileage(req: RepairModalWidgetReq, group: VehicleGroup): Map<Vehicle, Double> =
        newSuspendedTransaction {
            val mileage = PlsByDayGroup.mileage.sum()
            val groupPredicate = when (group) {
                VehicleGroup.MAIN -> PlsByDayGroup.tsGroup.inList(listOf("Основное", "Полуприцеп", "Прицеп"))
                VehicleGroup.SUB -> PlsByDayGroup.tsGroup.inList(listOf("Вспомогательное"))
            }
            PlsByDayGroup
                .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                    (OrganizationalUnitsTimelineTable.mvzId eq PlsByDayGroup.mvzId) and
                            (OrganizationalUnitsTimelineTable.startDate lessEq PlsByDayGroup.vehicleDate) and
                            (PlsByDayGroup.vehicleDate less OrganizationalUnitsTimelineTable.endDate)
                }
                .select(
                    PlsByDayGroup.marka,
                    PlsByDayGroup.plTonnage,
                    mileage,
                )
                .where { PlsByDayGroup.vehicleDate.between(req.from, req.to) and groupPredicate }
                .applyGeoFilter(req.geoFilter)
                .groupBy(
                    PlsByDayGroup.marka,
                    PlsByDayGroup.plTonnage,
                ).associate {
                    val res = it[mileage]
                    Vehicle(
                        it[PlsByDayGroup.marka],
                        it[PlsByDayGroup.plTonnage]
                    ) to if (res == null || res == 0.0) 1.0 else res
                }
        }

    suspend fun getMainExpenses(req: RepairModalWidgetReq): Map<Vehicle, Expenses> = newSuspendedTransaction {
        getVehicleExpenses(req, VehicleGroup.MAIN)
    }

    suspend fun getSubExpenses(req: RepairModalWidgetReq): Map<Vehicle, Expenses> = newSuspendedTransaction {
        getVehicleExpenses(req, VehicleGroup.SUB)
    }

    private fun getVehicleExpenses(req: RepairModalWidgetReq, group: VehicleGroup): Map<Vehicle, Expenses> {
        val exp = RepairStructures.structureExpense.sum()
        val orders = CountFilteredDistinct(
            RepairStructures.orderId,
            RepairStructures.structureExpense greater doubleLiteral(100000.0)
        )
        val vehicles = CountFilteredDistinct(
            RepairStructures.equnr,
            RepairStructures.structureExpense greater doubleLiteral(0.0)
        )

        val typePredicate = when (req.repairType) {
            RepairType.TOTAL -> null
            RepairType.TIRES -> RepairStructures.structureName eq "Шины"
            RepairType.PARTS -> RepairStructures.structureName eq "Запчасти"
            RepairType.OUR_WORKSHOP_SERVICES -> RepairStructures.structureName eq "Ремзона"
            RepairType.EXT_WORKSHOP_SERVICES -> RepairStructures.structureName eq "СТО"
        }
        val groupPredicate = when (group) {
            VehicleGroup.MAIN -> RepairStructures.tsGroup.inList(listOf("Основное", "Полуприцеп", "Прицеп"))
            VehicleGroup.SUB -> RepairStructures.tsGroup.inList(listOf("Вспомогательное"))
        }
        return RepairStructures
            .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                (OrganizationalUnitsTimelineTable.mvzId eq RepairStructures.mvzId) and
                        (OrganizationalUnitsTimelineTable.startDate lessEq RepairStructures.startDate) and
                        (RepairStructures.startDate less OrganizationalUnitsTimelineTable.endDate)
            }
            .select(
                RepairStructures.marka,
                RepairStructures.tonnage,
                exp,
                orders,
                vehicles,
            )
            .where {
                RepairStructures.startDate.between(req.from, req.to) and
                        groupPredicate
            }
            .apply {
                if (typePredicate != null) {
                    andWhere { typePredicate }
                }
            }
            .applyGeoFilter(req.geoFilter)
            .groupBy(
                RepairStructures.marka,
                RepairStructures.tonnage,
            ).associate {
                Vehicle(
                    marka = it[RepairStructures.marka],
                    tonnage = it[RepairStructures.tonnage]
                ) to Expenses(
                    expenses = it[exp],
                    orders = it[orders],
                    vehicles = it[vehicles],
                )
            }
    }

    private enum class VehicleGroup { MAIN, SUB }

    data class Vehicle(
        val marka: String?,
        val tonnage: BigDecimal?,
    )

    data class Expenses(
        val expenses: Double?,
        val orders: Long?,
        val vehicles: Long?,
    )
}
