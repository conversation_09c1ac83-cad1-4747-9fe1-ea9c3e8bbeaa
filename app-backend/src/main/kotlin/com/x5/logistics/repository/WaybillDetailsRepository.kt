package com.x5.logistics.repository

import com.fasterxml.jackson.databind.ObjectMapper
import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.Pls
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.PagedDetailsResponse
import com.x5.logistics.rest.dto.waybill.WaybillFilterReq
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.atp
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.brand
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.commerce
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.commissioningDate
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.dateClose
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.dateCloseReference
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.dateOpen
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.driverNumber
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.eqUnit
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.gbo
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.labeledWB
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.labels
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.licenseNum
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.maintenance
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.mileage
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.model
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.mrName
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.mvz
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.mvzInWB
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.mvzName
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.mvzNameInWB
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.number
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.primaryFuel
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.secondaryFuel
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.status
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.terName
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.timeClose
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.timeCloseReference
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.timeOpen
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.tod
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.tonnage
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.trailerLicenseNum
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.transit
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.typeWB
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.vehicleGroup
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.vehicleType
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.vin
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.waybillQty
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn.year
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumnFilter
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsQtyReq
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsQtyResp
import com.x5.logistics.rest.dto.waybill.WaybillLabel
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.util.getLogger
import jakarta.persistence.EntityManager
import org.jetbrains.exposed.sql.AndOp
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.Count
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.ExpressionWithColumnType
import org.jetbrains.exposed.sql.FloatColumnType
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.NotOp
import org.jetbrains.exposed.sql.OrOp
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.booleanLiteral
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.selectAll
import org.springframework.stereotype.Repository
import java.sql.Date
import java.sql.Time
import java.time.LocalDate
import java.time.LocalTime

@Suppress("UNCHECKED_CAST")
@Repository
class WaybillDetailsRepository(
    val em: EntityManager
) {
    val log = getLogger()
    val mapper = ObjectMapper()

    @ExposedTransactional
    fun getWaybillDetailedData(req: WaybillItemDetailsQtyReq): PagedDetailsResponse<WaybillItemDetailsQtyResp> {
        val aliases = Aliases(req)

        val counter = aliases.counter
        val report = getDataQuery(req)
        val query = report
            .select(report.fields + counter)
            .applyFilters(req.filters.filter { it.name.isCalculated })
            .orderBy(counter)
            .apply {
                req.sort.forEach {
                    orderBy(
                        it.column.exposedExpression.aliasOnlyExpression(),
                        if (it.asc) SortOrder.ASC else SortOrder.DESC
                    )
                }
            }
            .limit(req.pageSize, (req.pageNumber * req.pageSize).toLong())
            .also { log.debug(it.prepareSQL(QueryBuilder(false))) }
        var count: Int? = null
        val result = query.map { row ->
            val labels = getLabels(
                row,
                aliases.remontFlagAgg,
                aliases.idleFlagAgg,
                aliases.noTripsFlagAgg,
                aliases.wrongPlMvzFlagAgg,
                aliases.badMileageFlagAgg,
                aliases.longTimeFlagAgg
            )
            count = row[counter].toInt()
            WaybillItemDetailsQtyResp(
                labels = labels,
                labeledWB = row[aliases.labeledWaybillQty.aliasOnlyExpression() as Expression<Int>],
                status = if (status in req.columns) row[status.getExpression<String?>()] else null,
                terName = if (terName in req.columns) row[terName.getExpression<String?>()] else null,
                mrName = if (mrName in req.columns) row[mrName.getExpression<String?>()] else null,
                number = if (number in req.columns) row[number.getExpression<String?>()] else null,
                dateOpen = if (dateOpen in req.columns) row[dateOpen.getExpression<Date>()].toString() else null,
                timeOpen = if (timeOpen in req.columns) row[timeOpen.getExpression<Time>()].toString() else null,
                dateClose = if (dateClose in req.columns) row[dateClose.getExpression<Date>()].toString() else null,
                timeClose = if (timeClose in req.columns) row[timeClose.getExpression<Time>()].toString() else null,
                dateCloseReference = if (dateCloseReference in req.columns) row[dateCloseReference.getExpression<LocalDate?>()]?.toString() else null,
                timeCloseReference = if (timeCloseReference in req.columns) row[timeCloseReference.getExpression<Time?>()]?.toString() else null,
                mvzInWB = if (mvzInWB in req.columns) row[mvzInWB.getExpression<String?>()] else null,
                mvzNameInWB = if (mvzNameInWB in req.columns) row[mvzNameInWB.getExpression<String?>()] else null,
                driverNumber = if (driverNumber in req.columns) row[driverNumber.getExpression<Long?>()] else null,
                driver = if (driverNumber in req.columns) row[driverNumber.getExpression<Long?>()] else null,
                brand = if (brand in req.columns) row[brand.getExpression<String?>()] else null,
                model = if (model in req.columns) row[model.getExpression<String?>()] else null,
                year = if (year in req.columns) row[year.getExpression<Int?>()]?.toShort() else null,
                tonnage = if (tonnage in req.columns) row[tonnage.getExpression<Double?>()]?.toFloat() else null,
                licenseNum = if (licenseNum in req.columns) row[licenseNum.getExpression<String?>()] else null,
                trailerLicenseNum = if (trailerLicenseNum in req.columns) row[trailerLicenseNum.getExpression<String?>()] else null,
                eqUnit = if (eqUnit in req.columns) row[eqUnit.getExpression<Long?>()] else null,
                commissioningDate = if (commissioningDate in req.columns) row[commissioningDate.getExpression<Int?>()]?.toInt() else null,
                vin = if (vin in req.columns) row[vin.getExpression<String?>()] else null,
                gbo = if (gbo in req.columns) row[gbo.getExpression<Boolean?>()] else null,
                primaryFuel = if (primaryFuel in req.columns) row[primaryFuel.getExpression<Float?>()] else null,
                secondaryFuel = if (secondaryFuel in req.columns) row[secondaryFuel.getExpression<Float?>()] else null,
                mileage = if (mileage in req.columns) row[mileage.getExpression<Double>()] else null,
                hours = if (WaybillItemDetailsColumn.hours in req.columns) row[WaybillItemDetailsColumn.hours.getExpression<Double?>()]?.toBigDecimal() else null,
                motoHours = if (WaybillItemDetailsColumn.motoHours in req.columns) row[WaybillItemDetailsColumn.motoHours.getExpression<Double>()] else null,
                vehicleGroup = if (vehicleGroup in req.columns) row[vehicleGroup.getExpression<String?>()] else null,
                vehicleType = if (vehicleType in req.columns) row[vehicleType.getExpression<String?>()] else null,
                atp = if (atp in req.columns) row[atp.getExpression<String?>()] else null,
                mvzName = if (mvzName in req.columns) row[mvzName.getExpression<String?>()] else null,
                mvz = if (mvz in req.columns) row[mvz.getExpression<String?>()] else null,
                waybillQty = if (waybillQty in req.columns) row[aliases.plQuant.aliasOnlyExpression()].toInt() else null,
                maintenance = if (maintenance in req.columns) row[maintenance.getExpression<Boolean?>()] else null,
                typeWB = if (typeWB in req.columns) row[typeWB.getExpression<String?>()] else null,
                tod = if (tod in req.columns) row[tod.getExpression<Boolean?>()] else null,
                commerce = if (commerce in req.columns) row[commerce.getExpression<Boolean?>()] else null,
                transit = if (transit in req.columns) row[transit.getExpression<Boolean?>()] else null
            )
        }
        return PagedDetailsResponse(
            count = count ?: 0,
            pageNumber = req.pageNumber,
            pageSize = req.pageSize,
            actualStartDate = req.from,
            actualEndDate = req.to,
            totalLastDay = 0,
            totalAvg = 0f,
            items = result
        )
    }

    private fun getLabels(
        row: ResultRow,
        remontFlagAgg: ExpressionAlias<Boolean>,
        idleFlagAgg: ExpressionAlias<Boolean>,
        noTripsFlagAgg: ExpressionAlias<Boolean>,
        wrongPlMvzFlagAgg: ExpressionAlias<Boolean>,
        badMileageFlagAgg: ExpressionAlias<Boolean>,
        longTimeFlagAgg: ExpressionAlias<Boolean>
    ): List<WaybillLabel> {
        return listOfNotNull(
            if (row[remontFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.REPAIR_POINT else null,
            if (row[idleFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.DOWNTIME else null,
            if (row[noTripsFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.NO_TRIP else null,
            if (row[wrongPlMvzFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.MVZ_MISMATCH else null,
            if (row[badMileageFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.BAD_MILEAGE else null,
            if (row[longTimeFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.LONG_TIME else null,
        )
    }

    private fun getLabelsSet(
        row: ResultRow,
        remontFlagAgg: ExpressionAlias<Boolean>,
        idleFlagAgg: ExpressionAlias<Boolean>,
        noTripsFlagAgg: ExpressionAlias<Boolean>,
        wrongPlMvzFlagAgg: ExpressionAlias<Boolean>,
        badMileageFlagAgg: ExpressionAlias<Boolean>,
        longTimeFlagAgg: ExpressionAlias<Boolean>
    ): Set<WaybillLabel?> {
        return setOf(
            if (row[remontFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.REPAIR_POINT else null,
            if (row[idleFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.DOWNTIME else null,
            if (row[noTripsFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.NO_TRIP else null,
            if (row[wrongPlMvzFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.MVZ_MISMATCH else null,
            if (row[badMileageFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.BAD_MILEAGE else null,
            if (row[longTimeFlagAgg.aliasOnlyExpression() as Expression<Boolean?>] == true) WaybillLabel.LONG_TIME else null,
        )
    }

    private fun getDataQuery(
        req: WaybillItemDetailsQtyReq,
        requestedFilter: WaybillItemDetailsColumnFilter? = null,
    ): QueryAlias {
        val aliases = Aliases(req, requestedFilter)

        val filteredData = aliases.filteredData
        val columnExpressions = aliases.columnExpressions
        val probeg =
            Sum(filteredData[Pls.mileage], DoubleColumnType()).alias("probeg")
        val hours =
            Sum(filteredData[Pls.hours], DoubleColumnType()).alias("hours")
        val motoHours =
            Sum(filteredData[Pls.motoHours], DoubleColumnType()).alias("moto_hours")
        val fuelPri =
            Sum(filteredData[Pls.fuelPri], FloatColumnType()).alias("fuel_pri")
        val fuelSec =
            Sum(filteredData[Pls.fuelSec], FloatColumnType()).alias("fuel_sec")
        val filterValuesAgg = aliases.filterValuesAgg

        return filteredData.select(
            listOfNotNull(
                aliases.plQuant,
                aliases.labeledWaybillQty,
                aliases.remontFlagAgg,
                aliases.idleFlagAgg,
                aliases.noTripsFlagAgg,
                aliases.wrongPlMvzFlagAgg,
                aliases.badMileageFlagAgg,
                aliases.longTimeFlagAgg,
                probeg,
                hours,
                motoHours,
                fuelPri,
                fuelSec,
                filterValuesAgg
            ) + columnExpressions
        )
            .applyFilters(req.filters.filterNot { it.name.isCalculated })
            .apply {
                req.filters.filter { it.name == labels }
                    .forEach { this.applyLabelsFilter(it) }
            }
            .apply {
                if (requestedFilter?.value?.isNotEmpty() == true && requestedFilter.name != labels) {
                    andWhere {
                        buildFilterPredicate(
                            condition = FilterCondition.contain,
                            values = requestedFilter.value.filter { it != "" },
                            type = requestedFilter.name.type,
                            exposedExpression = requestedFilter.name.exposedExpression.aliasOnlyExpression()
                        )
                    }
                }
            }
            .groupBy(*columnExpressions.toTypedArray())
            .alias("report")
    }

    private fun Query.applyLabelsFilter(filter: WaybillItemDetailsColumnFilter): Query {
        return apply {
            if (filter.name == labels) {
                andWhere {
                    when (filter.condition) {
                        FilterCondition.nullOrEmpty -> AndOp(
                            listOf(
                                NotOp(Labels.REPAIR_POINT.expression),
                                NotOp(Labels.DOWNTIME.expression),
                                NotOp(Labels.NO_TRIP.expression),
                                NotOp(Labels.MVZ_MISMATCH.expression),
                                NotOp(Labels.BAD_MILEAGE.expression),
                                NotOp(Labels.LONG_TIME.expression)
                            )
                        )

                        FilterCondition.notNullOrEmpty -> OrOp(
                            listOf(
                                Labels.REPAIR_POINT.expression,
                                Labels.DOWNTIME.expression,
                                Labels.NO_TRIP.expression,
                                Labels.MVZ_MISMATCH.expression,
                                Labels.BAD_MILEAGE.expression,
                                Labels.LONG_TIME.expression
                            )
                        )

                        else -> if (filter.condition == FilterCondition.contain) {
                            OrOp(
                                filter.value.map { v -> WaybillLabel.entries.firstOrNull { it.label == v } }
                                    .map {
                                        when (it) {
                                            null -> throw WrongRequestDataException("No such flag")
                                            WaybillLabel.NO_TRIP -> Labels.NO_TRIP.expression
                                            WaybillLabel.MVZ_MISMATCH -> Labels.MVZ_MISMATCH.expression
                                            WaybillLabel.REPAIR_POINT -> Labels.REPAIR_POINT.expression
                                            WaybillLabel.DOWNTIME -> Labels.DOWNTIME.expression
                                            WaybillLabel.BAD_MILEAGE -> Labels.BAD_MILEAGE.expression
                                            WaybillLabel.LONG_TIME -> Labels.LONG_TIME.expression
                                            WaybillLabel.NO_DEVIATIONS -> AndOp(
                                                listOf(
                                                    NotOp(Labels.REPAIR_POINT.expression),
                                                    NotOp(Labels.DOWNTIME.expression),
                                                    NotOp(Labels.NO_TRIP.expression),
                                                    NotOp(Labels.MVZ_MISMATCH.expression),
                                                    NotOp(Labels.BAD_MILEAGE.expression),
                                                    NotOp(Labels.LONG_TIME.expression)
                                                )
                                            )
                                        }
                                    }
                            )
                        } else {
                            AndOp(
                                filter.value.map { v -> WaybillLabel.entries.firstOrNull { it.label == v } }
                                    .map {
                                        when (it) {
                                            null -> throw WrongRequestDataException("No such label")
                                            WaybillLabel.NO_TRIP -> NotOp(Labels.NO_TRIP.expression)
                                            WaybillLabel.MVZ_MISMATCH -> NotOp(Labels.MVZ_MISMATCH.expression)
                                            WaybillLabel.REPAIR_POINT -> NotOp(Labels.REPAIR_POINT.expression)
                                            WaybillLabel.DOWNTIME -> NotOp(Labels.DOWNTIME.expression)
                                            WaybillLabel.BAD_MILEAGE -> NotOp(Labels.BAD_MILEAGE.expression)
                                            WaybillLabel.LONG_TIME -> NotOp(Labels.LONG_TIME.expression)
                                            WaybillLabel.NO_DEVIATIONS -> OrOp(
                                                listOf(
                                                    Labels.REPAIR_POINT.expression,
                                                    Labels.DOWNTIME.expression,
                                                    Labels.NO_TRIP.expression,
                                                    Labels.MVZ_MISMATCH.expression,
                                                    Labels.BAD_MILEAGE.expression,
                                                    Labels.LONG_TIME.expression
                                                )
                                            )
                                        }
                                    }
                            )
                        }
                    }
                }
            }
        }
    }

    private fun Query.applyFilters(filters: List<WaybillItemDetailsColumnFilter>): Query {
        return apply {
            filters.filterNot { it.name == labels }
                .forEach { filter ->
                    andWhere {
                        buildFilterPredicate(
                            condition = filter.condition,
                            values = filter.value,
                            type = filter.name.type,
                            exposedExpression = filter.name.exposedExpression.aliasOnlyExpression(),
                            strictFilter = true
                        )
                    }
                }
        }
    }

    @ExposedTransactional
    fun getFilterValues(
        req: WaybillFilterReq,
        requestedFilter: WaybillItemDetailsColumnFilter,
        filters: List<WaybillItemDetailsColumnFilter>
    ): List<String> {
        val dataReq = WaybillItemDetailsQtyReq(
            pageNumber = 0,
            pageSize = Int.MAX_VALUE,
            from = req.from,
            to = req.to.plusDays(1),
            geoFilter = req.geoFilter,
            columns = req.columns,
            sort = emptyList(),
            filters = filters,
            granularity = null
        )

        val aliases = Aliases(dataReq, requestedFilter)
        val requestedColumnExpressionList = if (requestedFilter.name == labels) {
            listOf(
                intLiteral(1).alias("remont_flag_agg").aliasOnlyExpression(),
                intLiteral(1).alias("idle_flag_agg").aliasOnlyExpression(),
                intLiteral(1).alias("no_trips_flag_agg").aliasOnlyExpression(),
                intLiteral(1).alias("wrong_pl_mvz_flag_agg").aliasOnlyExpression(),
                intLiteral(1).alias("bad_mileage_flag_agg").aliasOnlyExpression(),
                intLiteral(1).alias("long_time_flag_agg").aliasOnlyExpression()
            )
        } else listOfNotNull(aliases.filterValuesAgg?.aliasOnlyExpression())

        val dataQuery = getDataQuery(dataReq, requestedFilter)

        val query = dataQuery
            .select(requestedColumnExpressionList)
            .applyFilters(req.filters.filter { it.name.isCalculated })
            .withDistinct()
            .also { "Waybills filters SQL: " + log.debug(it.prepareSQL(QueryBuilder(false))) }
        return when (requestedFilter.name) {
            labels -> {
                query.flatMap { row ->
                    getLabelsSet(
                        row,
                        aliases.remontFlagAgg,
                        aliases.idleFlagAgg,
                        aliases.noTripsFlagAgg,
                        aliases.wrongPlMvzFlagAgg,
                        aliases.badMileageFlagAgg,
                        aliases.longTimeFlagAgg
                    ).map {
                        when (it) {
                            null -> WaybillLabel.NO_DEVIATIONS.name
                            else -> it.name
                        }
                    }
                }.distinct()
            }

            else -> {
                query.flatMap { row ->
                    val value = aliases.filterValuesAgg?.aliasOnlyExpression()?.let { row[it].toString() }
                    mapper.readValue(value, List::class.java).map { it.toString() }
                }.distinct()
            }
        }
    }

    enum class Labels(val expression: Expression<Boolean>) {
        NO_TRIP(booleanLiteral(true).alias("no_trips_flag").aliasOnlyExpression()),
        MVZ_MISMATCH(booleanLiteral(true).alias("wrong_cost_point_flag").aliasOnlyExpression()),
        REPAIR_POINT(booleanLiteral(true).alias("repair_flag").aliasOnlyExpression()),
        DOWNTIME(booleanLiteral(true).alias("idle_flag").aliasOnlyExpression()),
        BAD_MILEAGE(booleanLiteral(true).alias("bad_mileage_flag").aliasOnlyExpression()),
        LONG_TIME(booleanLiteral(true).alias("long_time_flag").aliasOnlyExpression()),
    }

    private class Aliases(
        req: WaybillItemDetailsQtyReq,
        requestedFilter: WaybillItemDetailsColumnFilter? = null,
    ) {
        val orgUnits = getOrgUnits(req)

        val columnExpressions =
            req.columns.filterNot { it.isCalculated }
                .map { it.exposedExpression.aliasOnlyExpression() }
                .distinct()
        val filteredData = getFilteredData(req, orgUnits)
        val remontFlagAgg = BoolOr(filteredData[Pls.repairFlag]).alias("remont_flag_agg")
        val idleFlagAgg = BoolOr(filteredData[Pls.idleFlag]).alias("idle_flag_agg")
        val noTripsFlagAgg = BoolOr(filteredData[Pls.noTripsFlag]).alias("no_trips_flag_agg")
        val wrongPlMvzFlagAgg =
            BoolOr(filteredData[Pls.wrongCostPointFlag]).alias("wrong_pl_mvz_flag_agg")
        val badMileageFlagAgg =
            BoolOr(filteredData[Pls.badMileageFlag]).alias("bad_mileage_flag_agg")
        val longTimeFlagAgg =
            BoolOr(filteredData[Pls.longTimeFlag]).alias("long_time_flag_agg")

        val plQuant = Count(intLiteral(1)).alias(waybillQty.exposedExpression.alias)
        val labeledWaybillQty = CountFiltered(
            filteredData[Pls.repairFlag] or filteredData[Pls.idleFlag] or filteredData[Pls.noTripsFlag] or
                    filteredData[Pls.wrongCostPointFlag] or filteredData[Pls.badMileageFlag] or filteredData[Pls.longTimeFlag]
        ).alias(labeledWB.exposedExpression.alias)
        val counter = Count(intLiteral(1)).over().alias("counter")
        val filterValuesAgg = requestedFilter?.let {
            val exp = requestedFilter.name.exposedExpression
            JsonAgg(Distinct(exp.aliasOnlyExpression() as ExpressionWithColumnType<String>)).alias("filterValues")
        }?.takeIf { requestedFilter.name != labels }

        private fun getFilteredData(
            req: WaybillItemDetailsQtyReq,
            orgUnits: QueryAlias
        ): QueryAlias {
            val endDate =
                Case().When(Pls.userStat.inList(listOf("ЧЗАК", "ЗАКР")), Pls.endDateFact).Else(Pls.endDatePlan.delegate)

            return Pls
                .join(
                    otherTable = orgUnits,
                    joinType = JoinType.INNER,
                    additionalConstraint = {
                        (orgUnits[OrganizationalUnitsTimelineTable.mvzId] eq Pls.mvzId) and
                                (orgUnits[OrganizationalUnitsTimelineTable.startDate] lessEq Pls.startTimestamp.castToDate()) and
                                (orgUnits[OrganizationalUnitsTimelineTable.endDate] greater Pls.startTimestamp.castToDate())
                    }
                )
                .select(
                    Pls.getAllFields() + listOf(
                        orgUnits[OrganizationalUnitsTimelineTable.atpName],
                        orgUnits[OrganizationalUnitsTimelineTable.mrName],
                        orgUnits[OrganizationalUnitsTimelineTable.territoryName]
                    )
                )
                .where(
                    Pls.startDateFact.delegate lessEq dateLiteral(req.to) and
                            (dateLiteral(req.from) less endDate)
                )
                .alias("filtered_data")

        }

        private fun getOrgUnits(req: WaybillItemDetailsQtyReq): QueryAlias {
            return OrganizationalUnitsTimelineTable.selectAll()
                .applyGeoFilter(req.geoFilter)
                .alias("org_units")
        }
    }
}

