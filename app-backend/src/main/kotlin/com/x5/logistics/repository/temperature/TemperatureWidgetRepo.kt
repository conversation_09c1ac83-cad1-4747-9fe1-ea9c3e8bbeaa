package com.x5.logistics.repository.temperature

import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.som.SomTripsTable
import com.x5.logistics.data.temperature.TemperatureGoalLog
import com.x5.logistics.repository.CountFiltered
import com.x5.logistics.repository.CountStar
import com.x5.logistics.repository.GreatestFromList
import com.x5.logistics.repository.NullIf
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.castToDouble
import com.x5.logistics.repository.castToFloat
import com.x5.logistics.repository.times
import com.x5.logistics.rest.dto.temperature.TemperatureWidgetReq
import com.x5.logistics.service.DesktopWidgetsService
import com.x5.logistics.service.temperature.DiagramPointsDto
import com.x5.logistics.service.toDoubleSafely
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.Avg
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.OrOp
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.div
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Component
import java.math.RoundingMode
import java.time.LocalDate

@Component
class TemperatureWidgetRepo(
    val desktopWidgetsService: DesktopWidgetsService
) {
    val logger = getLogger()

    suspend fun getTemperatureGoal(req: TemperatureWidgetReq): Double = newSuspendedTransaction {
        val tGoalWithForRetailNetwork = req.characterDeliveryForRetailNetwork.map { characterDeliveryForRetailNetwork ->
            TemperatureGoalLog.tGoalQueryAlias(characterDeliveryForRetailNetwork.retailNetwork)
        }

        val tGoalQueryAlias = TemperatureGoalLog.tGoalQueryAlias()

        val tGoalAlias = Avg(
            Coalesce(
                tGoalQueryAlias[TemperatureGoalLog.temperatureGoal],
                GreatestFromList(tGoalWithForRetailNetwork.map { it[TemperatureGoalLog.temperatureGoal] }).castToFloat()
            ), 2
        ).alias("t_goal")

        var mainQuery = SomTripsTable.join(
            otherTable = tGoalQueryAlias,
            joinType = JoinType.LEFT,
            additionalConstraint = {
                tGoalQueryAlias[TemperatureGoalLog.retailNetwork] eq SomTripsTable.rcRetailNetwork and
                        (tGoalQueryAlias[TemperatureGoalLog.startDate] lessEq SomTripsTable.dtRegRcDate) and
                        (SomTripsTable.dtRegRcDate less tGoalQueryAlias[TemperatureGoalLog.endDate])
            })

        tGoalWithForRetailNetwork.forEach { queryAlias ->
            mainQuery = mainQuery.join(
                otherTable = queryAlias,
                joinType = JoinType.LEFT,
                additionalConstraint = {
                    queryAlias[TemperatureGoalLog.startDate] lessEq SomTripsTable.dtRegRcDate and
                            (SomTripsTable.dtRegRcDate less queryAlias[TemperatureGoalLog.endDate])
                })
        }

        mainQuery.join(
            otherTable = OrganizationalUnitsTimelineTable,
            joinType = JoinType.LEFT,
            additionalConstraint = {
                OrganizationalUnitsTimelineTable.mvzId eq SomTripsTable.mvzId and
                        (OrganizationalUnitsTimelineTable.startDate lessEq SomTripsTable.dtRegRcDate) and
                        (SomTripsTable.dtRegRcDate less OrganizationalUnitsTimelineTable.endDate)
            }).select(tGoalAlias)
            .where {
                SomTripsTable.dtRegRcDate.between(req.from, req.to) and
                        OrOp(
                            req.characterDeliveryForRetailNetwork.map {
                                (SomTripsTable.rcRetailNetwork eq it.retailNetwork) and
                                        (SomTripsTable.charaterDelvr inList it.characterDelivery)
                            }
                        ) and (SomTripsTable.statusTCalculate eq true)
            }
            .applyGeoFilter(req.geoFilter)
            .also { logger.debug(it.prepareSQL(QueryBuilder(false))) }
            .single()[tGoalAlias]?.setScale(1, RoundingMode.HALF_EVEN)?.toDouble() ?: 0.0
    }

    suspend fun getDiagramPoints(req: TemperatureWidgetReq): DiagramPointsDto = newSuspendedTransaction {
        val metricBuilder = MetricBuilder()

        val baseMetrics = listOf(
            metricBuilder.buildOwnCarsMetric().alias("own_cars"),
            metricBuilder.buildHiredCarsMetric().alias("hired_cars"),
            metricBuilder.buildAllCarsMetric().alias("all_cars"),
        )

        val diagramMetrics = desktopWidgetsService.getDateOffsets(req.from, req.to).flatMapIndexed { index, offset ->
            listOf(
                metricBuilder.buildOwnCarsMetric(offset).alias("own_cars_$index"),
                metricBuilder.buildHiredCarsMetric(offset).alias("hired_cars_$index"),
                metricBuilder.buildAllCarsMetric(offset).alias("all_cars_$index")
            )
        }

        val valuesQuery = SomTripsTable.join(
            otherTable = OrganizationalUnitsTimelineTable,
            joinType = JoinType.LEFT,
            additionalConstraint = {
                OrganizationalUnitsTimelineTable.mvzId eq SomTripsTable.mvzId and
                        (OrganizationalUnitsTimelineTable.startDate lessEq SomTripsTable.dtRegRcDate) and
                        (SomTripsTable.dtRegRcDate less OrganizationalUnitsTimelineTable.endDate)
            }).select(baseMetrics + diagramMetrics).where {
            SomTripsTable.dtRegRcDate.between(req.from, req.to) and
                    OrOp(
                        req.characterDeliveryForRetailNetwork.map {
                            (SomTripsTable.rcRetailNetwork eq it.retailNetwork) and
                                    (SomTripsTable.charaterDelvr inList it.characterDelivery)
                        }
                    ) and (SomTripsTable.statusTCalculate eq true)
        }.applyGeoFilter(req.geoFilter)
            .also { logger.debug(it.prepareSQL(QueryBuilder(false))) }
            .single()

        val ownCars = valuesQuery.getOrNull(baseMetrics[0])?.toDoubleSafely()
        val hiredCars = valuesQuery.getOrNull(baseMetrics[1])?.toDoubleSafely()
        val allCars = when {
            ownCars == null || hiredCars == null -> null
            else -> valuesQuery.getOrNull(baseMetrics[2])?.toDoubleSafely()
        }
        val diagramPointsOwnCars = (diagramMetrics.indices step 3).map {
            valuesQuery.getOrNull(diagramMetrics[it])?.toDoubleSafely()
        }
        val diagramPointsHiredCars = (diagramMetrics.indices step 3).map {
            valuesQuery.getOrNull(diagramMetrics[it + 1])?.toDoubleSafely()
        }
        val diagramPointsAllCars = when {
            diagramPointsOwnCars.isEmpty() || diagramPointsHiredCars.isEmpty() -> emptyList()
            else -> (diagramMetrics.indices step 3).map {
                valuesQuery.getOrNull(diagramMetrics[it + 2])?.toDoubleSafely()
            }
        }
        val pointsOwnCarsAllNulls = diagramPointsOwnCars.all { it == null }
        val pointsHiredCarsAllNulls = diagramPointsHiredCars.all { it == null }
        DiagramPointsDto(
            ownCars = ownCars,
            hiredCars = hiredCars,
            allCars = allCars,
            diagramPointsOwnCars = if (pointsOwnCarsAllNulls) emptyList() else diagramPointsOwnCars,
            diagramPointsHiredCars = if (pointsHiredCarsAllNulls) emptyList() else diagramPointsHiredCars,
            diagramPointsAllCars = if (pointsOwnCarsAllNulls || pointsHiredCarsAllNulls) emptyList() else diagramPointsAllCars
        )
    }

    private inner class MetricBuilder {
        fun buildOwnCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val baseCondition = ourTripFlag eq true
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val conditions = listOfNotNull(baseCondition, dateCondition).reduce { acc, expr -> acc and expr }

                val numerator = CountFiltered(conditions and (statusTFinal eq true)).castToDouble()
                val denominator = CountFiltered(conditions).castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildHiredCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val baseCondition = ourTripFlag eq false
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val conditions = listOfNotNull(baseCondition, dateCondition).reduce { acc, expr -> acc and expr }

                val numerator = CountFiltered(conditions and (statusTFinal eq true)).castToDouble()
                val denominator = CountFiltered(conditions).castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildAllCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val numeratorConditions = when {
                    dateCondition != null -> dateCondition and (statusTFinal eq true)
                    else -> statusTFinal eq true
                }

                val numerator = CountFiltered(numeratorConditions).castToDouble()
                val denominator = when {
                    dateCondition != null -> CountFiltered(dateCondition)
                    else -> CountStar
                }.castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }
    }
}