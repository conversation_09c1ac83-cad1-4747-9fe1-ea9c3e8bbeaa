package com.x5.logistics.repository.repair

import com.x5.logistics.repository.tsdata.TsDataDao
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import java.time.LocalDate

@Entity
@Table(name = "repair", schema = "ts")
data class RepairDao(
    @Id
    @Column(name = "order_id", nullable = false)
    val orderId: Long,

    @Column(name = "vehicle_mr_id")
    val mrId: String?,

    @Column(name = "vehicle_mr")
    val mr: String?,

    @Column(name = "vehicle_atp_id")
    val atpId: Int?,

    @Column(name = "vehicle_atp")
    val atp: String?,

    @Column(name = "final_repshop_name")
    val repairAtp: String?,

    @Column(name = "vehicle_mvz")
    val vehicleMvzId: String?,

    @Column(name = "vehicle_mvz_name")
    val vehicleMvzName: String?,

    @Column(name = "vehicle_retail_network")
    val vehicleRetailNetwork: String?,

    @Column(name = "vehicle_atp_type")
    val vehicleAtpType: String?,

    @Column(name = "vehicle_mvz_type")
    val vehicleMvzType: String?,

    @Column(name = "order_sys_stat")
    val orderSysStat: String?,

    @Column(name = "repair_start_date", nullable = false)
    val repairStartDate: LocalDate,

    @Column(name = "repair_kind", nullable = false)
    val repairKind: String,

    @Column(name = "order_mvz")
    val orderMvz: String,

    @Column(name = "mvz_name")
    val orderMvzName: String,

    @Column(name = "req_id")
    val reqId: String?,

    @Column(name = "req_type")
    val reqType: String?,

    @Column(name = "req_user_stat")
    val reqUserStat: String?,

    @Column(name = "req_sys_stat")
    val reqSysStat: String?,

    @Column(name = "order_text", nullable = false)
    val orderText: String,

    @Column(name = "repair_type_name", nullable = false)
    val repairTypeName: String,

    @Column(name = "repair_subtype_name", nullable = false)
    val repairSubtypeName: String,

    @Column(name = "vrt", nullable = false)
    val vrt: String,

    @Column(name = "vrt_name")
    val vrtName: String?,

    @Column(name = "event_id")
    val eventId: String?,

    @Column(name = "event_text")
    val eventText: String?,

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "equnr", referencedColumnName = "equnr")
    val tsData: TsDataDao,

    @Column(name = "equnr", insertable = false, updatable = false)
    val equnr: Long,

    @Column(name = "ts_group")
    val tsGroup: String?,

    @Column(name = "ts_type")
    val tsType: String?,

    @Column(name = "ts_marka", nullable = false)
    val tsMarka: String,

    @Column(name = "ts_model")
    val tsModel: String?,

    @Column(name = "ts_create_date", nullable = false)
    val tsCreateDate: LocalDate,

    @Column(name = "ts_load_wgt", nullable = false)
    val tsLoadWgt: Double,

    @Column(name = "ts_gbo", nullable = false)
    val tsGbo: Boolean,

    @Column(name = "payout_date")
    val payoutDate: LocalDate?,

    @Column(name = "repair_end_date", nullable = false)
    val repairEndDate: LocalDate,

    @Column(name = "repair_plan_end_date")
    val repairPlanEndDate: LocalDate?,

    @Column(name = "deblock_date")
    val deblockDate: LocalDate?,

    @Column(name = "services_amount")
    val servicesAmount: Double?,

    @Column(name = "services_expenses")
    val servicesExpenses: Double?,

    @Column(name = "repair_expenses")
    val repairExpenses: Double?
)
