package com.x5.logistics.repository.hr

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.hr.AtpHrPlacesView
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.AndOp
import org.jetbrains.exposed.sql.OrOp
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.select
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.ZoneId

@Component
class AtpHrPlacesExposedRepo {
    val logger = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    private val moscowZoneId = ZoneId.of("Europe/Moscow")

    @ExposedTransactional
    fun getUnfilled(): List<String> = with(AtpHrPlacesView) {
        val currentDate = LocalDate.now(moscowZoneId)
        val query = slice(atpName)
            .select {
                AndOp(
                    listOf(
                        OrOp(
                            listOf(
                                endDate.isNull(),
                                endDate greater currentDate
                            )
                        ),
                        placeId.isNull()
                    )
                )
            }.withDistinct()

        if (logQuery) logger.info(query.prepareSQL(QueryBuilder(false)))

        query.map {
            it[atpName]
        }
    }
}
