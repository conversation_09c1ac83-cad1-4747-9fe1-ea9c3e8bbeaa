package com.x5.logistics.repository.ktg

import com.fasterxml.jackson.databind.ObjectMapper
import com.x5.logistics.data.dictionary.KipFullTable
import com.x5.logistics.data.dictionary.KipTable
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.dictionary.TsReadyReasonsTable
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.repository.Distinct
import com.x5.logistics.repository.JsonAgg
import com.x5.logistics.repository.NullIf
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.castToDouble
import com.x5.logistics.repository.castToInt
import com.x5.logistics.repository.castToString
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.ktg.KtgDetailsFilterReq
import com.x5.logistics.rest.dto.ktg.KtgDetailsQtyReq
import com.x5.logistics.rest.dto.ktg.KtgDetailsQtyResp
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailsColumn
import org.hibernate.query.sqm.tree.SqmNode.*
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.DivideOp
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.ExpressionWithColumnType
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.LiteralOp
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.case
import org.jetbrains.exposed.sql.SqlExpressionBuilder.coalesce
import org.jetbrains.exposed.sql.SqlExpressionBuilder.div
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.times
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.addLogger
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andHaving
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Component

@Component
class KtgReportRepo(
    private val mapper: ObjectMapper,
) {

    suspend fun getKtgDetailsReportCount(req: KtgDetailsQtyReq): Long = newSuspendedTransaction {
        val columns = getKtgDetailsReportColumns(req)
        val countQuery = columns.tables.select(intLiteral(1))
        countQuery.fillQuery(req, columns, true)
        countQuery.count()
    }

    suspend fun getKtgReportFilterValues(req: KtgDetailsFilterReq): List<String?> = newSuspendedTransaction {
        val dataReq = KtgDetailsQtyReq(
            pageNumber = null,
            pageSize = null,
            from = req.from,
            to = req.to,
            geoFilter = req.geoFilter,
            columns = req.columns,
            sort = emptyList(),
            filters = req.filters
        )

        val columns = getKtgDetailsReportColumns(dataReq, req)
        val filterColumnExpression = columns.getAliasByColumn(req.request.name)
        val filterValuesAgg =
            JsonAgg(Distinct(filterColumnExpression.delegate.castToString()))
                .alias("filterValues")

        val query = columns.tables.select(
            req.columns.asSequence().distinct().map(columns::getAliasByColumn).toList() + filterValuesAgg
        )
        query.fillQuery(dataReq, columns, true)

        query.alias("data_query").select(filterValuesAgg.aliasOnlyExpression()).withDistinct(true)
            .flatMap {
                mapper.readValue(it[filterValuesAgg.aliasOnlyExpression()], List::class.java)
            }
            .map { it?.toString() }
            .distinct()
    }

    suspend fun getKtgDetailsReportItems(req: KtgDetailsQtyReq): List<KtgDetailsQtyResp> = newSuspendedTransaction {
        addLogger(StdOutSqlLogger)
        val columns = getKtgDetailsReportColumns(req)
        val query = columns.tables.select(
            req.columns.asSequence().distinct().map(columns::getAliasByColumn).toList()
        )
        query.fillQuery(req, columns, true)
        req.sort.forEach { item ->
            query.orderBy(columns.getExpressionByColumn(item.column), if (item.asc) SortOrder.ASC else SortOrder.DESC)
        }
        val pageSize = req.pageSize
        val pageNumber = req.pageNumber
        if (pageSize != null && pageNumber != null) {
            query.limit(pageSize, offset = pageNumber.toLong() * pageSize)
        }
        log.debug(query.prepareSQL(QueryBuilder(false)))

        query.map { row ->
            KtgDetailsQtyResp(
                vehicleGroup = columns.vehicleGroupAlias.takeIf { VehicleDetailsColumn.vehicleGroup in req.columns }
                    ?.let { row[it] },
                vehicleType = columns.vehicleTypeAlias.takeIf { VehicleDetailsColumn.vehicleType in req.columns }
                    ?.let { row[it] },
                atp = columns.atpAlias.takeIf { VehicleDetailsColumn.atp in req.columns }?.let { row[it] },
                mvz = columns.mvzAlias.takeIf { VehicleDetailsColumn.mvz in req.columns }?.let { row[it] },
                mvzName = columns.mvzNameAlias.takeIf { VehicleDetailsColumn.mvzName in req.columns }?.let { row[it] },
                mvzType = columns.mvzTypeAlias.takeIf { VehicleDetailsColumn.mvzType in req.columns }?.let { row[it] },
                terName = columns.terNameAlias.takeIf { VehicleDetailsColumn.terName in req.columns }?.let { row[it] },
                mrName = columns.mrNameAlias.takeIf { VehicleDetailsColumn.mrName in req.columns }?.let { row[it] },
                brand = columns.brandAlias.takeIf { VehicleDetailsColumn.brand in req.columns }?.let { row[it] },
                model = columns.modelAlias.takeIf { VehicleDetailsColumn.model in req.columns }?.let { row[it] },
                tonnage = columns.tonnageAlias.takeIf { VehicleDetailsColumn.tonnage in req.columns }?.let { row[it] },
                mileage = columns.mileageAlias.takeIf { VehicleDetailsColumn.mileage in req.columns }?.let { row[it] },
                licenseNum = columns.licenseNumAlias.takeIf { VehicleDetailsColumn.licenseNum in req.columns }
                    ?.let { row[it] },
                eqUnit = columns.eqUnitAlias.takeIf { VehicleDetailsColumn.eqUnit in req.columns }?.let { row[it] }
                    ?.toString(),
                rcSubmitting = columns.rcSubmittingAlias.takeIf { VehicleDetailsColumn.rcSubmitting in req.columns }
                    ?.let { row[it] },
                dateSubmitting = columns.dateSubmittingAlias.takeIf { VehicleDetailsColumn.dateSubmitting in req.columns }
                    ?.let { row[it] }?.toString(),
                vin = columns.vinAlias.takeIf { VehicleDetailsColumn.vin in req.columns }?.let { row[it] },
                commissioningDate = columns.commissioningDateAlias.takeIf { VehicleDetailsColumn.commissioningDate in req.columns }
                    ?.let { row[it] },
                status = columns.statusAlias.takeIf { VehicleDetailsColumn.status in req.columns }?.let { row[it] },
                reason = columns.reasonAlias.takeIf { VehicleDetailsColumn.reason in req.columns }?.let { row[it] }
                    ?.toString(),
                waybills = columns.waybillsAlias.takeIf { VehicleDetailsColumn.waybills in req.columns }
                    ?.let { row[it] },
                waybillsRepair = columns.waybillsRepairAlias.takeIf { VehicleDetailsColumn.waybillsRepair in req.columns }
                    ?.let { row[it] },
                ktg = columns.ktgAlias.takeIf { VehicleDetailsColumn.ktg in req.columns }?.let { row[it] },
                rg = columns.rgAlias.takeIf { VehicleDetailsColumn.rg in req.columns }?.let { row[it] },
                vehicleDays = columns.vehicleDaysAlias.takeIf { VehicleDetailsColumn.vehicleDays in req.columns }
                    ?.let { row[it] },
                rcSubmittingCode = columns.rcSubmittingCodeAlias.takeIf { VehicleDetailsColumn.rcSubmittingCode in req.columns }
                    ?.let { row[it] },
                retailNetwork = columns.retailNetworkAlias.takeIf { VehicleDetailsColumn.retailNetwork in req.columns }
                    ?.let { row[it] },
                atpType = columns.atpTypeAlias.takeIf { VehicleDetailsColumn.atpType in req.columns }?.let { row[it] },
                trailerType = columns.trailerTypeAlias.takeIf { VehicleDetailsColumn.trailerType in req.columns }
                    ?.let { row[it] },
                trailerGroup = columns.trailerGroupAlias.takeIf { VehicleDetailsColumn.trailerType in req.columns }
                    ?.let { row[it] },
                trailerBrand = columns.trailerBrandAlias.takeIf { VehicleDetailsColumn.trailerBrand in req.columns }
                    ?.let { row[it] },
                trailerModel = columns.trailerModelAlias.takeIf { VehicleDetailsColumn.trailerModel in req.columns }
                    ?.let { row[it] },
                trailerTonnage = columns.trailerTonnageAlias.takeIf { VehicleDetailsColumn.trailerTonnage in req.columns }
                    ?.let { row[it] },
                trailerLicenseNum = columns.trailerLicenseNumAlias.takeIf { VehicleDetailsColumn.trailerLicenseNum in req.columns }
                    ?.let { row[it] },
                trailerEqUnit = columns.trailerEqUnitAlias.takeIf { VehicleDetailsColumn.trailerEqUnit in req.columns }
                    ?.let { row[it] }?.toString(),
                trailerVin = columns.trailerVinAlias.takeIf { VehicleDetailsColumn.trailerVin in req.columns }
                    ?.let { row[it] },
                trailerCommissioningDate = columns.trailerCommissioningDateAlias.takeIf { VehicleDetailsColumn.trailerCommissioningDate in req.columns }
                    ?.let { row.getOrNull(it) },
                compart = columns.compartAlias.takeIf { VehicleDetailsColumn.compart in req.columns }?.let { row[it] },
                compartWhole = columns.compartWholeAlias.takeIf { VehicleDetailsColumn.compartWhole in req.columns }
                    ?.let { row[it] },
                trailerCompart = columns.trailerCompartAlias.takeIf { VehicleDetailsColumn.trailerCompart in req.columns }
                    ?.let { row[it] },
                tonnageWhole = columns.tonnageWholeAlias.takeIf { VehicleDetailsColumn.tonnageWhole in req.columns }
                    ?.let { row[it].toDouble() },
            )
        }
    }

    private fun getKtgDetailsReportColumns(
        req: KtgDetailsQtyReq,
        filterReq: KtgDetailsFilterReq? = null
    ): KtgDetailsReportColumns {
        return KtgDetailsReportColumns(req, filterReq)
    }

    private fun Query.fillQuery(
        req: KtgDetailsQtyReq,
        columns: KtgDetailsReportColumns,
        strictFilter: Boolean = false,
        filterQuery: Boolean = false,
    ) {
        applyGeoFilter(req.geoFilter)
        andWhere { KipFullTable.vehicleDate greaterEq req.from and (KipFullTable.vehicleDate less req.to) }

        val aggregatedColumns =
            sequenceOf(VehicleDetailsColumn.ktg, VehicleDetailsColumn.rg, VehicleDetailsColumn.vehicleDays)

        req.filters.forEach { filter ->
            if (filter.name !in aggregatedColumns) {
                andWhere {
                    if (filter.name == VehicleDetailsColumn.waybills
                        || filter.name == VehicleDetailsColumn.waybillsRepair
                    ) {
                        buildFilterPredicate(
                            condition = if (filter.condition == FilterCondition.equal
                                || filter.condition == FilterCondition.contain
                            ) {
                                FilterCondition.notNullOrEmpty
                            } else {
                                FilterCondition.nullOrEmpty
                            },
                            values = filter.value,
                            type = filter.name.type,
                            exposedExpression = columns.getExpressionByColumn(filter.name)
                        )
                    } else {
                        buildFilterPredicate(
                            condition = filter.condition,
                            values = filter.value,
                            type = filter.name.type,
                            exposedExpression = columns.getExpressionByColumn(filter.name),
                            strictFilter = strictFilter
                        )
                    }
                }
            }
        }

        if (!filterQuery) {
            req.columns.asSequence().distinct().filter { it !in aggregatedColumns }.forEach { column ->
                groupBy(columns.getExpressionByColumn(column))
            }
        }

        req.filters.forEach { filter ->
            if (filter.name in aggregatedColumns) {
                andHaving {
                    buildFilterPredicate(
                        condition = filter.condition,
                        values = filter.value,
                        type = filter.name.type,
                        exposedExpression = columns.getExpressionByColumn(filter.name)
                    )
                }
            }
            0
        }
    }

}

private class KtgDetailsReportColumns(req: KtgDetailsQtyReq, filterReq: KtgDetailsFilterReq? = null) {
    val allColumns = (
            req.columns.asSequence() +
                    req.filters.asSequence().map { it.name } +
                    req.sort.asSequence().map { it.column } +
                    filterReq?.request?.name
            ).toSet()

    val mainReasonJoinCondition = KipTable.vehicleReason eq TsReadyReasonsTable.id
    val filteredByReasonIsEmpty =
        req.filters.any { it.name == VehicleDetailsColumn.reason && it.condition == FilterCondition.nullOrEmpty }
    val reasonJoinCondition = when {
        filteredByReasonIsEmpty -> mainReasonJoinCondition and TsReadyReasonsTable.reason.isNull()
        else -> mainReasonJoinCondition
    }

    val tables = KipFullTable.join(
        otherTable = OrganizationalUnitsTimelineTable,
        joinType = JoinType.INNER,
        additionalConstraint = {
            (KipFullTable.mvzId eq OrganizationalUnitsTimelineTable.mvzId) and
                    (KipFullTable.vehicleDate greaterEq OrganizationalUnitsTimelineTable.startDate) and
                    (KipFullTable.vehicleDate less OrganizationalUnitsTimelineTable.endDate)
        }
    )

    val vehicleGroupAlias =
        KipFullTable.vehicleGroup.takeIf { VehicleDetailsColumn.vehicleGroup in allColumns }?.alias("vehicle_group")
    val vehicleTypeAlias =
        KipFullTable.vehicleType.takeIf { VehicleDetailsColumn.vehicleType in allColumns }?.alias("vehicle_type")
    val atpAlias =
        OrganizationalUnitsTimelineTable.atpName.takeIf { VehicleDetailsColumn.atp in allColumns }?.alias("atp")
    val mvzAlias = KipFullTable.mvzId.takeIf { VehicleDetailsColumn.mvz in allColumns }?.alias("mvz")
    val mvzNameAlias = OrganizationalUnitsTimelineTable.mvzName.takeIf { VehicleDetailsColumn.mvzName in allColumns }
        ?.alias("mvz_name")
    val mvzTypeAlias = OrganizationalUnitsTimelineTable.mvzType.takeIf { VehicleDetailsColumn.mvzType in allColumns }
        ?.alias("mvz_type")
    val mrNameAlias =
        OrganizationalUnitsTimelineTable.mrName.takeIf { VehicleDetailsColumn.mrName in allColumns }?.alias("mr_name")
    val brandAlias = KipFullTable.vehicleBrand.takeIf { VehicleDetailsColumn.brand in allColumns }?.alias("brand")
    val modelAlias = KipFullTable.vehicleModel.takeIf { VehicleDetailsColumn.model in allColumns }?.alias("model")
    val tonnageAlias =
        Coalesce(KipFullTable.vehicleTonnage, doubleLiteral(0.0)).takeIf { VehicleDetailsColumn.tonnage in allColumns }
            ?.alias("tonnage")
    val mileageAlias = KipFullTable.mileage.takeIf { VehicleDetailsColumn.mileage in allColumns }?.alias("mileage")
    val licenseNumAlias =
        KipFullTable.vehicleLicense.takeIf { VehicleDetailsColumn.licenseNum in allColumns }?.alias("license_num")
    val eqUnitAlias =
        KipFullTable.equnr.castToString().takeIf { VehicleDetailsColumn.eqUnit in allColumns }?.alias("eq_unit")
    val rcSubmittingAlias =
        KipFullTable.rcSubmitName.takeIf { VehicleDetailsColumn.rcSubmitting in allColumns }?.alias("rc_submitting")
    val dateSubmittingAlias =
        KipFullTable.vehicleDate.takeIf { VehicleDetailsColumn.dateSubmitting in allColumns }?.alias("date_submitting")
    val vinAlias = KipFullTable.vehicleVin.takeIf { VehicleDetailsColumn.vin in allColumns }?.alias("vin")
    val commissioningDateAlias =
        KipFullTable.vehicleCreateYear.takeIf { VehicleDetailsColumn.commissioningDate in allColumns }
            ?.alias("commissioning_date")
    val statusAlias = coalesce(
        KipFullTable.status,
        stringLiteral("Не подан в ТГ")
    ).takeIf { VehicleDetailsColumn.status in allColumns }?.alias("status")
    val reasonAlias = KipFullTable.reason.takeIf { VehicleDetailsColumn.reason in allColumns }?.alias("reason")
    val waybillsAlias = KipFullTable.waybills.takeIf { VehicleDetailsColumn.waybills in allColumns }?.alias("waybills")
    val waybillsRepairAlias = KipFullTable.waybillsRepair.takeIf { VehicleDetailsColumn.waybillsRepair in allColumns }
        ?.alias("waybills_repair")
    val ktgAlias =
        ((case().When(KipFullTable.ktg, intLiteral(1)).Else(Op.nullOp()).count().castToDouble() * 100.0 / NullIf(
            intLiteral(1).count().castToDouble(),
            LiteralOp(DoubleColumnType(), 0.0)
        )) as DivideOp<Double?, Double?>).takeIf { VehicleDetailsColumn.ktg in allColumns }?.alias("ktg")
    val rgAlias =
        ((case().When(KipFullTable.rg, intLiteral(1)).Else(Op.nullOp()).count().castToDouble() * 100.0 / NullIf(
            intLiteral(1).count().castToDouble(),
            LiteralOp(DoubleColumnType(), 0.0)
        )) as DivideOp<Double?, Double?>).takeIf { VehicleDetailsColumn.rg in allColumns }?.alias("rg")
    val vehicleDaysAlias =
        intLiteral(1).count().takeIf { VehicleDetailsColumn.vehicleDays in allColumns }?.alias("vehicle_days")
    val rcSubmittingCodeAlias = KipFullTable.rcSubmitId.takeIf { VehicleDetailsColumn.rcSubmittingCode in allColumns }
        ?.alias("rc_submitting_code")
    val retailNetworkAlias =
        OrganizationalUnitsTimelineTable.retailNetwork.takeIf { VehicleDetailsColumn.retailNetwork in allColumns }
            ?.alias("retail_network")
    val atpTypeAlias = OrganizationalUnitsTimelineTable.atpType.takeIf { VehicleDetailsColumn.atpType in allColumns }
        ?.alias("atp_type")
    val trailerTypeAlias =
        KipFullTable.trailerType.takeIf { VehicleDetailsColumn.trailerType in allColumns }?.alias("trailer_type")
    val trailerGroupAlias =
        KipFullTable.trailerGroup.takeIf { VehicleDetailsColumn.trailerGroup in allColumns }?.alias("trailer_group")
    val trailerBrandAlias =
        KipFullTable.trailerBrand.takeIf { VehicleDetailsColumn.trailerBrand in allColumns }?.alias("trailer_brand")
    val trailerModelAlias =
        KipFullTable.trailerModel.takeIf { VehicleDetailsColumn.trailerModel in allColumns }?.alias("trailer_model")
    val trailerTonnageAlias = Coalesce(
        KipFullTable.trailerTonnage,
        doubleLiteral(0.0)
    ).takeIf { VehicleDetailsColumn.trailerTonnage in allColumns }?.alias("trailer_tonnage")
    val trailerLicenseNumAlias =
        KipFullTable.trailerLicense.takeIf { VehicleDetailsColumn.trailerLicenseNum in allColumns }
            ?.alias("trailer_license_num")
    val trailerEqUnitAlias =
        KipFullTable.trailerEqunr.takeIf { VehicleDetailsColumn.trailerEqUnit in allColumns }?.alias("trailer_eq_unit")
    val trailerVinAlias =
        KipFullTable.trailerFleetNum.takeIf { VehicleDetailsColumn.trailerVin in allColumns }?.alias("trailer_vin")
    val trailerCommissioningDateAlias =
        KipFullTable.trailerCreateYear.takeIf { VehicleDetailsColumn.trailerCommissioningDate in allColumns }
            ?.castToInt()?.alias("trailer_commissioning_date")
    val tonnageWholeAlias =
        KipFullTable.tonnageWhole.takeIf { VehicleDetailsColumn.tonnageWhole in allColumns }?.alias("tonnage_whole")
    val compartWholeAlias =
        KipFullTable.compart_whole.takeIf { VehicleDetailsColumn.compartWhole in allColumns }?.alias("compart_whole")
    val compartAlias =
        Coalesce(KipFullTable.vehicleNoCompart, intLiteral(0)).takeIf { VehicleDetailsColumn.compart in allColumns }
            ?.alias("compart")
    val trailerCompartAlias = Coalesce(
        KipFullTable.trailerNoCompart,
        intLiteral(0)
    ).takeIf { VehicleDetailsColumn.trailerCompart in allColumns }?.alias("trailer_compart")
    val terNameAlias =
        OrganizationalUnitsTimelineTable.territoryName.takeIf { VehicleDetailsColumn.terName in allColumns }
            ?.alias("territory_name")

    fun getAliasByColumn(column: VehicleDetailsColumn): ExpressionAlias<*> {
        require(column in allColumns)
        return when (column) {
            VehicleDetailsColumn.vehicleGroup -> vehicleGroupAlias
            VehicleDetailsColumn.vehicleType -> vehicleTypeAlias
            VehicleDetailsColumn.atp -> atpAlias
            VehicleDetailsColumn.mvz -> mvzAlias
            VehicleDetailsColumn.mvzName -> mvzNameAlias
            VehicleDetailsColumn.mvzType -> mvzTypeAlias
            VehicleDetailsColumn.terName -> terNameAlias
            VehicleDetailsColumn.mrName -> mrNameAlias
            VehicleDetailsColumn.brand -> brandAlias
            VehicleDetailsColumn.model -> modelAlias
            VehicleDetailsColumn.tonnage -> tonnageAlias
            VehicleDetailsColumn.mileage -> mileageAlias
            VehicleDetailsColumn.licenseNum -> licenseNumAlias
            VehicleDetailsColumn.eqUnit -> eqUnitAlias
            VehicleDetailsColumn.rcSubmitting -> rcSubmittingAlias
            VehicleDetailsColumn.dateSubmitting -> dateSubmittingAlias
            VehicleDetailsColumn.vin -> vinAlias
            VehicleDetailsColumn.commissioningDate -> commissioningDateAlias
            VehicleDetailsColumn.status -> statusAlias
            VehicleDetailsColumn.reason -> reasonAlias
            VehicleDetailsColumn.waybills -> waybillsAlias
            VehicleDetailsColumn.waybillsRepair -> waybillsRepairAlias
            VehicleDetailsColumn.ktg -> ktgAlias
            VehicleDetailsColumn.rg -> rgAlias
            VehicleDetailsColumn.vehicleDays -> vehicleDaysAlias
            VehicleDetailsColumn.rcSubmittingCode -> rcSubmittingCodeAlias
            VehicleDetailsColumn.retailNetwork -> retailNetworkAlias
            VehicleDetailsColumn.atpType -> atpTypeAlias
            VehicleDetailsColumn.trailerType -> trailerTypeAlias
            VehicleDetailsColumn.trailerGroup -> trailerGroupAlias
            VehicleDetailsColumn.trailerBrand -> trailerBrandAlias
            VehicleDetailsColumn.trailerModel -> trailerModelAlias
            VehicleDetailsColumn.trailerTonnage -> trailerTonnageAlias
            VehicleDetailsColumn.trailerLicenseNum -> trailerLicenseNumAlias
            VehicleDetailsColumn.trailerEqUnit -> trailerEqUnitAlias
            VehicleDetailsColumn.trailerVin -> trailerVinAlias
            VehicleDetailsColumn.trailerCommissioningDate -> trailerCommissioningDateAlias
            VehicleDetailsColumn.tonnageWhole -> tonnageWholeAlias
            VehicleDetailsColumn.compartWhole -> compartWholeAlias
            VehicleDetailsColumn.compart -> compartAlias
            VehicleDetailsColumn.trailerCompart -> trailerCompartAlias
            else -> error("not found column: $column")
        }!!
    }

    fun getExpressionByColumn(column: VehicleDetailsColumn): Expression<*> =
        getAliasByColumn(column).delegate
}

