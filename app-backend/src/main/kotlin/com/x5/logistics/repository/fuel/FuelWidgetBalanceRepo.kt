package com.x5.logistics.repository.fuel

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.fuel.PlFuelStatsTable
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.minus
import com.x5.logistics.repository.safeDiv
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.fuel.widget.FuelWidgetBalanceResp
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.isAlmostZero
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.floatLiteral
import org.jetbrains.exposed.sql.sum
import org.springframework.stereotype.Repository
import java.math.RoundingMode
import java.time.LocalDate

@Repository
class FuelWidgetBalanceRepo {
    val log = getLogger()

    @ExposedTransactional
    fun getFuelWidgetBalanceData(
        from: LocalDate,
        to: LocalDate,
        geoFilter: GeoFilter
    ): FuelWidgetBalanceResp {
        val percentReduceFuelPlan =
            (PlFuelStatsTable.rashodFuelPlanHead minus PlFuelStatsTable.rashodFuelWithoutGboAssumed).sum()
                .safeDiv(
                    PlFuelStatsTable.rashodFuelWithoutGboAssumed.sum(),
                    floatLiteral(1F)
                )
        val percentReduceFuelFact =
            (PlFuelStatsTable.rashodFuelFactHead minus PlFuelStatsTable.rashodFuelWithoutGboAssumed).sum()
                .safeDiv(
                    PlFuelStatsTable.rashodFuelWithoutGboAssumed.sum(),
                    floatLiteral(1F)
                )
        val coefReplFuelPlan =
            PlFuelStatsTable.rashodGasPlan.sum()
                .safeDiv(
                    PlFuelStatsTable.effectFuelOfGboPlan.sum(),
                    floatLiteral(1F)
                )
        val coefReplFuelFact =
            PlFuelStatsTable.rashodGasFact.sum()
                .safeDiv(
                    PlFuelStatsTable.effectFuelOfGboFact.sum(),
                    floatLiteral(1F)
                )
        val econTotOfGboRub = PlFuelStatsTable.econTotOfGboRub.sum()

        val row = PlFuelStatsTable
            .join(
                otherTable = OrganizationalUnitsTimelineTable,
                joinType = JoinType.INNER,
                additionalConstraint = {
                    (OrganizationalUnitsTimelineTable.mvzId eq PlFuelStatsTable.mvzId) and
                            (OrganizationalUnitsTimelineTable.startDate lessEq PlFuelStatsTable.date) and
                            (OrganizationalUnitsTimelineTable.endDate greater PlFuelStatsTable.date)
                }
            )
            .select(
                percentReduceFuelPlan,
                percentReduceFuelFact,
                coefReplFuelPlan,
                coefReplFuelFact,
                econTotOfGboRub
            ).applyGeoFilter(geoFilter)
            .andWhere { PlFuelStatsTable.gbo eq true }
            .andWhere { (PlFuelStatsTable.date greaterEq from) and (PlFuelStatsTable.date lessEq to) }
            .also { log.debug(it.prepareSQL(QueryBuilder(false))) }
            .single()

        val planCoefficientSubstitutionDieselByGas = row[coefReplFuelPlan]?.let { it * -1 }
            ?.toBigDecimal()?.setScale(2, RoundingMode.HALF_UP)
        val factCoefficientSubstitutionDieselByGas = row[coefReplFuelFact]?.let { it * -1 }
            ?.toBigDecimal()?.setScale(2, RoundingMode.HALF_UP)
        val planPercentDeclineDiesel = row[percentReduceFuelPlan]?.let { it * 100 }
            ?.toBigDecimal()?.setScale(2, RoundingMode.HALF_UP)
        val factPercentDeclineDiesel = row[percentReduceFuelFact]?.let { it * 100 }
            ?.toBigDecimal()?.setScale(2, RoundingMode.HALF_UP)
        val overspendingEconomyPlanEffectWithGbo = row[econTotOfGboRub]
            ?.toBigDecimal()?.setScale(2, RoundingMode.HALF_UP)
        return FuelWidgetBalanceResp(
            planCoefficientSubstitutionDieselByGas = planCoefficientSubstitutionDieselByGas,
            factCoefficientSubstitutionDieselByGas = factCoefficientSubstitutionDieselByGas,
            planPercentDeclineDiesel = planPercentDeclineDiesel,
            factPercentDeclineDiesel = factPercentDeclineDiesel,
            overspendingEconomyPlanEffectWithGbo = overspendingEconomyPlanEffectWithGbo,
            isEmpty = overspendingEconomyPlanEffectWithGbo == null
                    && (planCoefficientSubstitutionDieselByGas ?: 0F).isAlmostZero
                    && (factCoefficientSubstitutionDieselByGas ?: 0F).isAlmostZero
                    && (planPercentDeclineDiesel ?: 0F).isAlmostZero && (factPercentDeclineDiesel ?: 0F).isAlmostZero
        )
    }
}
