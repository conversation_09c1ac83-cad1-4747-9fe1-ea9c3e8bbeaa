@file:Suppress("UNCHECKED_CAST", "unused")

package com.x5.logistics.repository

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.x5.logistics.config.ORG_UNIT_VERSION
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.dictionary.UnitTable
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.util.PRECISION_THRESHOLD
import io.swagger.v3.oas.annotations.media.Schema
import org.jetbrains.exposed.dao.Entity
import org.jetbrains.exposed.dao.EntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IdTable
import org.jetbrains.exposed.sql.AbstractQuery
import org.jetbrains.exposed.sql.AndOp
import org.jetbrains.exposed.sql.Between
import org.jetbrains.exposed.sql.BooleanColumnType
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.Cast
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.ComparisonOp
import org.jetbrains.exposed.sql.ComplexExpression
import org.jetbrains.exposed.sql.CustomFunction
import org.jetbrains.exposed.sql.CustomOperator
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.ExpressionWithColumnType
import org.jetbrains.exposed.sql.FloatColumnType
import org.jetbrains.exposed.sql.Function
import org.jetbrains.exposed.sql.GreaterOp
import org.jetbrains.exposed.sql.IColumnType
import org.jetbrains.exposed.sql.IntegerColumnType
import org.jetbrains.exposed.sql.IsNullOp
import org.jetbrains.exposed.sql.LessOp
import org.jetbrains.exposed.sql.LiteralOp
import org.jetbrains.exposed.sql.LongColumnType
import org.jetbrains.exposed.sql.NotOp
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.Op.Companion.nullOp
import org.jetbrains.exposed.sql.OrOp
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SqlExpressionBuilder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.div
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.TextColumnType
import org.jetbrains.exposed.sql.WindowFunction
import org.jetbrains.exposed.sql.WindowFunctionDefinition
import org.jetbrains.exposed.sql.addLogger
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.append
import org.jetbrains.exposed.sql.booleanLiteral
import org.jetbrains.exposed.sql.castTo
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.functions.math.AbsFunction
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.javatime.JavaInstantColumnType
import org.jetbrains.exposed.sql.javatime.JavaLocalDateColumnType
import org.jetbrains.exposed.sql.javatime.JavaLocalTimeColumnType
import org.jetbrains.exposed.sql.longLiteral
import org.jetbrains.exposed.sql.ops.SingleValueInListOp
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.sum
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.unionAll
import org.jetbrains.exposed.sql.vendors.currentDialect
import org.postgresql.util.PGInterval
import org.postgresql.util.PGobject
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.*

val moscowZoneId: ZoneId = ZoneId.of("Europe/Moscow")

class NullIf<T>(expr1: ExpressionWithColumnType<T>, expr2: Expression<*>) :
    CustomFunction<T>("nullif", expr1.columnType, expr1, expr2)

class Div(expr1: Expression<*>, expr2: Expression<*>) :
    CustomFunction<Int>("div", IntegerColumnType(), expr1, expr2)

class DatePart(text: Expression<*>, interval: Expression<*>) :
    CustomFunction<Double>("date_part", DoubleColumnType(), text, interval)

class Extract(private val part: String, val interval: Expression<*>) : Function<Long>(LongColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append("EXTRACT(")
            append(part)
            append(" FROM ")
            append(interval)
            append(")")
        }
    }
}

class CountAll : Function<Long>(LongColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append("COUNT(*) OVER ()")
        }
    }
}

class RowNumber : Function<Long>(LongColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append("ROW_NUMBER() OVER ()")
        }
    }
}

class SumOver(
    private val expression: Expression<*>
) : Function<Double>(DoubleColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append(
                CustomFunction("SUM", FloatColumnType(), expression)
            ).append(" OVER()")
        }
    }
}

class CountFilteredOver(
    private val columns: List<Expression<*>>,
    private val filterCondition: Expression<Boolean>
) : Function<Long>(LongColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("COUNT(1) FILTER (WHERE ")
        append(filterCondition)
        append(") OVER (PARTITION BY ")
        columns.forEachIndexed { index, column ->
            if (index > 0) append(", ")
            append(column)
        }
        append(")")
    }
}

class BoolOrOver(
    private val column: Expression<Boolean>,
    private val partitionColumns: List<Expression<*>>
) : Function<Boolean>(BooleanColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("BOOL_OR(")
        append(column)
        append(") OVER (PARTITION BY ")
        partitionColumns.forEachIndexed { index, partitionColumn ->
            if (index > 0) append(", ")
            append(partitionColumn)
        }
        append(")")
    }
}

class BoolOr(
    private val column: Expression<Boolean>
) : Function<Boolean>(BooleanColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("BOOL_OR(")
        append(column)
        append(")")
    }
}

fun Query.groupingSets(exprList: Set<List<Column<*>>>, total: Boolean) = apply {
    this.groupBy(groupSets(exprList, total))
}

fun groupSets(
    exprList: Set<List<Column<*>>>,
    total: Boolean
): Function<String> = object : Function<String>(TextColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("GROUPING SETS (")
            .append(
                exprList.joinToString { columns ->
                    columns.joinToString(
                        prefix = "(",
                        postfix = ")",
                        separator = ","
                    ) { it.name }
                } + (if (total) ", ()" else "")
            ).append(")")
    }
}

class CountFiltered(private val filterCondition: Expression<Boolean>) : Function<Long>(LongColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("COUNT(1) FILTER (WHERE ")
        append(filterCondition)
        append(")")
    }
}

class CountFilteredDistinct(private val expr: Expression<*>, private val filterCondition: Expression<Boolean>) : Function<Long>(LongColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("COUNT(DISTINCT ")
        append(expr)
        append(") FILTER (WHERE ")
        append(filterCondition)
        append(")")
    }
}

class BoolAnd(val expr: Expression<Boolean>) : Function<Boolean>(BooleanColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("bool_and(")
        append(expr)
        append(")")
    }
}

fun <T> evaluateOrNull(nullable: Expression<*>, expr: Expression<T>): ExpressionWithColumnType<T> = Case()
    .When(BoolAnd(nullable.isNull()), nullOp<T>())
    .Else(expr)


class SumFiltered<T>(
    private val expr: Expression<T>,
    private val filterCondition: Expression<Boolean>,
    columnType: IColumnType<T & Any>
) : Function<T?>(columnType), WindowFunction<T?> {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("SUM(", expr, ") FILTER (WHERE ")
        append(filterCondition)
        append(")")
    }

    override fun over(): WindowFunctionDefinition<T?> {
        return WindowFunctionDefinition(columnType, this)
    }
}

class CountWithFilter<T>(
    private val expr: Expression<T>,
    private val filterCondition: Expression<Boolean>,
    private val distinct: Boolean = false,
    columnType: IColumnType<T & Any>
) : Function<T?>(columnType), WindowFunction<T?> {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("COUNT(")
        if (distinct) append("DISTINCT ")
        append(expr, ") FILTER (WHERE ")
        append(filterCondition)
        append(")")
    }

    override fun over(): WindowFunctionDefinition<T?> {
        return WindowFunctionDefinition(columnType, this)
    }
}

class CountWithFilterLong(
    private val expr: Expression<*>,
    private val filterCondition: Expression<Boolean>,
    private val distinct: Boolean = false
) : Function<Long>(LongColumnType()), WindowFunction<Long> {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("COUNT(")
        if (distinct) append("DISTINCT ")
        append(expr, ") FILTER (WHERE ")
        append(filterCondition)
        append(")")
    }

    override fun over(): WindowFunctionDefinition<Long> {
        return WindowFunctionDefinition(columnType, this)
    }
}

fun <T> ExpressionWithColumnType<T>.countFiltered(filter: Expression<Boolean>, distinct: Boolean = false) =
    CountWithFilter(this, filter, distinct, columnType)

fun <T> ExpressionWithColumnType<T>.sumFiltered(filter: Expression<Boolean>) =
    SumFiltered(this, filter, columnType)

class ArrayFunction<T : Any>(expr: List<Expression<*>>, columnType: org.jetbrains.exposed.sql.ColumnType<T>) :
    CustomFunction<T>("", columnType, *(expr.toTypedArray()))

class Least<T>(
    expression1: ExpressionWithColumnType<T>,
    expression2: Expression<in T>,
    vararg otherExpressions: Expression<in T>
) : CustomFunction<T>(
    "least",
    expression1.columnType,
    *(arrayOf(expression1, expression2) + otherExpressions)
)

class LeastFromList(private val list: List<Expression<*>>) : Function<String>(TextColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("least(")
        list.forEachIndexed { idx, exp ->
            if (idx > 0) {
                append(",")
            }
            append(exp)
        }
        append(")")
    }
}

class Greatest<T>(
    expression1: ExpressionWithColumnType<T>,
    expression2: Expression<in T>,
    vararg otherExpressions: Expression<in T>
) : CustomFunction<T>(
    "greatest",
    expression1.columnType,
    *(arrayOf(expression1, expression2) + otherExpressions)
)

class GreatestFromList(private val list: List<Expression<*>>) : Function<String>(TextColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("greatest(")
        list.forEachIndexed { idx, exp ->
            if (idx > 0) {
                append(",")
            }
            append(exp)
        }
        append(")")
    }
}

class Age(
    expression1: ExpressionWithColumnType<LocalDate>,
    expression2: Expression<LocalDate>,
) : CustomFunction<Duration>(
    "AGE",
    IntervalColumnType,
    *(arrayOf(expression1, expression2))
)

class DaysInMonth(
    expression1: Expression<LocalDate>
) : CustomFunction<Int>(
    "days_in_month",
    IntegerColumnType(),
    expression1
)

operator fun <T, S : T> Expression<T>.div(denominator: Expression<S>) =
    with(SqlExpressionBuilder) {
        (this@Expression as ExpressionWithColumnType<T>) / denominator
    }

operator fun <T, S : T> Expression<T>.minus(subtrahend: Expression<S>) =
    with(SqlExpressionBuilder) {
        (this@Expression as ExpressionWithColumnType<T>) - subtrahend
    }

operator fun <T, S : T> Expression<T>.plus(summand: Expression<S>) =
    with(SqlExpressionBuilder) {
        (this@Expression as ExpressionWithColumnType<T>) + summand
    }

fun Expression<out Any?>.castToDouble(): ExpressionWithColumnType<Double?> = castTo(DoubleColumnType())

@JvmName("castToDoubleNotNull")
fun Expression<out Any>.castToDouble(): ExpressionWithColumnType<Double> = castTo(DoubleColumnType())

@Deprecated(message = "replace with castToDouble()", replaceWith = ReplaceWith("castToDouble"))
fun Expression<*>.castToDoubleDep(): ExpressionWithColumnType<Double> = castTo(DoubleColumnType())

fun Expression<*>.castToFloat(): ExpressionWithColumnType<Float> =
    castTo(FloatColumnType())

fun Expression<*>.castToInt(): ExpressionWithColumnType<Int> =
    castTo(IntegerColumnType())

fun Expression<*>.castToLong(): ExpressionWithColumnType<Long> =
    castTo(LongColumnType())

fun Expression<*>.castToDate(): ExpressionWithColumnType<LocalDate> =
    castTo(JavaLocalDateColumnType())

fun Expression<*>.castToString(): ExpressionWithColumnType<String> =
    castTo(TextColumnType())

class JsonAgg(private val expr: Expression<*>) : Function<String>(TextColumnType()) {

    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append(
                Cast(
                    CustomFunction("json_agg", TextColumnType(), expr),
                    TextColumnType()
                )
            )
        }
    }
}

class JsonBuildObject(vararg expressions: ExpressionWithColumnType<*>) :
    CustomFunction<String>("json_build_object", TextColumnType(), *expressions)

fun stringAggWithFilter(
    expression: Expression<String>,
    condition: Expression<Boolean>,
    separator: String = ", "
): Function<String> =
    object : Function<String>(TextColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                append("string_agg(distinct $expression, '$separator') filter (where $condition)")
            }
        }
    }

class Distinct<T>(private val expr: ExpressionWithColumnType<T>) : Function<T>(expr.columnType) {

    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append(" DISTINCT ")
            append(expr)
        }
    }
}

//FIXME: Взять техническую задачу: объединить с Distinct
class StringDistinct(private val expr: Expression<*>) : Function<String>(TextColumnType()) {

    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append(" DISTINCT ")
            append(expr)
        }
    }
}

fun generateDateSeriesWithInterval(
    start: Expression<LocalDate>,
    end: Expression<LocalDate>,
    interval: String
): Function<LocalDate> =
    object : Function<LocalDate>(JavaLocalDateColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                append("generate_series($start, $end, '$interval'::interval)::date")
            }
        }
    }

class GranularDate(
    gran: Granularity,
    dateToTrim: ExpressionWithColumnType<LocalDate?>
) : CustomFunction<LocalDate>(
    functionName = "get_granular_date",
    columnType = JavaLocalDateColumnType(),
    expr = arrayOf(LiteralOp(TextColumnType(), gran.name.lowercase()), dateToTrim)
)

class LastGranularityDate(
    gran: Granularity,
    day: Expression<LocalDate?>
) : CustomFunction<LocalDate>(
    functionName = "get_last_granular_date",
    columnType = JavaLocalDateColumnType(),
    expr = arrayOf(LiteralOp(TextColumnType(), gran.name.lowercase()), day)
)

fun isFilterAllowNull(
    condition: FilterCondition,
    values: List<Any>
): Boolean =
    when (condition) {
        FilterCondition.contain -> "null" in values
        FilterCondition.notContain -> "null" !in values
        FilterCondition.greater, FilterCondition.less, FilterCondition.equal, FilterCondition.notNullOrEmpty -> false
        FilterCondition.notEqual, FilterCondition.nullOrEmpty -> true
    }

fun SqlExpressionBuilder.buildFilterPredicate(
    condition: FilterCondition,
    values: List<Any?>,
    type: ColumnType,
    exposedExpression: Expression<*>,
    strictFilter: Boolean = false,
    precision: Double = PRECISION_THRESHOLD
): Op<Boolean> {
    return when (condition) {
        FilterCondition.contain -> {
            if (values.isEmpty()) {
                Op.TRUE
            } else if (strictFilter) {
                if (values.contains("null") || values.contains(null)) {
                    SingleValueInListOp(
                        exposedExpression.castToString(),
                        values.mapNotNull { it?.toString() }) or exposedExpression.isNull() or (exposedExpression.castToString() eq stringLiteral(
                        ""
                    ))
                } else {
                    SingleValueInListOp(exposedExpression.castToString(), values.mapNotNull { it?.toString() })
                }
            } else {
                OrOp(values.map {
                    if (it == "null" || it == null) { //с фронта приходит как обозначение пустых значений
                        IsNullOp(exposedExpression)
                    } else {
                        IlikeOp(
                            Cast(exposedExpression, TextColumnType()),
                            LiteralOp(UnescapedTextColumnType(), "'%%' || '$it' || '%%'"),
                            true
                        )
                    }
                })
            }
        }

        FilterCondition.notContain -> {
            if (values.isEmpty()) {
                Op.TRUE
            } else if (strictFilter) {
                if (values.contains("null") || values.contains(null)) {
                    exposedExpression.castToString() notInList values.mapNotNull { value -> value?.toString() } and exposedExpression.isNotNull()
                } else {
                    exposedExpression.castToString() notInList values.mapNotNull { value -> value?.toString() } or exposedExpression.isNull()
                }
            } else {
                AndOp(values.map {
                    if (it == "null" || it == null) { //с фронта приходит как обозначение пустых значений
                        exposedExpression.isNotNull() or (exposedExpression.castToString() neq stringLiteral(""))
                    } else {
                        IlikeOp(
                            Cast(exposedExpression, TextColumnType()),
                            LiteralOp(UnescapedTextColumnType(), "'%%' || '$it' || '%%'"),
                            like = false
                        ) or (exposedExpression.isNull())
                    }
                })
            }
        }

        FilterCondition.greater -> {
            exposedExpression.compare(values.filterNotNull(), type, ">=")
        }

        FilterCondition.less -> {
            exposedExpression.compare(values.filterNotNull(), type, "<=")
        }

        FilterCondition.equal -> {
            when (type) {
                ColumnType.DATE -> SingleValueInListOp(
                    exposedExpression.castTo(JavaLocalDateColumnType()),
                    (values as List<String>).map(LocalDate::parse)
                )

                ColumnType.TIME -> SingleValueInListOp(
                    exposedExpression.castTo(JavaLocalTimeColumnType()),
                    (values as List<String>).map(LocalTime::parse)
                )

                ColumnType.INSTANT -> {
                    val values = values as List<String>
                    try {
                        Instant.parse(values[0])
                        SingleValueInListOp(
                            exposedExpression.castTo(JavaInstantColumnType()),
                            (values.map(Instant::parse))
                        )
                    } catch (_: DateTimeParseException) {
                        try {
                            LocalDateTime.parse(values[0])
                            SingleValueInListOp(
                                exposedExpression.castTo(JavaInstantColumnType()),
                                values.map { LocalDateTime.parse(it).atZone(moscowZoneId).toInstant() })
                        } catch (_: DateTimeParseException) {
                            OrOp(
                                values.map { value ->
                                    val date = LocalDate.parse(value)
                                    val maxTime = LocalTime.parse(
                                        LocalTime.MAX.format(DateTimeFormatter.ofPattern("HH:mm:ss.SSS"))
                                    )
                                    val startDay = date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
                                    val endDay = date.atTime(maxTime).atZone(ZoneId.systemDefault()).toInstant()
                                    Between(
                                        expr = exposedExpression,
                                        from = LiteralOp(JavaInstantColumnType(), startDay),
                                        to = LiteralOp(JavaInstantColumnType(), endDay)
                                    )
                                }
                            )
                        }
                    }
                }

                ColumnType.STRING -> {
                    OrOp(
                        values.map { value ->
                            exposedExpression eq LiteralOp(TextColumnType(), value.toString())
                        }
                    )
                }

                ColumnType.BOOLEAN -> {
                    exposedExpression eq booleanLiteral(true)
                }

                else -> {
                    OrOp(
                        values.map { value ->
                            LessOp(
                                AbsFunction(
                                    exposedExpression.castTo<Double>(DoubleColumnType()) minus (doubleLiteral((value as Number).toDouble()))
                                ),
                                LiteralOp(DoubleColumnType(), precision)
                            )
                        }
                    )
                }
            }
        }


        FilterCondition.notEqual -> {
            when (type) {
                ColumnType.DATE -> NotOp(
                    SingleValueInListOp(
                        exposedExpression.castTo(JavaLocalDateColumnType()),
                        (values as List<String>).map(LocalDate::parse)
                    )
                )

                ColumnType.TIME -> NotOp(
                    SingleValueInListOp(
                        exposedExpression.castTo(JavaLocalTimeColumnType()),
                        (values as List<String>).map(LocalTime::parse)
                    )
                )

                ColumnType.INSTANT -> {
                    val values = values as List<String>
                    try {
                        Instant.parse(values[0])
                        NotOp(
                            SingleValueInListOp(
                                exposedExpression.castTo(JavaInstantColumnType()),
                                (values.map(Instant::parse))
                            )
                        )
                    } catch (_: DateTimeParseException) {
                        try {
                            LocalDateTime.parse(values[0])
                            NotOp(
                                SingleValueInListOp(
                                    exposedExpression.castTo(JavaInstantColumnType()),
                                    values.map { LocalDateTime.parse(it).atZone(moscowZoneId).toInstant() })
                            )
                        } catch (_: DateTimeParseException) {
                            OrOp(
                                values.map { value ->
                                    val date = LocalDate.parse(value)
                                    val maxTime = LocalTime.parse(
                                        LocalTime.MAX.format(DateTimeFormatter.ofPattern("HH:mm:ss.SSS"))
                                    )
                                    val startDay = date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
                                    val endDay = date.atTime(maxTime).atZone(ZoneId.systemDefault()).toInstant()
                                    NotOp(
                                        Between(
                                            expr = exposedExpression,
                                            from = LiteralOp(JavaInstantColumnType(), startDay),
                                            to = LiteralOp(JavaInstantColumnType(), endDay)
                                        )
                                    )
                                }
                            )
                        }
                    }
                }

                ColumnType.STRING -> {
                    AndOp(
                        values.map { value ->
                            exposedExpression neq LiteralOp(TextColumnType(), value.toString())
                        }
                    )
                }

                ColumnType.BOOLEAN -> {
                    exposedExpression neq booleanLiteral(true)
                }

                else -> {
                    AndOp(
                        values.map { value ->
                            GreaterOp(
                                AbsFunction(
                                    exposedExpression.castTo<Double>(DoubleColumnType()) minus (doubleLiteral((value as Number).toDouble()))
                                ),
                                LiteralOp(DoubleColumnType(), precision)
                            )
                        }
                    )
                }
            }
        }

        FilterCondition.nullOrEmpty -> {
            if (type == ColumnType.STRING) {
                (exposedExpression.isNull() or (exposedExpression eq stringLiteral("")))
            } else {
                exposedExpression.isNull()
            }
        }

        FilterCondition.notNullOrEmpty -> {
            if (type == ColumnType.STRING) {
                (exposedExpression.isNotNull() and (exposedExpression neq stringLiteral("")))
            } else {
                exposedExpression.isNotNull()
            }
        }
    }
}

fun SqlExpressionBuilder.buildJoinPredicate(
    query1: QueryAlias,
    query2: QueryAlias,
    values: List<Pair<Expression<*>?, Expression<*>?>>
): Op<Boolean> {
    return AndOp(
        values.map { (l, r) ->
            val left = when (l) {
                is ExpressionAlias -> query1[l as ExpressionAlias<Any>]
                else -> query1[l as Column<Any>]
            }
            val right = when (r) {
                is ExpressionAlias -> query2[r as ExpressionAlias<Any>]
                else -> query2[r as Column<Any>]
            }
            left eq right
        }
    )
}

private fun Expression<*>.compare(values: List<Any>, type: ColumnType, opSign: String): Op<Boolean> {
    return when (type) {
        ColumnType.BOOLEAN -> CustomComparisonOp(
            this, LiteralOp(TextColumnType(), values[0].toString()).castTo(BooleanColumnType()), opSign
        )

        ColumnType.DATE -> CustomComparisonOp(
            this,
            LiteralOp(TextColumnType(), values[0].toString()).castTo(JavaLocalDateColumnType()),
            opSign
        )

        ColumnType.INSTANT -> {
            val value = (values[0] as String).let { value ->
                try {
                    Instant.parse(value)
                } catch (_: DateTimeParseException) {
                    val localDateTime = try {
                        LocalDateTime.parse(value)
                    } catch (_: DateTimeParseException) {
                        if (opSign == ">=") {
                            LocalDate.parse(value).atStartOfDay()
                        } else {
                            val maxTime = LocalTime.parse(
                                LocalTime.MAX.format(DateTimeFormatter.ofPattern("HH:mm:ss.SSS"))
                            )
                            LocalDate.parse(value).atTime(maxTime)
                        }
                    }
                    localDateTime.atZone(ZoneId.systemDefault()).toInstant()
                }
            }
            CustomComparisonOp(this, LiteralOp(JavaInstantColumnType(), value), opSign)
        }

        ColumnType.STRING -> CustomComparisonOp(this, LiteralOp(TextColumnType(), values[0].toString()), opSign)
        ColumnType.NUMBER -> {
            val correction = if (opSign == ">=") (-PRECISION_THRESHOLD) else PRECISION_THRESHOLD
            CustomComparisonOp(
                this,
                LiteralOp(DoubleColumnType(), (values[0] as Number).toDouble() + correction),
                opSign
            )
        }

        ColumnType.TIME -> CustomComparisonOp(
            this,
            LiteralOp(TextColumnType(), values[0].toString()).castTo(JavaLocalTimeColumnType()),
            opSign
        )
    }
}

class CustomComparisonOp(expr1: Expression<*>, expr2: Expression<*>, opSign: String) :
    ComparisonOp(expr1, expr2, opSign)

class UnescapedTextColumnType() : TextColumnType() {
    override fun nonNullValueToString(value: String): String = buildString {
        append(value)
    }
}

class IlikeOp(expr1: Expression<*>, expr2: Expression<*>, like: Boolean) :
    ComparisonOp(expr1, expr2, if (like) "ILIKE" else "NOT ILIKE")

val mapper = ObjectMapper().registerKotlinModule()

inline fun <reified T> Table.jsonb(name: String): Column<T> =
    registerColumn(name, object : org.jetbrains.exposed.sql.ColumnType<T & Any>() {
        override fun sqlType(): String =
            currentDialect.dataTypeProvider.jsonBType()

        override fun valueFromDB(value: Any): T? {
            return when (value) {
                is PGobject -> mapper.readValue(value.value!!, T::class.java)!!
                is String -> mapper.readValue(value, T::class.java)!!
                is T -> value
                else -> {
                    val pgObject = PGobject()
                    pgObject.type = "jsonb"
                    pgObject.value = mapper.writeValueAsString(value)
                    mapper.readValue(pgObject.value!!, T::class.java)!!
                }
            }
        }

        override fun notNullValueToDB(value: T & Any): Any {
            val pgObject = PGobject()
            pgObject.type = "jsonb"
            pgObject.value = mapper.writeValueAsString(value)
            return pgObject
        }
    })

inline fun <reified T> Table.checkedJsonb(name: String): Column<Result<T>> =
    registerColumn(name, object: org.jetbrains.exposed.sql.ColumnType<Result<T>>() {
        override fun sqlType(): String =
            currentDialect.dataTypeProvider.jsonBType()

        override fun valueFromDB(value: Any): Result<T> =
            try {
                Result.success(mapper.readValue((value as PGobject).value!!, T::class.java))
            } catch (e: JsonProcessingException) {
                Result.failure(e)
            }

        override fun notNullValueToDB(value: Result<T>): Any =
            mapper.writeValueAsString(value.getOrNull())
    })

fun Table.jsonbMap(name: String): Column<Map<String, Any?>> =
    jsonb(name)

private object IntervalColumnType : org.jetbrains.exposed.sql.ColumnType<Duration>() {
    override fun sqlType(): String = "INTERVAL"

    override fun valueFromDB(value: Any): Duration {
        value as PGInterval
        val time = Date()
        val start = time.toInstant()
        value.add(time)
        return Duration.between(start, time.toInstant())
    }

    override fun notNullValueToDB(value: Duration): Any {
        return PGInterval(
            0,
            0,
            value.toDaysPart().toInt(),
            value.toHoursPart(),
            value.toMinutesPart(),
            value.toSecondsPart().toDouble() + (value.toNanosPart() / 1_000_000_000.0)
        )
    }
}

fun Table.interval(name: String): Column<Duration> =
    registerColumn(name, IntervalColumnType)

fun countWithFilter(condition: Expression<Boolean>): Function<Long> = object : Function<Long>(LongColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        +"COUNT(CASE WHEN "
        +condition
        +" THEN 1 ELSE NULL END)"
    }
}

fun countOrNull(condition: Expression<Boolean>): Function<Long?> {
    return object : Function<Long?>(LongColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
            // Используем CASE для проверки, есть ли хотя бы одно не-NULL значение
            +"CASE "
            +"WHEN COUNT(CASE WHEN "
            +condition
            +" IS NOT NULL THEN 1 END) = 0 THEN NULL "
            +"ELSE COUNT(CASE WHEN "
            +condition
            +" THEN 1 END) "
            +"END"
        }
    }
}

fun Query.applyGeoFilter(geoFilter: GeoFilter) = apply {
    val (mr, atp, mvz, retailNetwork, atpType, mvzType, territory) = geoFilter
    if (!mr.isNullOrEmpty()) {
        this.andWhere { OrganizationalUnitsTimelineTable.mrId inList mr }
    }
    if (!atp.isNullOrEmpty()) {
        this.andWhere { OrganizationalUnitsTimelineTable.atpId inList atp }
    }
    if (!mvz.isNullOrEmpty()) {
        this.andWhere { OrganizationalUnitsTimelineTable.mvzId inList mvz }
    }
    if (!mvzType.isNullOrEmpty()) {
        this.andWhere { OrganizationalUnitsTimelineTable.mvzType inList mvzType }
    }
    if (!atpType.isNullOrEmpty()) {
        this.andWhere { OrganizationalUnitsTimelineTable.atpType inList atpType }
    }
    if (!retailNetwork.isNullOrEmpty()) {
        this.andWhere { OrganizationalUnitsTimelineTable.retailNetwork inList retailNetwork }
    }
    if (!territory.isNullOrEmpty()) {
        this.andWhere { OrganizationalUnitsTimelineTable.territoryId inList territory }
    }
}

fun refreshOrgUnit() = transaction {
    addLogger(StdOutSqlLogger)
    exec(" REFRESH MATERIALIZED VIEW organizational_units_timeline ")
    ORG_UNIT_VERSION.incrementAndGet()
}

fun getGeoFilterPredicate(
    subQuery: QueryAlias,
    geoFilter: GeoFilter
): Op<Boolean> {
    val (mr, atp, mvz, retailNetwork, atpType, mvzType, territory) = geoFilter
    return AndOp(
        listOfNotNull(
            if (!mr.isNullOrEmpty()) subQuery[OrganizationalUnitsTimelineTable.mrId] inList mr else null,
            if (!atp.isNullOrEmpty()) subQuery[OrganizationalUnitsTimelineTable.atpId] inList atp else null,
            if (!mvz.isNullOrEmpty()) subQuery[OrganizationalUnitsTimelineTable.mvzId] inList mvz else null,
            if (!mvzType.isNullOrEmpty()) subQuery[OrganizationalUnitsTimelineTable.mvzType] inList mvzType else null,
            if (!atpType.isNullOrEmpty()) subQuery[OrganizationalUnitsTimelineTable.atpType] inList atpType else null,
            if (!retailNetwork.isNullOrEmpty()) subQuery[OrganizationalUnitsTimelineTable.retailNetwork] inList retailNetwork else null,
            if (!territory.isNullOrEmpty()) subQuery[OrganizationalUnitsTimelineTable.territoryId] inList territory else null
        )
    )
}

private object CommaSeparatedList : org.jetbrains.exposed.sql.ColumnType<List<String>>() {
    override fun sqlType(): String =
        currentDialect.dataTypeProvider.textType()

    override fun valueFromDB(value: Any): List<String> =
        value.toString().split(',')

    override fun notNullValueToDB(value: List<String>): Any =
        value.joinToString(separator = ",")
}

fun Table.commaSeparatedList(name: String): Column<List<String>> =
    registerColumn(name, CommaSeparatedList)

class CoalesceList(private val list: List<Expression<*>>) : Function<String>(TextColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        list.appendTo(
            prefix = "COALESCE(",
            postfix = ")",
            separator = ", "
        ) { +it }

    }
}

object CountStar : Function<Long>(LongColumnType()), WindowFunction<Long> {
    override fun over(): WindowFunctionDefinition<Long> =
        WindowFunctionDefinition(LongColumnType(), this)

    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append("COUNT(*)")
        }
    }
}

fun <T : Any> nullOf(type: IColumnType<T>): ExpressionWithColumnType<T?> =
    object : ExpressionWithColumnType<T?>() {
        override val columnType: IColumnType<T>
            get() = type

        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder.append("NULL")
        }
    }

fun <T> nullOf(): Expression<T?> =
    object: Expression<T?>() {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder.append("NULL")
        }
    }

@Suppress("UNCHECKED_CAST")
fun <T : Number> zeroLiteral(type: IColumnType<T>) =
    LiteralOp(type, 0 as T)

fun <T> case(
    default: ExpressionWithColumnType<out T>,
    firstCase: Pair<Expression<out Boolean?>, Expression<out T>>,
    vararg otherCases: Pair<Expression<out Boolean?>, Expression<out T>>
): ExpressionWithColumnType<T> =
    object : ExpressionWithColumnType<T>() {
        override val columnType: IColumnType<T & Any>
            get() = default.columnType as IColumnType<T & Any>

        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                append("CASE WHEN ", firstCase.first, " THEN ", firstCase.second)
                otherCases.forEach { case ->
                    append(" WHEN ", case.first, " THEN ", case.second)
                }
                append(" ELSE ", default, " END")
            }
        }
    }

@Suppress("UNCHECKED_CAST")
fun <T, DIV> ExpressionWithColumnType<T>.safeDiv(
    divider: Expression<DIV>,
    zero: Expression<out DIV>,
    division: (ExpressionWithColumnType<T>, Expression<DIV>) -> ExpressionWithColumnType<out T?> =
        { divisible: ExpressionWithColumnType<T>, div: Expression<DIV> -> divisible / (div as Expression<T>) },
    default: Expression<out T?> = nullOf(columnType)
): ExpressionWithColumnType<T?> =
    case(
        default = division(this, divider),
        firstCase = (divider eq zero) to default
    )

@JvmName("safeDivNumber")
infix fun <T : Number?, DIV : T?> ExpressionWithColumnType<T>.safeDiv(divider: ExpressionWithColumnType<DIV>) =
    safeDiv(divider = divider, zero = zeroLiteral(divider.columnType))

@JvmName("safeDivDuration")
infix fun <DIV : Number?> ExpressionWithColumnType<Duration?>.safeDiv(divider: ExpressionWithColumnType<DIV>) =
    safeDiv(
        divider = divider,
        zero = zeroLiteral(divider.columnType),
        division = { divisible: Expression<out Duration?>, div: Expression<out Number?> -> divisible / div }
    )

@JvmName("safeDivDurationNotNull")
infix fun <DIV : Number?> ExpressionWithColumnType<Duration>.safeDiv(divider: ExpressionWithColumnType<DIV>) =
    safeDiv(
        divider = divider,
        zero = zeroLiteral(divider.columnType),
        division = { divisible: Expression<out Duration?>, div: Expression<out Number?> -> divisible / div }
    )

@Suppress("UNCHECKED_CAST")
@JvmName("divNotNull")
infix operator fun <T : Number> Expression<Duration>.div(other: Expression<T>) =
    ((this as Expression<Duration?>) / other) as CustomOperator<Duration>

infix operator fun Expression<out Duration?>.div(other: Expression<out Number?>) =
    CustomOperator<Duration?>(
        operatorName = "/",
        columnType = IntervalColumnType,
        expr1 = this,
        expr2 = other
    )

infix operator fun Expression<out Float?>.minus(other: Expression<out Float?>) =
    CustomOperator<Float?>(
        operatorName = "-",
        columnType = FloatColumnType(),
        expr1 = this,
        expr2 = other
    )

fun dateLiteral(date: LocalDate): ExpressionWithColumnType<LocalDate> =
    object : ExpressionWithColumnType<LocalDate>() {
        override val columnType: IColumnType<LocalDate>
            get() = JavaLocalDateColumnType()

        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder.append("date '", date.toString(), "'")
        }
    }

fun daysBetween(from: Expression<LocalDate>, to: Expression<LocalDate>): CustomOperator<Int> =
    CustomOperator("-", IntegerColumnType(), to, from)

enum class DateTruncPeriod {
    DAY,
    WEEK,
    MONTH,
    QUARTER,
    YEAR
}

fun dateTrunc(
    period: DateTruncPeriod,
    date: Expression<LocalDate>
): Function<LocalDate> = object : Function<LocalDate>(JavaLocalDateColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append("date_trunc('", period.name, "', ", date, ")::date")
        }
    }
}

fun String.toInterval(): Function<LocalDate> = object : Function<LocalDate>(JavaLocalDateColumnType()) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append("'", this@toInterval, "'::interval")
        }
    }
}

@JvmName("toSecondsNotNull")
fun Expression<Duration>.toSeconds(): Function<Double> =
    object : Function<Double>(DoubleColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                append("EXTRACT(EPOCH FROM ", this@toSeconds, ")")
            }
        }
    }

fun Expression<LocalDate>.extractYear(): Function<Int> =
    object : Function<Int>(IntegerColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                append("EXTRACT(YEAR FROM ", this@extractYear, ")")
            }
        }
    }

@Suppress("UNCHECKED_CAST")
fun Expression<Duration?>.toSeconds(): Function<Double?> =
    (this as Expression<Duration>).toSeconds() as Function<Double?>

fun Expression<Duration>.toHours() =
    toSeconds() / 3600.0

@Suppress("UNCHECKED_CAST")
fun Expression<Duration?>.toHours() =
    (this as Expression<Duration>).toHours() as ExpressionWithColumnType<Double?>

fun Expression<Duration>.toDays() =
    toSeconds() / (3600.0 * 24)

@Suppress("UNCHECKED_CAST")
fun Expression<Duration?>.toDays() =
    (this as Expression<Duration>).toDays() as ExpressionWithColumnType<Double?>

fun <T> sumWithFilter(condition: Expression<Boolean>, value: Expression<T>, zero: ExpressionWithColumnType<T>) =
    case(default = zero, firstCase = condition to value).sum()

fun sumWithFilter(condition: Expression<Boolean>, value: Expression<Double>) =
    sumWithFilter(condition, value, doubleLiteral(0.0))

@Suppress("UNCHECKED_CAST")
fun <T> ExpressionWithColumnType<T>.toNullable() = this as ExpressionWithColumnType<T?>

inline val <T> T.nullable: T?
    get() = this

@Schema(description = "Гранулярность отчета.")
enum class Granularity(val description: String, val interval: String) {
    @JsonProperty("Day")
    @Schema(description = "День.")
    DAY("День", "1 day"),

    @JsonProperty("Week")
    @Schema(description = "Неделя.")
    WEEK("Календарная неделя", "7 days"),

    @JsonProperty("Reporting_Week")
    @Schema(description = "Отчетная неделя (пт-чт).")
    REPORTING_WEEK("Отчетная неделя", "7 days"),

    @JsonProperty("Month")
    @Schema(description = "Месяц.")
    MONTH("Месяц", "1 month"),

    @JsonProperty("Quarter")
    @Schema(description = "Квартал.")
    QUARTER("Квартал", "3 months"),

    @JsonProperty("Year")
    @Schema(description = "Год.")
    YEAR("Год", "1 year")
}

@Suppress("UNCHECKED_CAST")
@JvmName("getGranularityFromNotNull")
fun getGranularityFrom(granularity: Granularity, date: Expression<LocalDate>) =
    getGranularityFrom(granularity, date as Expression<LocalDate?>) as CustomFunction<LocalDate>

fun getGranularityFrom(granularity: Granularity, date: Expression<LocalDate?>) = object : CustomFunction<LocalDate?>(
    "getGranularityFrom", JavaLocalDateColumnType(), stringLiteral(granularity.name), date
) {}

fun getGranularityTo(granularity: Granularity, date: Expression<LocalDate>) = object : CustomFunction<LocalDate>(
    "getGranularityTo", JavaLocalDateColumnType(), stringLiteral(granularity.name), date
) {}

@Suppress("UNCHECKED_CAST")
@JvmName("getGranularityLabelNotNull")
fun getGranularityLabel(granularity: Granularity, date: Expression<LocalDate>) =
    getGranularityLabel(granularity, date as Expression<LocalDate?>) as CustomFunction<String>

fun getGranularityLabel(granularity: Granularity, date: Expression<LocalDate?>) = object : CustomFunction<String?>(
    "getGranularityLabel", TextColumnType(), stringLiteral(granularity.name), date
) {}

fun Expression<LocalDate>.plusDays(days: Int) = object : ExpressionWithColumnType<LocalDate>() {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append("(")
            append(this@plusDays)
            append(" + (")
            append(days.toString())
            append("))")
        }
    }

    override val columnType: IColumnType<LocalDate>
        get() = JavaLocalDateColumnType()
}

fun Expression<LocalDate>.plusInterval(interval: String) = object : ExpressionWithColumnType<LocalDate>() {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append("(")
            append(this@plusInterval)
            append(" + '")
            append(interval)
            append("'::interval)::date")
        }
    }

    override val columnType: IColumnType<LocalDate>
        get() = JavaLocalDateColumnType()
}

class GranularityPeriodsQuery(
    val query: Query,
    val periodFrom: ExpressionAlias<LocalDate>,
    val periodTo: ExpressionAlias<LocalDate>,
    val label: ExpressionAlias<String>,
    val partPeriod: ExpressionAlias<Boolean>,
    val calculatedPeriodFrom: ExpressionAlias<LocalDate>,
    val calculatedPeriodTo: ExpressionAlias<LocalDate>
)

fun getGranularityPeriodsQuery(granularity: Granularity, from: LocalDate, to: LocalDate): GranularityPeriodsQuery {
    if (from > to) {
        throw WrongRequestDataException("from must be less or equal than to")
    }
    val (daysSubquery, dayAlias) = run {
        var dayAlias: ExpressionAlias<LocalDate>? = null
        val daysSubquery = generateSequence(from) {
            it.plusDays(1)
        }.takeWhile {
            it <= to
        }.map {
            val localDayAlias = dateLiteral(it).alias("d")
            if (dayAlias == null) {
                dayAlias = localDayAlias
            }
            UnitTable.select(localDayAlias)
        }.reduce(AbstractQuery<*>::unionAll).alias("days")
        daysSubquery to (daysSubquery[dayAlias!!].alias("d").aliasOnlyExpression())
    }
    val periodFrom = getGranularityFrom(granularity, dayAlias).alias("periodFrom")
    val periodTo = getGranularityTo(granularity, dayAlias).alias("periodTo")
    val label = getGranularityLabel(granularity, dayAlias).alias("granularityLabel")
    val partPeriod =
        ((periodFrom.delegate less dateLiteral(from)) or (periodTo.delegate greater dateLiteral(to))).alias("partPeriod")
    val calculatedPeriodFrom = Greatest(dateLiteral(from), periodFrom.delegate).alias("calculatedPeriodFrom")
    val calculatedPeriodTo = Least(dateLiteral(to), periodTo.delegate).alias("calculatedPeriodTo")
    val query = daysSubquery.select(
        periodFrom,
        periodTo,
        label,
        partPeriod,
        calculatedPeriodFrom,
        calculatedPeriodTo
    ).groupBy(
        label.delegate,
        periodFrom.delegate,
        periodTo.delegate
    ).orderBy(periodFrom.delegate)
    return GranularityPeriodsQuery(
        query = query,
        periodFrom = periodFrom,
        periodTo = periodTo,
        label = label,
        partPeriod = partPeriod,
        calculatedPeriodFrom = calculatedPeriodFrom,
        calculatedPeriodTo = calculatedPeriodTo
    )
}

fun getTupleCondition(columns: List<Expression<*>>, values: List<List<*>>) = object : Op<Boolean>() {
    init {
        values.forEach { list ->
            require(columns.size == list.size)
        }
    }

    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            if (columns.isEmpty()) {
                append(TRUE)
            } else if (values.isEmpty()) {
                append(FALSE)
            } else {
                append("(")
                iter(columns) {
                    append(it)
                }
                append(" in ")
                iter(values) { list ->
                    iter(list) {
                        when (it) {
                            is Long -> append(longLiteral(it))
                            is Int -> append(intLiteral(it))
                            is String -> append(stringLiteral(it))
                            is Double -> append(doubleLiteral(it))
                            is Boolean -> append(booleanLiteral(it))
                            null -> append("NULL")
                            else -> throw RuntimeException("unknown value: $it")
                        }
                    }
                }
                append(")")
            }
        }
    }

    private fun <T> QueryBuilder.iter(list: List<T>, action: QueryBuilder.(T) -> Unit) {
        var first = true
        for (item in list) {
            append(if (first) "(" else ",")
            action(item)
            first = false
        }
        append(")")
    }
}

fun Expression<LocalDate>.minusDays(days: Int = 1): ExpressionWithColumnType<LocalDate> =
    object : ExpressionWithColumnType<LocalDate>() {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                append("(")
                append(this@minusDays)
                append(" - interval '")
                append(days.toString())
                append("')::date")
            }
        }

        override val columnType: IColumnType<LocalDate>
            get() = JavaLocalDateColumnType()
    }

infix operator fun Expression<Int>.plus(other: Expression<Int>) =
    CustomOperator<Int>(
        operatorName = "+",
        columnType = IntegerColumnType(),
        expr1 = this,
        expr2 = other
    )

infix operator fun <T, S : T> Expression<T>.times(other: Expression<S>) =
    with(SqlExpressionBuilder) {
        (this@Expression as ExpressionWithColumnType<T>) * other
    }

fun Column<*>.ddistinctOn(vararg extraColumns: Column<*>) = DistinctOn(this, extraColumns)

class DistinctOn<T>(expr: Column<T>, columns: Array<out Column<*>>) : Function<T>(expr.columnType) {

    private val distinctNames = listOf(expr, *columns)
        .joinToString(
            separator = ", ",
            transform = {
                "${it.table.tableName}.${it.name}"
            }
        )

    private val colName = expr.table.tableName + "." + expr.name

    override fun toQueryBuilder(queryBuilder: QueryBuilder) {
        queryBuilder {
            append(" DISTINCT ON ($distinctNames) $colName ")
        }
    }
}

abstract class StringEntityClass<out E : Entity<String>>(table: IdTable<String>, entityType: Class<E>? = null) :
    EntityClass<String, E>(table, entityType)

open class StringIdTable(name: String = "", columnName: String = "id") : IdTable<String>(name) {
    override val id: Column<EntityID<String>> = text(columnName).entityId()
    override val primaryKey by lazy { super.primaryKey ?: PrimaryKey(id) }
}

fun <T> Column<T>.aliasOnlyExpression(): Expression<T> {
    return this.alias(this.name).aliasOnlyExpression()
}

/**
 * агрегационная функция, оба параметра - агрегацонные, обозначают начало и конец периода.
 * @return объединяет все периоды и возвращает количество дней в этом объединении.
 */
fun numDaysInUnionDateSegments(
    startIncluding: Expression<LocalDate>,
    endExcluding: Expression<LocalDate>
) = object : CustomFunction<Int>(
    "num_days_in_union_date_segments",
    IntegerColumnType(),
    startIncluding,
    endExcluding
) {}

infix fun <T, S1 : T?, S2 : T?> Expression<in S1>.inExpressions(expressions: List<Expression<in S2>>): Op<Boolean> =
    object: Op<Boolean>(), ComplexExpression {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                if (expressions.isEmpty()) {
                    +FALSE
                } else {
                    append(this@inExpressions)
                    append(" IN (")
                    append(expressions[0])
                    (1 until expressions.size).forEach { index ->
                        append(", ")
                        append(expressions[index])
                    }
                    append(")")
                }
            }
        }
    }

infix fun <T, S1 : T?, S2 : T?> Expression<in S1>.notInExpressions(expressions: List<Expression<in S2>>): Op<Boolean> =
    object: Op<Boolean>(), ComplexExpression {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                if (expressions.isEmpty()) {
                    +TRUE
                } else {
                    append(this@notInExpressions)
                    append(" NOT IN (")
                    append(expressions[0])
                    (1 until expressions.size).forEach { index ->
                        append(", ")
                        append(expressions[index])
                    }
                    append(")")
                }
            }
        }
    }

class CoalesceTyped<T>(
    columnType: IColumnType<T & Any>,
    private val expr: Expression<out T>,
    private vararg val alternate: Expression<out T>
) : Function<T & Any>(columnType) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder): Unit = queryBuilder {
        (listOf(expr) + alternate).appendTo(
            prefix = "COALESCE(",
            postfix = ")",
            separator = ", "
        ) { +it }
    }

    fun add(another: Expression<out T>): CoalesceTyped<T> {
        val newArray = Array(alternate.size + 1) {
            if (it < alternate.size) {
                alternate[it]
            }
            another
        }
        return CoalesceTyped(columnType, expr, *newArray)
    }
}

fun <T> ExpressionWithColumnType<T>.coalesce(vararg alternate: Expression<out T>) =
    CoalesceTyped(columnType, this, *alternate)

fun <T> Expression<T>.coalesceAdd(type: IColumnType<T & Any>, another: Expression<out T>) =
    if (this is CoalesceTyped<T>) add(another) else CoalesceTyped(type, this).add(another)

fun <T> ExpressionWithColumnType<T>.coalesceAdd(another: Expression<out T>) =
    coalesceAdd(columnType, another)
