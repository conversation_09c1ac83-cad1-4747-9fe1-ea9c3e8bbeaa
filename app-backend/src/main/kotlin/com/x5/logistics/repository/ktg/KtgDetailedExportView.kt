package com.x5.logistics.repository.ktg

import com.x5.logistics.rest.dto.GeoFilter
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object KtgDetailedExportView : Table("ktg_detailed_export_view") {
    val vehicleDate = date("vehicle_date").nullable()
    val mvzId = text("mvz_id").nullable()
    val mvzName = text("mvz_name").nullable()
    val vehicleType = text("vehicle_type").nullable()
    val vehicleGroup = text("vehicle_group").nullable()
    val vehicleBrand = text("vehicle_brand").nullable()
    val vehicleModel = text("vehicle_model").nullable()
    val vehicleTonnage = float("vehicle_tonnage").nullable()
    val vehicleLicense = text("vehicle_license").nullable()
    val equnr = long("equnr").nullable()
    val vehicleVin = text("vehicle_vin").nullable()
    val rcSubmitId = text("rc_submit_id").nullable()
    val rcSubmitName = text("rc_submit_name").nullable()
    val trailerLicense = text("trailer_license").nullable()
    val retailNetwork = text("retail_network").nullable()
    val atpType = text("atp_type").nullable()
    val mrName = text("mr_name").nullable()
    val atpName = text("atp_name").nullable()
    val mvzType = text("mvz_type").nullable()
    val tsType = text("ts_type").nullable()
    val marka = text("marka").nullable()
    val model = text("model").nullable()
    val loadWgt = double("load_wgt").nullable()
    val tsDataEqunr = long("ts_data_equnr").nullable()
    val fleetNum = text("fleet_num").nullable()
    val tsGroup = text("ts_group").nullable()
    val status = text("status").nullable()
    val reason = text("reason").nullable()
    val ktg = integer("ktg").nullable()
    val rg = integer("rg").nullable()
    val createDateYear = integer("create_date_year").nullable()
    val terId = long("territory_id").nullable()
    val terName = text("territory_name").nullable()
    val repshopName = text("repshop_name").nullable()
    val vehicleGbo = text("vehicle_gbo").nullable()
    val dateYear = integer("date_year").nullable()
    val dateQuarter = text("date_quarter").nullable()
    val dateMonth = text("date_month").nullable()
    val dateWeek = text("date_week").nullable()
    val dateReportWeek = text("date_report_week").nullable()
    val dateDay = text("date_day").nullable()
    val atpId = long("atp_id").nullable()
    val mrId = long("mr_id").nullable()

    fun getGeoFilter(geofilter: GeoFilter?): List<Op<Boolean>> {
        val filters = mutableListOf<Op<Boolean>>()
        if (geofilter != null) {
            geofilter.mr?.let {
                if (it.isNotEmpty()) {
                    filters.add(mrId inList it)
                }
            }
            geofilter.atp?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpId inList it)
                }
            }
            geofilter.mvz?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzId inList it)
                }
            }
            geofilter.retailNetwork?.let {
                if (it.isNotEmpty()) {
                    filters.add(retailNetwork inList it)
                }
            }
            geofilter.atpType?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpType inList it)
                }
            }
            geofilter.mvzType?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzType inList it.filterNotNull())
                }
            }
        }
        return filters.toList()
    }
}
