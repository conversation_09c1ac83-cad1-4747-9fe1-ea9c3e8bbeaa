package com.x5.logistics.data.repair.mechanics

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object MechanicRepairServicesTable : Table("repair_services") {
    val from = date("repair_start_date")
    val serviceAmount = double("service_amount").nullable()
    val mechanicId = text("mechanic_id").nullable()
    val mechanicRepshopId = long("mechanic_repshop_id")
    val mechanicMvzId = text("mechanic_mvz_id")
}
