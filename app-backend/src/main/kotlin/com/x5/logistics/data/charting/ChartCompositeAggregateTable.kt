package com.x5.logistics.data.charting

import com.x5.logistics.repository.jsonb
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateSettingsDto
import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.datetime

object ChartCompositeAggregateTable : LongIdTable("combine_aggregates") {
    val name = varchar("name", 50)
    val settings = jsonb<ChartCompositeAggregateSettingsDto>("settings")
    val deleted = bool("deleted").default(false)
    val createdBy = varchar("created_by", 255)
    val createdAt = datetime("created_at")
}