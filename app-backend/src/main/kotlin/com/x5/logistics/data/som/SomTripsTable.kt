package com.x5.logistics.data.som

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.time
import org.jetbrains.exposed.sql.javatime.timestamp

object SomTripsTable : Table("som_trips") {
    val numTripTms = long("num_trip_tms").nullable()
    val numTripNq = long("num_trip_nq").nullable()
    val gosNumTs = text("gos_num_ts").nullable()
    val equnr = long("equnr").nullable()
    val vehicleMvzId = text("vehicle_mvz_id").nullable()
    val vehicleFakeMvzId = text("vehicle_fake_mvz_id").nullable()
    val finalVehicleMvzId = text("final_vehicle_mvz_id").nullable()
    val mvzId = text("mvz_id").nullable()
    val tsIsInactive = bool("ts_is_inactive")
    val tdGroup = text("td_group").nullable()
    val tdType = text("td_type").nullable()
    val tdFleetNum = text("td_fleet_num").nullable()
    val tdBrand = text("td_brand").nullable()
    val tdModel = text("td_model").nullable()
    val tdTonnage = float("td_tonnage").nullable()
    val tdCreateYear = integer("td_create_year").nullable()
    val idRc = long("id_rc").nullable()
    val rcName = text("rc_name").nullable()
    val idRcSap = text("id_rc_sap").nullable()
    val rcNameSap = text("rc_name_sap").nullable()
    val rcMvzId = text("rc_mvz_id").nullable()
    val rcFormat = text("rc_format").nullable()
    val rcRetailNetwork = text("rc_retail_network").nullable()
    val rcNqCode = integer("rc_nq_code").nullable()
    val dtPlanRc = timestamp("dt_plan_rc").nullable()
    val dtPlanRcDate = date("dt_plan_rc_date").nullable()
    val timePlanRc = time("time_plan_rc").nullable()
    val dtRegRc = timestamp("dt_reg_rc").nullable()
    val dtRegRcDate = date("dt_reg_rc_date").nullable()
    val timeRegRc = time("time_reg_rc").nullable()
    val dtArriveTs = timestamp("dt_arrive_ts").nullable()
    val statusTCode = text("status_t_code").nullable()
    val statusTDescription = text("status_t_description").nullable()
    val statusT = text("status_t").nullable()
    val statusTFinal = bool("status_t_final").nullable()
    val statusTCalculate = bool("status_t_calculate").nullable()
    val carIn = bool("car_in").nullable()
    val carInGps = bool("car_in_gps").nullable()
    val tsAtpTk = text("ts_atp_tk").nullable()
    val isDelvWndowCount = long("is_delv_wndow_count").nullable()
    val isDelvIntimeCount = long("is_delv_intime_count").nullable()
    val isPlanWndowCount = long("is_plan_wndow_count").nullable()
    val isDelvIntimeWithPlanWndowCount = long("is_delv_intime_with_plan_wndow_count").nullable()
    val isDelvWindowWithPlanWndowCount = long("is_delv_window_with_plan_wndow_count").nullable()
    val charaterDelvr  = text("charater_delvr").nullable()
    val pointsCount = long("points_count").nullable()
    val numOrderTs = text("num_order_ts").nullable()
    val nqName = text("nq_name").nullable()
    val gosNumTrailer = text("gos_num_trailer").nullable()
    val moreThanOneSapRc = bool("more_than_one_sap_rc").nullable()
    val sapRcArray = array<String>("sap_rc_array").nullable()
    val qmnum = text("qmnum").nullable()
    val qmnumArray = array<String>("qmnum_array").nullable()
    val moreThanOneQmnum = bool("more_than_one_qmnum").nullable()
    val ourTripFlag = bool("our_trip_flag").nullable()
    val ourTripText = text("our_trip_text").nullable()
    val rcNameSapLogistics = text("rc_name_sap_logistics").nullable()
    val idRcSapLogistics = text("id_rc_sap_logistics").nullable()
    val rnAndCharaterDelivery = text("rn_and_chracter_deliver").nullable()
}