package com.x5.logistics.data

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object RepairServicesWithOrgUnitView: Table("repair_services_view") {
    val terName = text("ter_name").nullable()
    val territoryId = long("territory_id").nullable()
    val mechanicMrId = long("mechanic_mr_id").nullable()
    val mechanicMrName = text("mechanic_mr_name").nullable()
    val mechanicRepshopId = long("mechanic_repshop_id").nullable()
    val mechanicRepshopName = text("mechanic_repshop_name").nullable()
    val mechanicAtpRepairName = text("mechanic_atp_repair_name").nullable()
    val serviceTerritoryId = long("service_territory_id").nullable()
    val serviceTerName = text("service_ter_name").nullable()
    val vehicleMrId = long("vehicle_mr_id").nullable()
    val vehicleMr = text("vehicle_mr").nullable()
    val vehicleAtpId = long("vehicle_atp_id").nullable()
    val vehicleAtp = text("vehicle_atp").nullable()
    val vehicleMvzId = text("vehicle_mvz_id").nullable()
    val vehicleMvzName = text("vehicle_mvz_name").nullable()
    val equnr = long("equnr")
    val repairStartDate = date("repair_start_date")
    val serviceAmount = double("service_amount")
    val repairKind = text("repair_kind")
    val vehicleRepshopName = text("vehicle_repshop_name").nullable()
    val vehicleRepshopId = long("vehicle_repshop_id").nullable()
    val mechanicId = long("mechanic_id").nullable()
    val ourWorkshop = bool("our_workshop")
}
