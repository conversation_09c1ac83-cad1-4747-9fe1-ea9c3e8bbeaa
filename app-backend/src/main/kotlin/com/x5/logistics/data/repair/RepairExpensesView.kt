package com.x5.logistics.data.repair

import com.x5.logistics.rest.dto.GeoFilter
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object RepairExpensesView : Table("repair_expenses_by_order_and_vehicle") {
    val orderId = long("order_id")
    val equnr = long("equnr")
    val startDate = date("repair_start_date")
    val mrId = long("mr_id")
    val mrName = long("mr_name")
    val atpId = long("atp_id")
    val atpName = long("atp_name")
    val atpType = text("atp_type")
    val mvzId = text("mvz_id")
    val mvzName = text("mvz_name")
    val mvzType = text("mvz_type")
    val retailNetwork = text("retail_network")
    val territoryId = long("territory_id")
    val territoryName = text("territory_name").nullable()
    val vehicleBrand = text("ts_marka")
    val vehicleTonnage = float("ts_load_wgt")
    val vehicleType = text("ts_type")
    val vehicleGroup = text("ts_group")
    val vrt = text("vrt_id")
    val vrtName = text("vrt_name")
    val vrtSubtypeId = integer("subtype_id")
    val vrtSubtypeName = text("subtype_name")
    val ourWorkshop = bool("our_workshop")
    val repairExpensesFull = double("repair_expenses_full")
    val servicesExpenses = double("services_expenses")
    val ourWorkshopServicesExpenses = double("our_workshop_services_expenses")
    val extWorkshopServicesExpenses = double("ext_workshop_services_expenses")
    val tiresServicesExpenses = double("tires_services_expenses")
    val tiresExpenses = double("tires")
    val tiresPartsExpenses = double("tires_parts")
    val partsExpenses = double("parts")
    val rubKmPlan = double("coef")

    fun getGeoFilterRepairExpenses(geofilter: GeoFilter?): List<Op<Boolean>> {
        val filters = mutableListOf<Op<Boolean>>()
        if (geofilter != null) {
            geofilter.mr?.let {
                if (it.isNotEmpty()) {
                    filters.add(mrId inList it)
                }
            }
            geofilter.atp?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpId inList it)
                }
            }
            geofilter.mvz?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzId inList it)
                }
            }
            geofilter.retailNetwork?.let {
                if (it.isNotEmpty()) {
                    filters.add(retailNetwork inList it)
                }
            }
            geofilter.atpType?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpType inList it)
                }
            }
            geofilter.mvzType?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzType inList it.filterNotNull())
                }
            }
            geofilter.territory?.let {
                if (it.isNotEmpty()) {
                    filters.add(territoryId inList it)
                }
            }
        }
        return filters.toList()
    }
}