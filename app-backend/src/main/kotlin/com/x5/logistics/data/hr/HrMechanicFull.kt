package com.x5.logistics.data.hr

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object HrMechanicFull: Table("hr_mechanic_full") {
    val fromDttm = date("from_dttm")
    val toDttm = date("to_dttm")
    val mrName = text("mr_name")
    val repshopName = text("repshop_name")
    val rate = double("rate")
    val personnelNo = text("personnel_no")
    val status = long("emp_status_dk")
    val atpName = text("atp_name")
    val mvzName = text("mvz_name")
    val mvzId = text("mvz_id")
}
