package com.x5.logistics.data.dictionary.org

import org.jetbrains.exposed.dao.LongEntity
import org.jetbrains.exposed.dao.LongEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.timestamp

object MrTable : LongIdTable("macro_region") {
    val name = text("name")
    val createdAt = timestamp("created_at")
    val createdBy = text("created_by")
    val updatedAt = timestamp("updated_at")
    val updatedBy = text("updated_by")
    val deleted = bool("deleted").default(false)
}

class MrEntity(id: EntityID<Long>) : LongEntity(id) {
    companion object : LongEntityClass<MrEntity>(MrTable)

    var name by MrTable.name
    var createdAt by MrTable.createdAt
    var createdBy by MrTable.createdBy
    var updatedAt by MrTable.updatedAt
    var updatedBy by MrTable.updatedBy
    var deleted by MrTable.deleted
}