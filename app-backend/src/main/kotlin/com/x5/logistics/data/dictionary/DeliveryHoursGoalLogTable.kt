package com.x5.logistics.data.dictionary

import com.x5.logistics.repository.dateLiteral
import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lead
import org.jetbrains.exposed.sql.WindowFunctionDefinition
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.javatime.date
import java.time.LocalDate

object DeliveryHoursGoalLogTable : LongIdTable("delivery_hours_goal_log") {
    val retailNetwork = text("retail_network")
    val deliveryHoursGoal = double("delivery_hours_goal")
    val createDate = date("create_date")
    val startDate = date("start_date")
}

object DeliveryHoursGoalLogView {
    val retailNetwork = DeliveryHoursGoalLogTable.retailNetwork
    val deliveryHoursGoal = DeliveryHoursGoalLogTable.deliveryHoursGoal
    val startDate = DeliveryHoursGoalLogTable.startDate

    @Suppress("UNCHECKED_CAST")
    val endDate = (DeliveryHoursGoalLogTable.startDate.lead(
        defaultValue = dateLiteral(LocalDate.of(9999, 12, 31))
    ).over()
        .partitionBy(DeliveryHoursGoalLogTable.retailNetwork) as WindowFunctionDefinition<LocalDate>).alias("end_date")

    fun createSubquery(selectedRetailNetwork: String? = null): QueryAlias =
        DeliveryHoursGoalLogTable.select(
            retailNetwork,
            deliveryHoursGoal,
            startDate,
            endDate
        ).apply {
            if (selectedRetailNetwork != null) {
                where { retailNetwork eq selectedRetailNetwork }
            }


        }.alias("dlv_goal_${selectedRetailNetwork}")
}
