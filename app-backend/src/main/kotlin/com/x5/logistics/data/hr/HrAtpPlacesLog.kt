package com.x5.logistics.data.hr

import org.jetbrains.exposed.dao.id.IntIdTable
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.datetime

object HrAtpPlacesLog : IntIdTable("hr_atp_places_log") {
    val atpId = integer("atp_id").nullable()
    val hrpId = integer("hrp_id").nullable()
    val startDate = date("start_date").nullable()
    val createdAt = datetime("created_at").nullable()
    val createdBy = text("created_by").nullable()
    val updatedAt = datetime("updated_at").nullable()
    val updatedBy = text("updated_by").nullable()
    val deleted = bool("deleted")
}
