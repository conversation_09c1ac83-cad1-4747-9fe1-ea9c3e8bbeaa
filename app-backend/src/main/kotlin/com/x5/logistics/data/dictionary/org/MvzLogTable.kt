package com.x5.logistics.data.dictionary.org

import com.x5.logistics.data.dictionary.org.AtpLogTable.atpLogSubqueryAlias
import com.x5.logistics.repository.minusDays
import org.jetbrains.exposed.dao.LongEntity
import org.jetbrains.exposed.dao.LongEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Lead
import org.jetbrains.exposed.sql.Max
import org.jetbrains.exposed.sql.Min
import org.jetbrains.exposed.sql.NotExists
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.javatime.JavaLocalDateColumnType
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.dateLiteral
import org.jetbrains.exposed.sql.javatime.timestamp
import org.jetbrains.exposed.sql.min
import org.jetbrains.exposed.sql.or
import java.time.LocalDate

object MvzLogTable : LongIdTable("mvz_log") {
    val uid = text("uid").nullable()
    val atpId = long("atp_id")
    val startDate = date("start_date")
    val createdAt = timestamp("created_at")
    val createdBy = text("created_by")
    val updatedAt = timestamp("updated_at")
    val updatedBy = text("updated_by")
    val deleted = bool("deleted").default(false)

    val endDate = Lead(startDate.minusDays(1), defaultValue = dateLiteral(LocalDate.of(2099, 12, 31)))
        .over().partitionBy(uid).orderBy(startDate).alias("end_date")
    val minStartDate = Min(startDate, JavaLocalDateColumnType()).over().partitionBy(uid).alias("min_start_date")

    private val currentDate = com.x5.logistics.repository.dateLiteral(LocalDate.now())
    private val child = this.alias("child")
    private val maxValidDate = Max(
        Case().When(startDate lessEq currentDate, startDate)
            .Else(dateLiteral(LocalDate.of(1800, 1, 1))),
        JavaLocalDateColumnType()
    ).over().partitionBy(uid)
    private val minDate = startDate.min().over().partitionBy(uid)
    private val noValidDatesExist = NotExists(
        child
            .select(intLiteral(1))
            .where { child[deleted] eq false and
                (child[uid] eq uid) and
                        (child[startDate] lessEq currentDate)
            }
    )

    val isCurrent = ((uid.isNull()) or
            (startDate eq maxValidDate) or
            (noValidDatesExist and (startDate eq minDate)))
                .alias("is_current")

    val mvzUid = MvzCodesTable.id.alias("mvz_uid")
    val mvzName = MvzCodesTable.name.alias("mvz_name")
    val mvzStartDate = MvzCodesTable.startDate.alias("mvz_start_date")
    val atpTableAtpId = AtpTable.id.alias("atp_log_atp_id")
    val atpName = AtpTable.name.alias("atp_name")
    val atpType = AtpTable.type.alias("atp_type")
    val mrName = AtpLogTable.mrName.alias("mr_name")
    val retailNetwork = AtpTable.retailNetwork.alias("retail_network")
    val ut = MvzCodesTable.ut.alias("ut")
    val type = MvzCodesTable.type.alias("type")

    val minMvzLogAtpDate = Min(MvzLogTable.startDate, JavaLocalDateColumnType()).alias("min_mvz_log_atp_date")
    val mvzLogAtpMinDateSubquery = MvzLogTable.select(MvzLogTable.atpId, minMvzLogAtpDate)
        .where { MvzLogTable.deleted eq false }
        .groupBy(MvzLogTable.atpId).alias("mvz_log_min_atp_date_subquery")
    val atpLifeStart = mvzLogAtpMinDateSubquery[minMvzLogAtpDate].alias("atp_life_start")

    val mvzLogSubquery = with(MvzLogTable) {
        join(MvzCodesTable, JoinType.RIGHT, uid, MvzCodesTable.id) { deleted eq false }
            .join(AtpTable, JoinType.LEFT, atpId, AtpTable.id) { AtpTable.deleted eq false }
            .join(atpLogSubqueryAlias, JoinType.LEFT) {
                (atpLogSubqueryAlias[AtpLogTable.atpId] eq atpId) and (atpLogSubqueryAlias[AtpLogTable.deleted] eq false) and
                        (atpLogSubqueryAlias[AtpLogTable.startDate] lessEq dateLiteral(LocalDate.now())) and
                        (atpLogSubqueryAlias[AtpLogTable.endDate] greaterEq dateLiteral(LocalDate.now()))
            }
            .select(
                MvzLogTable.columns
                        + mvzUid
                        + mvzStartDate
                        + endDate
                        + minStartDate
                        + isCurrent
                        + mvzName
                        + ut
                        + type
                        + atpTableAtpId
                        + atpName
                        + mrName.aliasOnlyExpression()
                        + retailNetwork.aliasOnlyExpression()
            )
    }

    val mvzLogSubqueryAlias = mvzLogSubquery.alias("mvz_log_subquery")

}

class MvzLogEntity(id: EntityID<Long>) : LongEntity(id) {
    companion object : LongEntityClass<MvzLogEntity>(MvzLogTable)

    var uid by MvzLogTable.uid
    var atpId by MvzLogTable.atpId
    var startDate by MvzLogTable.startDate
    var createdAt by MvzLogTable.createdAt
    var createdBy by MvzLogTable.createdBy
    var updatedAt by MvzLogTable.updatedAt
    var updatedBy by MvzLogTable.updatedBy
    var deleted by MvzLogTable.deleted
}