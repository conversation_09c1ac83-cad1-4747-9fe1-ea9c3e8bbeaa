package com.x5.logistics.data.dictionary.hr.drivers

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import jakarta.persistence.Table
import java.io.Serializable

@Entity
@Table(name = "drivers_per_vehicle_group_view")
@IdClass(HrDriversPerVehicleDictionaryGroupViewId::class)
data class HrDriversPerVehicleDictionaryGroupViewDao(
    @Id
    val atpId: Long,

    @Column(name = "atp_name", nullable = false)
    val atpName: String,

    @Column(name = "mr_name", nullable = true)
    val mrName: String?,

    @Column(name = "place_name", nullable = true)
    val placeName: String?,

    @Column(name = "retail_network", nullable = true)
    val retailNetwork: String?,

    @Column(name = "atp_type", nullable = true)
    val atpType: String?,

    @Id
    @Column(name = "year", nullable = true)
    val year: Int?,
)

class HrDriversPerVehicleDictionaryGroupViewId(
    var atpId: Long = 0,
    var year: Int = 0,
): Serializable
