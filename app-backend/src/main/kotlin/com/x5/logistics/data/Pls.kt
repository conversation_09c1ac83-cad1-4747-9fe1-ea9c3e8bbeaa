package com.x5.logistics.data

import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.datetime
import org.jetbrains.exposed.sql.javatime.time
import kotlin.reflect.full.declaredMemberProperties

object Pls : Table() {
    val userStat = text("user_stat")
    val qmnum = text("qmnum")
    val startTimestamp = datetime("start_timestamp")
    val endTimestamp = datetime("end_timestamp")
    val startDateFact = date("start_date_fact").alias("pl_start_date")
    val startTimeFact = time("start_time_fact").alias("pl_start_time")
    val endDateFact = date("end_date_fact")
    val endTimeFact = time("end_time_fact").alias("pl_end_time_fact")
    val endDatePlan = date("end_date_plan").alias("pl_end_date_plan")
    val endTimePlan = time("end_time_plan").alias("pl_end_time_plan")
    val hours = double("hours")
    val plMvzId = text("pl_mvz_id")
    val plMvzName = text("pl_mvz_name")
    val mvzId = text("mvz_id")
    val mvzName = text("mvz_name")
    val driverNumber = long("driver_number")
    val msaus = bool("msaus")
    val marka = text("marka")
    val model = text("model")
    val year = integer("year")
    val loadWgt = double("load_wgt")
    val vehicleLicense = text("vehicle_license")
    val trailerLicenseNum = text("trailer_license_num")
    val equnr = long("equnr")
    val commissioningYear = integer("commissioning_year")
    val fleetNum = text("fleet_num")
    val gbo = bool("gbo")
    val fuelPri = float("fuel_pri")
    val fuelSec = float("fuel_sec")
    val mileage = double("mileage")
    val motoHours = double("moto_hours")
    val tsGroup = text("ts_group")
    val tsType = text("ts_type")
    val repairFlag = bool("repair_flag")
    val idleFlag = bool("idle_flag")
    val noTripsFlag = bool("no_trips_flag")
    val wrongCostPointFlag = bool("wrong_cost_point_flag")
    val badMileageFlag = bool("bad_mileage_flag")
    val plReserve = bool("pl_reserve")
    val plType = text("pl_type")
    val plCommerce = bool("pl_commerce")
    val plTod = bool("pl_tod")
    val plTransit = bool("pl_transit")
    val longTimeFlag = bool("long_time_flag")

    fun getAllFields(): List<Expression<*>> {
        return this::class.declaredMemberProperties.map {
            it.getter.call(Pls)!! as Expression<*>
        }
    }
}