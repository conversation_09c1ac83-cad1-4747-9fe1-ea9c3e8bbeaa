package com.x5.logistics.data.dictionary.hrplaces

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import org.hibernate.annotations.Fetch
import org.hibernate.annotations.FetchMode
import java.io.Serializable
import java.time.LocalDate

@Entity
@Table(name = "hr_place_tplace_log_view")
@IdClass(HrPlaceTplaceLogViewEntityId::class)
data class HrPlaceTplaceLogViewEntityDao(
    @Column(name = "id", nullable = false)
    val id: Long,

    @Column(name = "origin", nullable = false)
    val origin: Int,

    @Column(name = "min_start_date", nullable = false)
    val minStartDate: LocalDate,

    @Id
    @ManyToOne(optional = false)
    @JoinColumn(name = "p_id")
    val place: HrPlaceDao,

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @Fetch(FetchMode.JOIN)
    @JoinColumn(name = "tp_id", referencedColumnName = "id", nullable = true)
    val totalPlace: HrTotalPlaceDao?,

    @Id
    @Column(name = "start_date", nullable = false)
    val startDate: LocalDate,

    @Column(name = "end_date")
    val endDate: LocalDate?,

    @Column(name = "created_at")
    val createdAt: LocalDate?,

    @Column(name = "created_by", nullable = false)
    val createdBy: String,

    @Column(name = "updated_at")
    val updatedAt: LocalDate?,

    @Column(name = "updated_by")
    val updatedBy: String?
)

data class HrPlaceTplaceLogViewEntityId(
    var place: Long = 0L,
    val startDate: LocalDate = LocalDate.MIN
): Serializable
