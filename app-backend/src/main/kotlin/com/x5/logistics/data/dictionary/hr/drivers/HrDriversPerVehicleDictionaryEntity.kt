package com.x5.logistics.data.dictionary.hr.drivers

import com.x5.logistics.data.AtpEntity
import com.x5.logistics.data.Month
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import java.io.Serializable
import java.math.BigDecimal
import java.time.Instant

@Entity
@Table(name = "hr_drivers_per_vehicle")
@IdClass(HrDriversPerVehicleDictionaryEntityId::class)
data class HrDriversPerVehicleDictionaryEntity(
    @Id
    @ManyToOne
    @JoinColumn(name = "atp_id", nullable = false)
    val atp: AtpEntity,

    @Id
    @Column(name = "year", nullable = false)
    val year: Int,

    @Id
    @Column(name = "month", nullable = false)
    @Enumerated(EnumType.STRING)
    val month: Month,

    @Column(name = "value", nullable = false)
    val value: BigDecimal,

    @Column(name = "created_at", nullable = false)
    val createdAt: Instant,

    @Column(name = "created_by", nullable = false)
    val createdBy: String,

    @Column(name = "updated_at", nullable = false)
    val updatedAt: Instant,

    @Column(name = "updated_by", nullable = false)
    val updatedBy: String
)

class HrDriversPerVehicleDictionaryEntityId(
    var atp: Long = 0,
    var year: Int = 0,
    var month: Month = Month.JANUARY,
): Serializable
