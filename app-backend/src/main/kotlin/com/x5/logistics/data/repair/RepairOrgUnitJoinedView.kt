package com.x5.logistics.data.repair

import com.x5.logistics.rest.dto.GeoFilter
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object RepairOrgUnitJoinedView : Table("repair_org_unit_timeline_joined_view") {
    val orderId = long("order_id").nullable()
    val repairExpensesFull = double("repair_expenses_full").nullable()
    val repairStartDate = date("repair_start_date").nullable()
    val mvzId = text("mvz_id").nullable()
    val mvzType = text("mvz_type").nullable()
    val mrId = long("mr_id").nullable()
    val atpId = long("atp_id").nullable()
    val atpType = text("atp_type").nullable()
    val retailNetwork = text("retail_network").nullable()

    fun getGeoFilter(geofilter: GeoFilter?): List<Op<Boolean>> {
        val filters = mutableListOf<Op<Boolean>>()
        if (geofilter != null) {
            geofilter.mr?.let {
                if (it.isNotEmpty()) {
                    filters.add(mrId inList it)
                }
            }
            geofilter.atp?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpId inList it)
                }
            }
            geofilter.mvz?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzId inList it)
                }
            }
            geofilter.retailNetwork?.let {
                if (it.isNotEmpty()) {
                    filters.add(retailNetwork inList it)
                }
            }
            geofilter.atpType?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpType inList it)
                }
            }
            geofilter.mvzType?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzType inList it.filterNotNull())
                }
            }
        }
        return filters.toList()
    }
}
