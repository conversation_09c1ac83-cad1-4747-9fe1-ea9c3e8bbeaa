package com.x5.logistics.data.email

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import org.hibernate.annotations.ColumnDefault
import java.time.Instant
import java.time.LocalDate

@Entity
@Table(name = "user_email_subscription")
open class UserEmailSubscription {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ColumnDefault("nextval('user_email_subscription_id_seq'")
    @Column(name = "id", nullable = false)
    open var id: Long? = null

    @Size(max = 255)
    @NotNull
    @Column(name = "email", nullable = false)
    open var email: String? = null

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "subject_id")
    open var subject: UserEmailSubscriptionSubject? = null

    @NotNull
    @Column(name = "subscription_date", nullable = false)
    open var subscriptionDate: LocalDate? = null

    @Column(name = "unsubscription_date")
    open var unsubscriptionDate: LocalDate? = null

    @Size(max = 255)
    @NotNull
    @Column(name = "created_by", nullable = false)
    open var createdBy: String? = null

    @NotNull
    @Column(name = "created_at", nullable = false)
    open var createdAt: Instant? = null
}