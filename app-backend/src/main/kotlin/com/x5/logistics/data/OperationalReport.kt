package com.x5.logistics.data

import com.x5.logistics.repository.CountAll
import com.x5.logistics.repository.CountWithFilter
import com.x5.logistics.repository.CountWithFilterLong
import com.x5.logistics.repository.GranularDate
import com.x5.logistics.repository.Granularity
import com.x5.logistics.repository.aliasOnlyExpression
import com.x5.logistics.repository.castToDate
import com.x5.logistics.repository.castToDouble
import com.x5.logistics.repository.countOrNull
import com.x5.logistics.repository.daysBetween
import com.x5.logistics.repository.evaluateOrNull
import com.x5.logistics.repository.interval
import com.x5.logistics.repository.safeDiv
import com.x5.logistics.repository.times
import com.x5.logistics.repository.toHours
import org.jetbrains.exposed.sql.BooleanColumnType
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.Count
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.LiteralOp
import org.jetbrains.exposed.sql.LongColumnType
import org.jetbrains.exposed.sql.Max
import org.jetbrains.exposed.sql.Min
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.RowNumber
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.booleanLiteral
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.javatime.JavaDurationColumnType
import org.jetbrains.exposed.sql.javatime.JavaLocalDateColumnType
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.timestamp
import org.jetbrains.exposed.sql.longLiteral
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.sum
import java.time.Duration

object OperationalReport : Table("operational_report") {
    val mvzId = text("mvz_id")
    val vehicleDate = date("vehicle_date").nullable()
    val vehicleLicense = varchar("vehicle_license", 50).nullable()
    val numTripTms = long("num_trip_tms").nullable()
    val vehicleGroup = varchar("vehicle_group", 100).nullable()
    val vehicleType = varchar("vehicle_type", 100).nullable()
    val vehicleBrand = varchar("vehicle_brand", 100).nullable()
    val vehicleModel = varchar("vehicle_model", 100).nullable()
    val vehicleTonnage = float("vehicle_tonnage").nullable()
    val vehicleCreateYear = integer("vehicle_create_year").nullable()
    val equnr = long("equnr").nullable()
    val ourTripText = text("our_trip_text").nullable()

    //    val ourTripFlag = bool("our_trip_flag").nullable()
    val ourTripFlag = Case()
        .When(ourTripText.aliasOnlyExpression() eq stringLiteral("СТ"), booleanLiteral(true))
        .When(ourTripText.aliasOnlyExpression() eq stringLiteral("НТ"), booleanLiteral(false))
        .Else(Op.nullOp()).alias("our_trip_flag")
    val charaterDelvr = text("charater_delvr").nullable()
    val rnAndCharaterDelivery = text("rn_and_chracter_deliver").nullable()
    val rcName = varchar("rc_name", 255).nullable()
    val idRcSap = text("id_rc_sap").nullable()
    val rcNameSapLogistics = text("rc_name_sap_logistics").nullable()
    val idRcSapLogistics = text("id_rc_sap_logistics").nullable()
    val rcRetailNetwork = text("rc_retail_network").nullable()
    val dtRegRc = timestamp("dt_reg_rc").nullable()
    val carIn = bool("car_in").nullable()
    val carInGps = bool("car_in_gps").nullable()
    val isDelvWndowCount = long("is_delv_wndow_count").nullable()
    val isDelvIntimeCount = long("is_delv_intime_count").nullable()
    val isDelvWindowWithPlanWndowCount = long("is_delv_window_with_plan_wndow_count").nullable()
    val pointsCount = long("points_count").nullable()
    val statusTFinal = bool("status_t_final").nullable()
    val statusTCalculate = bool("status_t_calculate").nullable()
    val kip = interval("kip").nullable()
    val kipNoReserve = interval("kip_no_reserve").nullable()
    val ktg = bool("ktg").nullable()
    val rg = bool("rg").nullable()
    val comeOff = bool("come_off").nullable()

    fun granularDate(granularity: Granularity) = GranularDate(granularity, vehicleDate)
    val week = granularDate(Granularity.WEEK).alias("week_date")
    val reportingWeek = granularDate(Granularity.REPORTING_WEEK).alias("reporting_week_date")
    val month = granularDate(Granularity.MONTH).alias("month_date")
    val quarter = granularDate(Granularity.QUARTER).alias("quarter_date")
    val year = granularDate(Granularity.YEAR).alias("year_date")

    private val minDate = Min(vehicleDate.aliasOnlyExpression(), JavaLocalDateColumnType())
    private val maxDate = Max(vehicleDate.aliasOnlyExpression(), JavaLocalDateColumnType())
    val daysDiff = Expression.build {
        daysBetween(minDate.castToDate(), maxDate.castToDate()) + 1
    }

    val rowNumber = RowNumber().over()
        .partitionBy(vehicleDate, vehicleLicense)
        .alias("row_number")

    val vehicleCount =
        (Count(vehicleLicense.aliasOnlyExpression(), distinct = true).castToDouble()
            .safeDiv(daysDiff.castToDouble())).alias("vehicle_count")

    private val uniqueCondition = rowNumber.aliasOnlyExpression() eq longLiteral(1)
    private val ktgUnique = Case()
        .When(uniqueCondition, ktg.aliasOnlyExpression())
        .Else(LiteralOp(BooleanColumnType(), false))
    private val ktgTrueCount = countOrNull(ktgUnique eq true)
    private val totalKtgCount = Sum(
        Case()
            .When(uniqueCondition and ktg.aliasOnlyExpression().isNotNull(), longLiteral(1))
            .Else(longLiteral(0L)), LongColumnType()
    ).castToDouble()
    val ktgShare =
        (ktgTrueCount.castToDouble().safeDiv(totalKtgCount) * doubleLiteral(100.0)).alias("ktg_share")

    private val rgUnique = Case()
        .When(uniqueCondition, rg.aliasOnlyExpression())
        .Else(LiteralOp(BooleanColumnType(), false))
    private val rgTrueCount = countOrNull(rgUnique eq true)
    private val totalRgCount = Sum(
        Case()
            .When(uniqueCondition and rg.aliasOnlyExpression().isNotNull(), longLiteral(1))
            .Else(longLiteral(0L)), LongColumnType()
    ).castToDouble()
    val rgShare =
        (rgTrueCount.castToDouble().safeDiv(totalRgCount.castToDouble()) * doubleLiteral(100.0)).alias("rg_share")

    private val kipUnique = Case()
        .When(rowNumber.aliasOnlyExpression() eq longLiteral(1), kip.aliasOnlyExpression())
        .Else(LiteralOp(JavaDurationColumnType(), Duration.ZERO))
    val kipHours = kipUnique.sum().toHours().alias("kip_hours")

    private val totalHours = Sum(
        Case()
            .When(uniqueCondition and kip.aliasOnlyExpression().isNotNull(), doubleLiteral(1.0))
            .Else(LiteralOp(DoubleColumnType(), 0.0)), DoubleColumnType()
    ) * doubleLiteral(24.0)
    val kipShare = (kipHours.delegate.castToDouble().safeDiv(totalHours) * doubleLiteral(100.0)).alias("kip_share")

    private val kipNoReserveUnique = Case()
        .When(uniqueCondition, kipNoReserve.aliasOnlyExpression())
        .Else(LiteralOp(JavaDurationColumnType(), Duration.ZERO))
    val kipNoReserveHours = kipNoReserveUnique.sum().toHours().alias("kip_no_reserve_hours")
    val kipNoReserveShare = (kipNoReserveHours.delegate.castToDouble()
        .safeDiv(totalHours) * doubleLiteral(100.0)).alias("kip_no_reserve_share")

    val tripsCount = Count(numTripTms.aliasOnlyExpression(), distinct = true)
        .alias("trips_count")

    val pointsCountSum = Sum(pointsCount.aliasOnlyExpression(), LongColumnType())
        .alias("points_count_sum")

    val tempTrueCount = countOrNull(
        (statusTFinal.aliasOnlyExpression() eq booleanLiteral(true)) and (statusTCalculate.aliasOnlyExpression() eq booleanLiteral(
            true
        ))
    )
    private val totalCalcCount = countOrNull(statusTCalculate.aliasOnlyExpression() eq booleanLiteral(true))

    private val delvWndowSum = Sum(isDelvWndowCount.aliasOnlyExpression(), LongColumnType()).castToDouble()
    private val pointsSum = Sum(pointsCount.aliasOnlyExpression(), LongColumnType()).castToDouble()

    private val delvIntimeSum = Sum(isDelvIntimeCount.aliasOnlyExpression(), LongColumnType()).castToDouble()
    val deliveryInPlan = (delvIntimeSum.safeDiv(pointsSum) * doubleLiteral(100.0)).alias("delivery_in_plan")

    val delvWindowPlanSum = Sum(isDelvWindowWithPlanWndowCount.aliasOnlyExpression(), LongColumnType()).castToDouble()

    private val carInCount = evaluateOrNull(
        carIn.aliasOnlyExpression(),
        CountWithFilter(
            expr = numTripTms.aliasOnlyExpression(),
            filterCondition = carIn.aliasOnlyExpression() eq booleanLiteral(true),
            distinct = true,
            columnType = LongColumnType()
        ).castToDouble()

    )

    private val totalCarInCount = Count(numTripTms.aliasOnlyExpression(), distinct = true)
    private val totalOurCount = CountWithFilter(
        expr = numTripTms.aliasOnlyExpression(),
        filterCondition = ourTripFlag.aliasOnlyExpression() eq booleanLiteral(true),
        distinct = true,
        columnType = LongColumnType()
    ).castToDouble()
    private val totalHiredCount = CountWithFilter(
        expr = numTripTms.aliasOnlyExpression(),
        filterCondition = ourTripFlag.aliasOnlyExpression() eq booleanLiteral(false),
        distinct = true,
        columnType = LongColumnType()
    ).castToDouble()

    val carInTime =
        (carInCount.castToDouble()
            .safeDiv(totalCarInCount.castToDouble()) * doubleLiteral(100.0)).alias("car_in_time")

    private val carInOwnCount = evaluateOrNull(
        carIn.aliasOnlyExpression(),
        CountWithFilter(
            expr = numTripTms.aliasOnlyExpression(),
            filterCondition = carIn.aliasOnlyExpression() eq booleanLiteral(true) and (ourTripFlag.aliasOnlyExpression() eq booleanLiteral(
                true
            )),
            distinct = true,
            columnType = LongColumnType()
        ).castToDouble()
    )
    val carInTimeOwn =
        (carInOwnCount.castToDouble()
            .safeDiv(totalOurCount.castToDouble()) * doubleLiteral(100.0)).alias("car_in_time_own")

    private val carInGPSOwnCount = evaluateOrNull(
        carInGps.aliasOnlyExpression(),
        CountWithFilter(
            expr = numTripTms.aliasOnlyExpression(),
            filterCondition = carInGps.aliasOnlyExpression() eq booleanLiteral(true) and (ourTripFlag.aliasOnlyExpression() eq booleanLiteral(
                true
            )),
            distinct = true,
            columnType = LongColumnType()
        ).castToDouble()
    )

    val carInGpsOwn =
        (carInGPSOwnCount.castToDouble()
            .safeDiv(totalOurCount.castToDouble()) * doubleLiteral(100.0)).alias("car_in_gps_own")

    private val carInHiredCount = evaluateOrNull(
        carIn.aliasOnlyExpression(),
        CountWithFilter(
            expr = numTripTms.aliasOnlyExpression(),
            filterCondition = carIn.aliasOnlyExpression() eq booleanLiteral(true) and (ourTripFlag.aliasOnlyExpression() eq booleanLiteral(
                false
            )),
            distinct = true,
            columnType = LongColumnType()
        ).castToDouble()
    )
    val carInTimeHired =
        (carInHiredCount.castToDouble()
            .safeDiv(totalHiredCount.castToDouble()) * doubleLiteral(100.0)).alias("car_in_time_hired")

    private val carInGPSHiredCount = evaluateOrNull(
        carInGps.aliasOnlyExpression(),
        CountWithFilter(
            expr = numTripTms.aliasOnlyExpression(),
            filterCondition = carInGps.aliasOnlyExpression() eq booleanLiteral(true) and (ourTripFlag.aliasOnlyExpression() eq booleanLiteral(
                false
            )),
            distinct = true,
            columnType = LongColumnType()
        ).castToDouble()
    )
    val carInGpsHired = (carInGPSHiredCount.castToDouble().safeDiv(totalHiredCount.castToDouble()) * doubleLiteral(
        100.0
    )).alias("car_in_gps_hired")

    private val comeOffDays = CountWithFilterLong(
        expr = vehicleDate.aliasOnlyExpression(),
        filterCondition = comeOff.aliasOnlyExpression().isNotNull(),
        distinct = true
    )

    val comeOffCount = CountWithFilterLong(
        expr = longLiteral(1),
        filterCondition = (comeOff.aliasOnlyExpression() eq booleanLiteral(true)) and (rowNumber.aliasOnlyExpression() eq longLiteral(
            1
        )),
        distinct = false
    ).castToDouble().safeDiv(comeOffDays.castToDouble()).alias("come_off_count")

    private val totalVehicles = CountWithFilterLong(
        expr = longLiteral(1),
        filterCondition = (comeOff.aliasOnlyExpression()
            .isNotNull()) and (rowNumber.aliasOnlyExpression() eq longLiteral(1)),
        distinct = false
    )
    val comeOffPercent = (CountWithFilterLong(
        expr = longLiteral(1),
        filterCondition = (comeOff.aliasOnlyExpression() eq booleanLiteral(true)) and (rowNumber.aliasOnlyExpression() eq longLiteral(
            1
        )),
        distinct = false
    ).castToDouble()
        .safeDiv(totalVehicles.castToDouble()) * doubleLiteral(100.0)).alias("come_off_percent")

    val temperature = (tempTrueCount.castToDouble()
        .safeDiv(totalCalcCount.castToDouble()) * doubleLiteral(100.0)).alias("temperature")

    val count = CountAll().alias("count")
}
