package com.x5.logistics.data.repair

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object PlsByDay : Table("pls_by_day") {
    val mvzId = text("mvz_id").nullable()
    val vehicleDate = date("vehicle_date").nullable()
    val tsGroup = text("ts_group").nullable()
    val plTonnage = double("pl_tonnage").nullable()
    val mileage = double("mileage").nullable()
}