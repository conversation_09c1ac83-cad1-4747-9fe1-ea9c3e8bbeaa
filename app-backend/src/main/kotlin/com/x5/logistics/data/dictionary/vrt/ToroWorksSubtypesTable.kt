package com.x5.logistics.data.dictionary.vrt

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.timestamp

object ToroWorksSubtypesTable: Table("toro_works_subtypes") {
    val id = integer("id")
    val name = text("name")
    val color = text("color")
    val createdBy = text("created_by")
    val createdAt = timestamp("created_at")
    val updatedBy = text("updated_by")
    val updatedAt = timestamp("updated_at")
    val deleted = bool("deleted")
}