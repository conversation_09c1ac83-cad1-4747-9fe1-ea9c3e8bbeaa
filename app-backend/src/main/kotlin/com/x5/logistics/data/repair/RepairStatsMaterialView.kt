package com.x5.logistics.data.repair

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object RepairStatsMaterialView : Table("repair_stats") {
    val mvz = text("mvz_id").nullable()
    val vehicleDate = date("vehicle_date").nullable()
    val repairExpensesFull = double("repair_expenses_full").nullable()
    val mileage = double("mileage").nullable()
    val tsLicenseNum = text("ts_license_num").nullable()
    val tsGroup = text("ts_group").nullable()
    val tsType = text("ts_type").nullable()
    val tsMarka = text("ts_marka").nullable()
    val tsModel = text("ts_model").nullable()
    val tsLoadWgt = double("ts_load_wgt").nullable()
    val vehicleYear = integer("vehicle_year").nullable()
    val tsCreateDate = date("ts_create_date").nullable()
    val vin = text("vin").nullable()
    val equnr = long("equnr").nullable()
    val tsGbo = text("ts_gbo_text").nullable()
    val tdNoCompart = integer("td_no_compart").nullable()
    val trailerLicenseNum = text("trailer_license_num").nullable()
}