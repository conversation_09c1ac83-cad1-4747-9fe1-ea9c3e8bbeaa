package com.x5.logistics.data.som

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.time
import org.jetbrains.exposed.sql.javatime.timestamp

object SomTripsAndPointsFullTable : Table("som_trips_and_points_full") {
    val mvzId = text("mvz_id").nullable()
    val gosNumTs = text("gos_num_ts").nullable()
    val fleetNum = text("fleet_num").nullable()
    val equnr  = long("equnr").nullable()
    val gosNumTrailer = text("gos_num_trailer").nullable()
    val vehicleGroup = text("vehicle_group").nullable()
    val vehicleType = text("vehicle_type").nullable()
    val vehicleBrand  = text("vehicle_brand").nullable()
    val vehicleModel = text("vehicle_model").nullable()
    val vehicleTonnage = float("vehicle_tonnage").nullable()
    val vehicleCreateYear = integer("vehicle_create_year").nullable()
    val numTripTms = long("num_trip_tms").nullable()
    val numTripNq = long("num_trip_nq").nullable()
    val idRc = long("id_rc").nullable()
    val rcName = text("rc_name").nullable()
    val idRcSap = text("id_rc_sap").nullable()
    val rcNameSap = text("rc_name_sap").nullable()
    val rcMvzId = text("rc_mvz_id").nullable()
    val rcFormat = text("rc_format").nullable()
    val rcRetailNetwork = text("rc_retail_network").nullable()
    val rcNqCode = integer("rc_nq_code").nullable()
    val dtPlanRc = timestamp("dt_plan_rc").nullable()
    val dtPlanRcDate = date("dt_plan_rc_date").nullable()
    val timePlanRc = time("time_plan_rc").nullable()
    val dtRegRc = timestamp("dt_reg_rc").nullable()
    val dtRegRcDate = date("dt_reg_rc_date").nullable()
    val timeRegRc = time("time_reg_rc").nullable()
    val ourTripFlag = bool("our_trip_flag").nullable()
    val ourTripText = text("our_trip_text").nullable()
    val codeSap = text("code_sap").nullable()
    val codeNq = long("code_nq").nullable()
    val nameTt = text("name_tt").nullable()
    val codeSapSending = text("code_sap_sending").nullable()
    val nameTtSending = text("name_tt_sending").nullable()
    val orderPointPlan  = integer("order_point_plan").nullable()
    val orderPointFact = integer("order_point_fact").nullable()
    val dtPlanArrive = timestamp("dt_plan_arrive").nullable()
    val dtPlanArriveDate = date("dt_plan_arrive_date").nullable()
    val timePlanArrive = time("time_plan_arrive").nullable()
    val dtFactArrive = timestamp("dt_fact_arrive").nullable()
    val dtFactArriveDate = date("dt_fact_arrive_date").nullable()
    val timeFactArrive = time("time_fact_arrive").nullable()
    val dtPlanDepart = timestamp("dt_plan_depart").nullable()
    val dtPlanDepartDate = date("dt_plan_depart_date").nullable()
    val timePlanDepart = time("time_plan_depart").nullable()
    val dtFactDepart = timestamp("dt_fact_depart").nullable()
    val dtFactDepartDate = date("dt_fact_depart_date").nullable()
    val timeFactDepart = time("time_fact_depart").nullable()
    val dtScanFirstPallet = timestamp("dt_scan_first_pallet").nullable()
    val dtScanFirstPalletDate = date("dt_scan_first_pallet_date").nullable()
    val timeScanFirstPallet = time("time_scan_first_pallet").nullable()
    val charaterDelvr  = text("charater_delvr").nullable()
    val statusT = text("status_t").nullable()
    val statusTDescription = text("status_t_description").nullable()
    val statusTFinal = bool("status_t_final").nullable()
    val statusTCalculate = bool("status_t_calculate").nullable()
    val statusDelvrWndowGps = text("status_delvr_wndow_gps").nullable()
    val statusDelvrWndowPallet = text("status_delvr_wndow_pallet").nullable()
    val statusDelvrIntimeGps = text("status_delvr_intime_gps").nullable()
    val statusDelvrIntimePallet = text("status_delvr_intime_pallet").nullable()
    val isTruePlanWndow = bool("is_true_plan_wndow").nullable()
    val isTruePlanWndowText = text("is_true_plan_wndow_text").nullable()
    val tsAtpTk = text("ts_atp_tk").nullable()
    val carIn = bool("car_in").nullable()
    val carInGps = bool("car_in_gps").nullable()
    val isDelvWndowCount = integer("is_delv_wndow_count").nullable()
    val isDelvIntimeCount = integer("is_delv_intime_count").nullable()
    val isDelvWindowWithPlanWndowCount = integer("is_delv_window_with_plan_wndow_count").nullable()
    val pointsCount = integer("points_count").nullable()
    val rcNameSapLogistics = text("rc_name_sap_logistics").nullable()
    val idRcSapLogistics = text("id_rc_sap_logistics").nullable()
    val rnAndCharaterDelivery = text("rn_and_chracter_deliver").nullable()
}