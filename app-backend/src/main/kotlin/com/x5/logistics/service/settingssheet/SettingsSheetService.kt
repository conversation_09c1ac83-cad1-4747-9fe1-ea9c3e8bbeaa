package com.x5.logistics.service.settingssheet

import com.x5.logistics.data.dictionary.org.AtpEntity
import com.x5.logistics.data.dictionary.org.AtpTable
import com.x5.logistics.data.dictionary.org.MrEntity
import com.x5.logistics.data.dictionary.org.MrTable
import com.x5.logistics.data.dictionary.org.MvzCodeEntity
import com.x5.logistics.data.dictionary.org.MvzCodesTable
import com.x5.logistics.data.dictionary.org.TerritoryEntity
import com.x5.logistics.data.dictionary.org.TerritoryTable
import com.x5.logistics.repository.castToLong
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.service.WbBuilder
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.dateFormatterWithDots
import com.x5.logistics.util.now
import com.x5.logistics.util.timeFormatter
import org.jetbrains.exposed.sql.transactions.transaction
import org.springframework.stereotype.Service

@Service
class SettingsSheetService {
    fun addSettingsSheet(wb: WbBuilder, req: ExportRequest) {
        val headerStyle = ExcelStyles.Header.style(wb)
        val strStyle = ExcelStyles.Text.style(wb)
        val sectionHeaderStyle = ExcelStyles.SectionHeader.style(wb)

        val baseFilters = if (req.isRepairPlace) req.filters?.filterNot { it.name == "Ремзона" } else req.filters
        val filters = baseFilters?.flatMap { filter ->
            when {
                filter.value.isEmpty() -> listOf(Filter(filter.name, filter.condition, emptyList()))
                else -> filter.value.map { value ->
                    Filter(filter.name, filter.condition, listOf(value))
                }
            }
        }
        val repairPlaceList =
            if (req.isRepairPlace) req.filters?.find { it.name == "Ремзона" }?.value ?: emptyList() else emptyList()
        val orgUnitData = if (!req.isRepairPlace) getOrgUnitData(req.geoFilter) else OrgUnitData(
            listOf(),
            listOf(),
            listOf(),
            listOf()
        )

        var colNum = 8
        val excludeCols = mutableListOf(7)

        wb.sheet("Настройки") {
            header {
                currStyle = sectionHeaderStyle
                head(value = "Настройки выгрузки отчета", cols = if (req.granularitySupported) 5 else 4)
            }

            header {
                currStyle = headerStyle
                head("Даты")
                if (req.granularitySupported) head("Гранулярность")
                head("Дата формирования выгрузки")
                head("Время формирования выгрузки (Мск)")
                head("Сотрудник")
            }
            row {
                currStyle = strStyle
                cell("${req.from.format(dateFormatterWithDots)} - ${req.to.format(dateFormatterWithDots)}")
                if (req.granularitySupported) cell(req.granularityValue ?: "Не выбрано")
                cell(now().format(dateFormatterWithDots))
                cell(now().format(timeFormatter))
                cell(req.userName)
            }

            row {}

            val filterSelected = filters?.isNotEmpty() == true
            val sortSelected = req.sort?.isNotEmpty() == true

            header {
                currStyle = sectionHeaderStyle
                if (req.isRepairPlace) {
                    head("Ремзоны")
                } else {
                    head(value = "Подразделения", cols = 7)
                }

                if (filterSelected) {
                    currStyle = headerStyle
                    head("")
                    currStyle = sectionHeaderStyle
                    head(value = "Фильтры: ${filters?.size}", cols = 3)
                }

                if (sortSelected) {
                    currStyle = headerStyle
                    head("")
                    currStyle = sectionHeaderStyle
                    head(value = "Сортировка: ${req.sort?.size}", cols = 2)
                }

            }

            header {
                currStyle = headerStyle
                if (req.isRepairPlace) {
                    head("Все ремзоны: ${repairPlaceList.size}")
                } else {
                    head("Территория: ${req.geoFilter?.territory?.size ?: 0}")
                    head("Макрорегион: ${req.geoFilter?.mr?.size ?: 0}")
                    head("АТП: ${req.geoFilter?.atp?.size ?: 0}")
                    head("МВЗ: ${req.geoFilter?.mvz?.size ?: 0}")
                    head("Торговая сеть АТП: ${req.geoFilter?.retailNetwork?.size ?: 0}")
                    head("Вид деятельности АТП: ${req.geoFilter?.atpType?.size ?: 0}")
                    head("Тип МВЗ: ${req.geoFilter?.mvzType?.size ?: 0}")
                }

                if (filterSelected) {
                    head("")
                    head("Название колонки")
                    head("Условие")
                    head("Значение")
                }

                if (sortSelected) {
                    head("")
                    head("Название колонки")
                    head("Условие")
                }

                head("")
                head("Отображаемые колонки: ${req.columns?.size}")
            }

            val rowsCount = listOf(
                repairPlaceList,
                orgUnitData.terName,
                orgUnitData.mrName,
                orgUnitData.atpName,
                orgUnitData.mvzName,
                req.geoFilter?.retailNetwork ?: emptyList(),
                req.geoFilter?.atpType ?: emptyList(),
                req.geoFilter?.mvzType ?: emptyList(),
                filters ?: emptyList(),
                req.sort ?: emptyList(),
                req.columns ?: emptyList(),
            ).maxOf { it.size }

            for (rowNumber in 0 until rowsCount) {
                row {
                    currStyle = strStyle
                    if (req.isRepairPlace) {
                        cell(repairPlaceList.getOrNull(rowNumber))
                    } else {
                        cell(orgUnitData.terName.getOrNull(rowNumber))
                        cell(orgUnitData.mrName.getOrNull(rowNumber))
                        cell(orgUnitData.atpName.getOrNull(rowNumber))
                        cell(orgUnitData.mvzName.getOrNull(rowNumber))
                        cell(req.geoFilter?.retailNetwork?.getOrNull(rowNumber))
                        cell(req.geoFilter?.atpType?.getOrNull(rowNumber))
                        cell(req.geoFilter?.mvzType?.getOrNull(rowNumber))
                    }
                    if (filterSelected) {
                        val filter = filters?.getOrNull(rowNumber)
                        cell("")
                        cell(filter?.name)
                        cell(filter.getCondition())
                        cell(filter.getValue())
                    }
                    if (sortSelected) {
                        val sort = req.sort?.getOrNull(rowNumber)
                        cell("")
                        cell(sort?.column)
                        cell(if (sort?.asc == true) "По возрастанию" else if (sort?.asc == false) "По убыванию" else "")
                    }
                    cell("")
                    cell(req.columns?.getOrNull(rowNumber))
                }
            }
            when {
                !sortSelected && !filterSelected-> colNum = 8
                !sortSelected -> {
                    excludeCols.add(11)
                    colNum = 12
                }
                !filterSelected -> {
                    excludeCols.add(10)
                    colNum = 11
                }
                else -> {
                    excludeCols.add(11)
                    excludeCols.add(14)
                    colNum = 15
                }
            }
        }.autosize(colNum = colNum, excludeCols = excludeCols)
    }

    private fun Filter?.getCondition() = when {
        this.isBoolean() && this?.condition == "Равно" -> "Есть"
        this.isBoolean() && this?.condition == "Не равно" -> "Пусто"
        else -> this?.condition
    }

    private fun Filter?.getValue() = when {
        this.isBoolean() -> ""
        else -> this?.value?.joinToString("\n")?.replace("null", "Пусто")
    }

    private fun Filter?.isBoolean() = this?.value?.any { it in sequenceOf(true, false) } == true

    private fun getOrgUnitData(
        geoFilter: GeoFilter?
    ): OrgUnitData = transaction {
        val terNames = if (geoFilter?.territory?.isNotEmpty() == true) {
            TerritoryEntity
                .find { TerritoryTable.id.castToLong() inList geoFilter.territory }
                .map { it.name }
        } else listOf()


        val mrNames = if (geoFilter?.mr?.isNotEmpty() == true) {
            MrEntity
                .find { MrTable.id inList geoFilter.mr }
                .map { it.name }
        } else listOf()


        val atpNames = if (geoFilter?.atp?.isNotEmpty() == true) {
            AtpEntity
                .find { AtpTable.id inList geoFilter.atp }
                .map { it.name }
        } else listOf()

        val mvzNames = if (geoFilter?.mvz?.isNotEmpty() == true) {
            MvzCodeEntity
                .find { MvzCodesTable.id inList geoFilter.mvz }
                .map { it.name }
        } else listOf()

        OrgUnitData(
            terName = terNames,
            mrName = mrNames,
            atpName = atpNames,
            mvzName = mvzNames,
        )
    }
}