package com.x5.logistics.service

import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.RichTextString
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.ss.util.CellRangeAddress
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.apache.poi.xssf.streaming.SXSSFWorkbook
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.lang.Integer.max
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import java.util.function.Consumer

/*
  XLSX DSL example:
fun main() {
    workbook {
        style { // Стиль для всех листов (POI CellStyle)
            alignment = HorizontalAlignment.CENTER
            verticalAlignment = VerticalAlignment.CENTER
        }

        sheet("Количество ТС") {
            style { // Стиль для всего листа. Переписывает наследуемый стиль
                alignment = HorizontalAlignment.CENTER
                verticalAlignment = VerticalAlignment.CENTER
                fillPattern = FillPatternType.SOLID_FOREGROUND
                fillForegroundColor = IndexedColors.GREY_25_PERCENT.index
            }
            header {
                head("a")
                clearStyle() // Сброс стиля в значение по умолчанию. ("пустой стиль")
                head("b")
                {
                    head("b1") // Подзаголовок
                }
                style {
                    alignment = HorizontalAlignment.CENTER
                    verticalAlignment = VerticalAlignment.CENTER
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                    fillForegroundColor = IndexedColors.LIGHT_BLUE.index
                }
                head("c")
                {
                    head("c1") // 2 ряд заголовка
                    head("c2")
                    head("c3")
                    {
                        head("c31") // 3 ряд заголовка
                        head("c32") {
                            style { // Стиль только для 4 ряда заголовка
                                alignment = HorizontalAlignment.CENTER
                                verticalAlignment = VerticalAlignment.CENTER
                                fillPattern = FillPatternType.SOLID_FOREGROUND
                                fillForegroundColor = IndexedColors.BLUE_GREY.index
                            }
                            head("c321") // 4 ряд заголовка
                            head("c322")
                            head("c323")
                        }
                        head("c33")
                    }
                }
                clearStyle()
                head("d")
            }
            row { // Новый ряд
                cell("R1", shift = 1, rows = 2, cols = 2) // Ячейка. shift - сдвиг колонок от текущего местоположения. rows и cols размер ячейки
                style {
                    alignment = HorizontalAlignment.LEFT
                    verticalAlignment = VerticalAlignment.CENTER
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                    fillForegroundColor = IndexedColors.GREEN.index
                }
                cell("R2", shift = 1, rows = 2, cols = 2)
            }
            row {} // Пустой ряд
            row {
                cell("R3")
                cell(
                    "R4",
                    cc = { // Consumer<Cell> для созданной ячейки
                        val factory = it.sheet.workbook.creationHelper
                        val anchor = factory.createClientAnchor()
                        anchor.setCol1(10)
                        anchor.setCol2(20)
                        anchor.setRow1(7)
                        anchor.setRow2(8)

                        val comment = it.sheet.createDrawingPatriarch().createCellComment(anchor)
                        comment.string = factory.createRichTextString("Test")
                        it.cellComment = comment
                    })
            }
            autosize() // Авто выравнивание всех столбцов на листе.
        }
    }.toFile("test.xlsx") // Запись в файл.
}
*/
@DslMarker
annotation class XlsxBuilder

@XlsxBuilder
class WbBuilder(
    val wb: Workbook = XSSFWorkbook()
) {
    private var currStyle: CellStyle? = null
//    var wb: Workbook = XSSFWorkbook()

    fun style(init: CellStyle.() -> Unit): CellStyle {
        val style = wb.createCellStyle()
        style.init()

        currStyle = style
        return style
    }

    fun clearStyle() {
        currStyle = null
    }

    fun sheet(name: String, init: SheetBuilder.() -> Unit): SheetBuilder {
        val sheetBuilder = SheetBuilder(wb, currStyle, name)
        sheetBuilder.init()
        return sheetBuilder
    }

    fun setActiveSheet(name: String) {
        for (i in 0..<wb.numberOfSheets) {
            wb.getSheetAt(i).isSelected = false
        }
        val index = wb.getSheetIndex(name)
        wb.getSheetAt(index).isSelected = true;
        wb.setActiveSheet(index)
    }

    fun write(out: OutputStream) {
        wb.write(out)
    }

    fun toInputStream(): InputStream {
        ByteArrayOutputStream().use {
            wb.write(it)
            if (wb is SXSSFWorkbook) wb.dispose()
            return ByteArrayInputStream(it.toByteArray())
        }
    }

    fun toFile(fileName: String) {
        FileOutputStream(fileName).use {
            wb.write(it)
        }
    }
}

@XlsxBuilder
class SheetBuilder(val wb: Workbook, var currStyle: CellStyle?, val name: String) {
    private val sheet: Sheet = wb.createSheet(name)
    private var rowIdx = 0

    init {
        if (sheet is SXSSFSheet) sheet.trackAllColumnsForAutoSizing()
    }

    fun style(init: CellStyle.() -> Unit): CellStyle {
        val style = wb.createCellStyle()
        style.init()

        currStyle = style
        return style
    }

    fun clearStyle() {
        currStyle = null
    }

    fun row(init: RowBuilder.() -> Unit): RowBuilder {
        val rb = RowBuilder(sheet.createRow(rowIdx++), currStyle)
        rb.init()
        return rb
    }

    fun header(init: HeaderBuilder.() -> Unit): HeaderBuilder {
        val hb = HeaderBuilder(sheet, currStyle)
        hb.init()

        val headers = hb.headers
        val headersDeep = headersDeep(headers)

        for (rowNum in 0..headersDeep) {
            sheet.createRow(rowIdx + rowNum)
        }

        var colIdx = 0
        headers.forEach { colIdx = createHeader(sheet, rowIdx, colIdx, it, headersDeep) }

        rowIdx += headersDeep

        return hb
    }

    fun autosize() {
        var lastColNum = 0
        for (rowNum in sheet.firstRowNum..sheet.lastRowNum) {
            lastColNum = max(lastColNum, sheet.getRow(rowNum)?.lastCellNum?.toInt() ?: 0)
        }

        for (colIdx in 0..lastColNum) {
            sheet.autoSizeColumn(colIdx, true)
        }
    }

    fun autosize(
        colNum: Int,
        excludeCols: List<Int> = emptyList()
    ) {
        if (sheet is SXSSFSheet) sheet.trackAllColumnsForAutoSizing()
        for (colIdx in 0..colNum) {
            if (colIdx !in excludeCols) {
                sheet.autoSizeColumn(colIdx, true)
            }
        }
    }

    fun write(out: OutputStream) {
        wb.write(out)
    }
}

@XlsxBuilder
class HeaderBuilder(val sheet: Sheet, var currStyle: CellStyle?) {
    var headers: MutableList<HeaderInfo> = mutableListOf()

    fun style(init: CellStyle.() -> Unit) {
        val style = sheet.workbook.createCellStyle()
        style.init()

        currStyle = style
    }

    fun clearStyle() {
        currStyle = null
    }

    fun head(
        value: Any?,
        cols: Int = 1,
        cc: Consumer<Cell>? = null,
        subHeader: (HeaderBuilder.() -> Unit)? = null
    ) {
        val hb = HeaderBuilder(sheet, currStyle)

        val subHeaders = if (subHeader != null) {
            hb.subHeader()
            hb.headers
        } else {
            null
        }

        headers.add(HeaderInfo(value, cols, currStyle, cc, subHeaders))
    }
}

data class HeaderInfo(
    val value: Any?,
    val cols: Int = 1,
    val cellStyle: CellStyle?,
    val cc: Consumer<Cell>? = null,
    val subHeaders: List<HeaderInfo>?
)

@XlsxBuilder
class RowBuilder(val row: Row, var currStyle: CellStyle?) {
    private var colIdx = 0

    fun style(init: CellStyle.() -> Unit) {
        val style = row.sheet.workbook.createCellStyle()
        style.init()

        currStyle = style
    }

    fun clearStyle() {
        currStyle = null
    }

    fun setRowHeight(lines: Int) {
        row.heightInPoints = row.sheet.defaultRowHeightInPoints * lines
    }

    fun cell(
        value: Any?,
        shift: Int = 0,
        cols: Int = 1,
        rows: Int = 1,
        cc: Consumer<Cell>? = null,
        initStyle: (CellStyle.() -> Unit)? = null
    ): Cell {
        val res = cell0(
            value, row, colIdx, currStyle, shift, cols, rows, cc, initStyle
        )

        colIdx = res.second

        return res.first
    }
}

private fun createHeader(sheet: Sheet, rowIdx: Int, colIdx: Int, header: HeaderInfo, deepRemains: Int): Int {
    return if (header.subHeaders == null) {
        cell0(header.value, sheet.getRow(rowIdx), colIdx, header.cellStyle, 0, header.cols, deepRemains, header.cc).second
    } else {
        var subHeaderCols = colIdx
        header.subHeaders.forEach { subHeaderCols = createHeader(sheet, rowIdx + 1, subHeaderCols, it, deepRemains - 1) }

        cell0(header.value, sheet.getRow(rowIdx), colIdx, header.cellStyle, 0, subHeaderCols - colIdx, 1, header.cc)

        subHeaderCols
    }
}

private fun headersDeep(headers: List<HeaderInfo>?, deep: Int = 0): Int =
    headers?.map { headersDeep(it.subHeaders, deep + 1) }?.maxOrNull() ?: deep

private fun cell0(
    value: Any?,
    row: Row,
    currColIdx: Int,
    currStyle: CellStyle?,
    shift: Int = 0,
    cols: Int = 1,
    rows: Int = 1,
    cc: Consumer<Cell>? = null,
    initStyle: (CellStyle.() -> Unit)? = null
): Pair<Cell, Int> {
    var colIdx = currColIdx + shift

    val cell = row.createCell(colIdx)

    if (rows != 1 || cols != 1) {
        row.sheet.addMergedRegion(
            CellRangeAddress(
                row.rowNum, row.rowNum + (rows - 1),
                colIdx, (colIdx) + (cols - 1)
            )
        )
    }

    colIdx += cols

    if (initStyle != null) {
        val style = cell.sheet.workbook.createCellStyle()
        style.initStyle()
        cell.cellStyle = style
    } else if (currStyle != null) {
        cell.cellStyle = currStyle
    }

    cc?.accept(cell)

    if (value != null) {
        when (value) {
            is Date -> cell.setCellValue(value)
            is Number -> cell.setCellValue(value.toDouble())
            is LocalDate -> cell.setCellValue(value)
            is LocalDateTime -> cell.setCellValue(value)
            is Boolean -> if (value) cell.setCellValue("Есть")
            is RichTextString -> cell.setCellValue(value)
            is Calendar -> cell.setCellValue(value)
            else -> cell.setCellValue(value.toString())
        }
    } else {
        cell.setCellValue("")
    }

    return Pair(cell, colIdx)
}

fun workbook(init: WbBuilder.() -> Unit): WbBuilder {
    val wb = WbBuilder()
    wb.init()
    return wb
}

fun streamWorkbook(useSharedStrings: Boolean = false, init: WbBuilder.() -> Unit): WbBuilder {
    val wb = if (useSharedStrings) {
        WbBuilder(SXSSFWorkbook(null, -1, true, true))
    } else {
        WbBuilder(SXSSFWorkbook(-1))
    }
    wb.init()
    return wb
}

fun streamWorkbook(window: Int, init: WbBuilder.() -> Unit): WbBuilder {
    val wb = WbBuilder(SXSSFWorkbook(window))
    wb.init()
    return wb
}

fun streamWorkbook(window: Int, useSharedStrings: Boolean = false, init: WbBuilder.() -> Unit): WbBuilder {
    val wb = WbBuilder(SXSSFWorkbook(null, window, true, useSharedStrings))
    wb.init()
    return wb
}

fun WbBuilder.getStyles(): Map<String, CellStyle> {
    val generalStyle = style { dataFormat = wb.createDataFormat().getFormat("General") }
    val floatStyle = style { dataFormat = wb.createDataFormat().getFormat("0.00;;0;") }
    val dateStyle = style { dataFormat = wb.createDataFormat().getFormat("dd.MM.yyyy") }
    return mapOf(
        "general" to generalStyle,
        "float" to floatStyle,
        "date" to dateStyle
    )
}
