package com.x5.logistics.service

import com.x5.logistics.repository.fuel.FuelModalEffectRepo
import com.x5.logistics.rest.dto.fuel.FuelModalWidgetReq
import com.x5.logistics.rest.dto.fuel.FuelModalWidgetResp
import com.x5.logistics.rest.dto.fuel.percent.FuelPercentModalReq
import com.x5.logistics.rest.dto.fuel.percent.FuelPercentModalResp
import org.springframework.stereotype.Service

@Service
class FuelEffectService(
    val repo: FuelModalEffectRepo
) {
    fun getFuelEffect(req: FuelPercentModalReq): FuelPercentModalResp {
        return repo.getFuelPercentModal(req)
    }

    suspend fun getFuelVehicleAndDriverData(req: FuelModalWidgetReq): FuelModalWidgetResp {
        return repo.getFuelVehicleAndDriverData(req)
    }
}