package com.x5.logistics.service.charting

import com.x5.logistics.data.charting.ChartTimeSpansDictionary
import com.x5.logistics.repository.charting.ChartTimeSpansDictionaryRepository
import com.x5.logistics.rest.dto.charting.ChartTimeSpansDictionaryDto
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ChartTimeSpansDictionaryService(
    private val repo: ChartTimeSpansDictionaryRepository
) {

    fun getAll(): List<ChartTimeSpansDictionaryDto> = repo.findAll().map { it.toDto() }

    fun getByName(name: String): ChartTimeSpansDictionaryDto = repo.findByName(name)!!.toDto()

    @Transactional
    fun new(item: ChartTimeSpansDictionaryDto): ChartTimeSpansDictionaryDto = repo.save(item.toEntity()).toDto()

    @Transactional
    fun update(item: ChartTimeSpansDictionaryDto): ChartTimeSpansDictionaryDto = repo.save(item.toEntity()).toDto()

    @Transactional
    fun deleteById(id: Long) = repo.deleteById(id)

    private fun ChartTimeSpansDictionary.toDto(): ChartTimeSpansDictionaryDto {
        return ChartTimeSpansDictionaryDto(
            id = this.id,
            name = this.name,
            sysName = this.sysName,
            order = this.order ?: 99,
        )
    }

    private fun ChartTimeSpansDictionaryDto.toEntity(): ChartTimeSpansDictionary {
        return ChartTimeSpansDictionary(
            id = this.id,
            name = this.name,
            sysName = this.sysName,
            order = this.order,
        )
    }
}
