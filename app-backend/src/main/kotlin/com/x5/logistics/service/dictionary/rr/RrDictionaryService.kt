package com.x5.logistics.service.dictionary.rr

import com.x5.logistics.data.AtpEntity
import com.x5.logistics.data.dictionary.rr.Month
import com.x5.logistics.data.dictionary.rr.RrDictionaryEntity
import com.x5.logistics.data.dictionary.rr.RrDictionaryGroup
import com.x5.logistics.data.dictionary.rr.RrDictionaryGroupId
import com.x5.logistics.data.dictionary.vrt.ToroWork
import com.x5.logistics.data.dictionary.vrt.ToroWorksSubtype
import com.x5.logistics.repository.AtpRepo
import com.x5.logistics.repository.dictionary.rr.RrDictionaryEntityRepo
import com.x5.logistics.repository.dictionary.rr.RrDictionaryGroupRepo
import com.x5.logistics.repository.dictionary.rr.RrDictionaryTonnageRepo
import com.x5.logistics.repository.dictionary.rr.RrDictionaryViewEntityRepo
import com.x5.logistics.repository.dictionary.vrt.ToroWorkJpaRepository
import com.x5.logistics.rest.dto.SortItem
import com.x5.logistics.rest.dto.dictionary.rr.AtpTonnagesFilter
import com.x5.logistics.rest.dto.dictionary.rr.DeleteRrDictionaryGroupReq
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryDetailedGroupDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryDictAtpDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryDictDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryDictVrtDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryFiltersDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupField
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupFilterField
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupIdDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupMonthDetailsDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupRateDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupsListReq
import com.x5.logistics.rest.dto.dictionary.rr.UpdateRrDictionaryGroupReq
import com.x5.logistics.rest.dto.dictionary.rr.UpdatedByVRT
import com.x5.logistics.rest.dto.dictionary.rr.VrtDetails
import com.x5.logistics.rest.exception.RecordAlreadyExistsException
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.util.getLogger
import jakarta.persistence.criteria.CriteriaBuilder
import jakarta.persistence.criteria.Order
import jakarta.persistence.criteria.Predicate
import jakarta.persistence.criteria.Root
import jakarta.transaction.Transactional
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.LocalDate

@Service
class RrDictionaryService(
    private val rrDictionaryGroupRepo: RrDictionaryGroupRepo,
    private val rrDictionaryViewEntityRepo: RrDictionaryViewEntityRepo,
    private val atpRepo: AtpRepo,
    private val rrDictionaryEntityRepo: RrDictionaryEntityRepo,
    private val toroWorkJpaRepository: ToroWorkJpaRepository,
    private val rrDictionaryTonnageRepo: RrDictionaryTonnageRepo,
) {
    val logger = getLogger()
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    fun getRrDictionaryGroups(req: RrDictionaryGroupsListReq): List<RrDictionaryGroupDto> =
        rrDictionaryGroupRepo.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(RrDictionaryGroup::class.java)
            val root = query.from(RrDictionaryGroup::class.java)
            query.select(root)
            req.getPredicate(builder, root)?.let { predicate ->
                query.where(predicate)
            }
            req.sort.ifEmpty {
                listOf(
                    SortItem(RrDictionaryGroupField.ATP_NAME),
                    SortItem(RrDictionaryGroupField.TONNAGE)
                )
            }
                .map { it.getOrder(builder, root) }.takeIf { it.isNotEmpty() }?.let {
                    query.orderBy(it)
                }
            val typedQuery = createQuery(query)
            typedQuery.firstResult = req.pageNumber * req.pageSize
            typedQuery.maxResults = req.pageSize
            typedQuery.resultList.map { it.toDto(req.year) }
        }

    fun countRrDictionaryGroups(req: RrDictionaryGroupsListReq): Int = rrDictionaryGroupRepo.getByJpa {
        val builder = criteriaBuilder
        val query = builder.createQuery(Long::class.javaObjectType)
        val root = query.from(RrDictionaryGroup::class.java)
        query.select(builder.count(root))
        req.getPredicate(builder, root)?.let { predicate ->
            query.where(predicate)
        }
        val typedQuery = createQuery(query)
        typedQuery.singleResult.toInt()
    }

    fun createRrDictionaryGroup(author: String, req: RrDictionaryGroupIdDto): RrDictionaryGroupDto {
        val atp = atpRepo.findById(req.atpId).orElseThrow {
            WrongRequestDataException("АТП не найдено")
        }
        rrDictionaryGroupRepo.findById(
            RrDictionaryGroupId(
                atp = req.atpId,
                tonnage = req.tonnage,
            )
        )
            .orElse(null)
            ?.let {
                throw RecordAlreadyExistsException("Запись с такой парой АТП-тоннаж уже существует")
            }
        val group = RrDictionaryGroup(
            atp = atp,
            tonnage = req.tonnage,
            createdAt = Instant.now(),
            createdBy = author
        )
        val rrDictionaryGroup = rrDictionaryGroupRepo.save(group)

        return rrDictionaryGroup.toDto(req.year)
    }

    fun deleteRrDictionaryGroup(req: DeleteRrDictionaryGroupReq) {
        val childrenCount = rrDictionaryViewEntityRepo.countByAtpTonnage(
            atpId = req.atpId,
            tonnage = req.tonnage
        )
        if (childrenCount > 0) {
            throw WrongRequestDataException("Строка не пуста")
        }
        rrDictionaryGroupRepo.deleteById(
            RrDictionaryGroupId(
                atp = req.atpId,
                tonnage = req.tonnage
            )
        )
    }

    fun getRrDictionaryDetailedGroup(req: RrDictionaryGroupIdDto): RrDictionaryDetailedGroupDto {
        val entity = rrDictionaryGroupRepo.findById(
            RrDictionaryGroupId(
                atp = req.atpId,
                tonnage = req.tonnage
            )
        ).orElseThrow {
            WrongRequestDataException("Строка не найдена")
        }
        return entity.toDetailedDto(req.year)
    }

    @Transactional
    fun updateRrDictionaryGroup(author: String, req: UpdateRrDictionaryGroupReq): RrDictionaryDetailedGroupDto {
        val group = rrDictionaryGroupRepo.findById(
            RrDictionaryGroupId(
                atp = req.atpId,
                tonnage = req.tonnage
            )
        ).orElseThrow {
            WrongRequestDataException("Группа не найдена")
        }
        val atp = atpRepo.findById(req.atpId).orElseThrow {
            WrongRequestDataException("АТП не найдено")
        }
        val allVrtIds = req.details.asSequence()
            .map { it.vrtId }
            .distinct()
            .toList()
        val vrtById = toroWorkJpaRepository.findAllById(allVrtIds).associateBy { it.id }
        allVrtIds.forEach {
            if (it !in vrtById) {
                throw WrongRequestDataException("ВРТ c кодом $it не найдено")
            }
        }

        req.details.asSequence()
            .filter { it.rate >= 0 }
            .forEach {
                val startDateValue = LocalDate.of(req.year, it.month.ordinal + 1, 1)

                val entity = rrDictionaryEntityRepo.findByYearAndAtp_IdAndTonnageAndToroWork_IdAndMonth(
                    req.year,
                    req.atpId,
                    req.tonnage,
                    it.vrtId,
                    it.month
                )?.copy(rate = it.rate, updatedAt = Instant.now(), updatedBy = author) ?: RrDictionaryEntity(
                    year = req.year,
                    atp = atp,
                    toroWork = vrtById[it.vrtId]!!,
                    tonnage = req.tonnage,
                    month = it.month,
                    rate = it.rate,
                    createdAt = Instant.now(),
                    createdBy = author,
                    updatedAt = Instant.now(),
                    updatedBy = author,
                    startDate = startDateValue,
                    endDate = startDateValue.plusMonths(1)
                )

                rrDictionaryEntityRepo.save(entity)
            }

        return group.toDetailedDto(req.year)
    }

    fun getRrDictionaryDict(): RrDictionaryDictDto {
        val atps = atpRepo.findAll().map {
            RrDictionaryDictAtpDto(
                id = it.id,
                name = it.name
            )
        }.sortedBy { it.name }
        val tonnages = rrDictionaryTonnageRepo.findAll().map { it.tonnage }
        val vrts = toroWorkJpaRepository.findAll().map {
            RrDictionaryDictVrtDto(
                id = it.id,
                name = it.name,
                deprecated = (it.subtype?.id in listOf<Long>(1, 32))
            )
        }.sortedBy { it.id }
        return RrDictionaryDictDto(
            atps = atps,
            tonnages = tonnages,
            vrts = vrts
        )
    }

    fun getRrDictionaryFilters(
        atpId: Long?
    ): RrDictionaryFiltersDto {
        val predicates = buildList<(RrDictionaryGroup) -> Boolean> {
            if (atpId != null) {
                add {
                    it.atp.id == atpId
                }
            }
        }
        val groups = run {
            val allGroups = rrDictionaryGroupRepo.findAll()
            if (predicates.isEmpty()) {
                allGroups
            } else {
                allGroups.filter { group ->
                    predicates.all { it(group) }
                }
            }
        }
        val atps = groups.asSequence()
            .map { it.atp }
            .distinctBy { it.id }
            .sortedBy { it.id }
            .map { RrDictionaryDictAtpDto(it.id, it.name) }
            .toList()
        val tonnages = groups.asSequence()
            .map { it.tonnage }
            .distinct()
            .sorted()
            .toList()
        return RrDictionaryFiltersDto(
            atps = atps,
            tonnages = tonnages,
            years = (2010..(LocalDate.now().year + 1)).toList()
        )
    }

    fun getFilterAtpTonnages(): List<AtpTonnagesFilter> {
        return rrDictionaryGroupRepo.findDistinctAtpIdAndTonnage()
    }

    private fun RrDictionaryGroupsListReq.getPredicate(
        criteriaBuilder: CriteriaBuilder,
        root: Root<RrDictionaryGroup>
    ): Predicate? {
        val predicates = buildList {
            filters.forEach { filter ->
                val path = when (filter.name) {
                    RrDictionaryGroupFilterField.ATP_ID -> root.get<AtpEntity>("atp").get<Long>("id")
                    RrDictionaryGroupFilterField.ATP_NAME -> root.get<AtpEntity>("atp").get<String>("name")
                    RrDictionaryGroupFilterField.TONNAGE -> root.get<Double>("tonnage")
                    RrDictionaryGroupFilterField.VRT_SUBTYPE_ID -> root.get<ToroWork>("toroWork").get<ToroWorksSubtype>("subtype").get<Long>("id")
                }
                add(path.`in`(filter.value))
            }
        }.toTypedArray()
        return if (predicates.isEmpty()) {
            null
        } else if (predicates.size == 1) {
            predicates[0]
        } else {
            criteriaBuilder.and(*predicates)
        }
    }

    private fun SortItem<RrDictionaryGroupField>.getOrder(
        criteriaBuilder: CriteriaBuilder,
        root: Root<RrDictionaryGroup>
    ): Order {
        val path = when (column) {
            RrDictionaryGroupField.YEAR -> root.get<Int>("year")
            RrDictionaryGroupField.ATP_NAME -> root.get<AtpEntity>("atp").get<String>("name")
            RrDictionaryGroupField.TONNAGE -> root.get<Double>("tonnage")
            RrDictionaryGroupField.CREATED_BY -> root.get<String>("createdBy")
            RrDictionaryGroupField.CREATED_AT -> root.get<Instant>("createdAt")
        }
        return if (asc) {
            criteriaBuilder.asc(path)
        } else {
            criteriaBuilder.desc(path)
        }
    }

    private fun RrDictionaryGroup.toDto(selectedYear: Int): RrDictionaryGroupDto {
        val entities = rrDictionaryViewEntityRepo.findByAtpTonnage(atp.id, tonnage)
        val updatedBy = entities.filter { it.year == selectedYear }
            .maxByOrNull { it.updatedAt }?.updatedBy
        val entitiesByMonth = entities.filter { it.year == selectedYear }
            .associateBy { it.month }
        return RrDictionaryGroupDto(
            year = selectedYear,
            atpId = atp.id,
            atpName = atp.name,
            tonnage = tonnage,
            deletable = entities.isEmpty(),
            rates = Month.entries.asSequence().map { month ->
                RrDictionaryGroupRateDto(
                    month = month,
                    rate = entitiesByMonth[month]?.rate ?: 0.0
                )
            }.toList(),
            updatedBy = updatedBy ?: ""
        )
    }

    private fun RrDictionaryGroup.toDetailedDto(year: Int): RrDictionaryDetailedGroupDto {
        val entities = rrDictionaryEntityRepo.findByYearAtpTonnage(
            year = year,
            atpId = atp.id,
            tonnage = tonnage
        )
        val entitiesByMonth = entities.groupBy { it.month }
        return RrDictionaryDetailedGroupDto(
            atpId = atp.id,
            atpName = atp.name,
            year = year,
            tonnage = tonnage,
            deletable = entitiesByMonth.isEmpty(),
            updatedByVRT = entities.map {
                UpdatedByVRT(
                    vrtId = it.toroWork.id,
                    updatedBy = it.updatedBy ?: ""
                )
            },
            details = Month.entries.asSequence()
                .map { month ->
                    val entities = entitiesByMonth[month].orEmpty()
                    RrDictionaryGroupMonthDetailsDto(
                        month = month,
                        sumRate = entities.sumOf { it.rate.toBigDecimal() }.toDouble(),
                        vrt = entities.map {
                            VrtDetails(
                                id = it.toroWork.id,
                                name = it.toroWork.name,
                                rate = it.rate
                            )
                        }
                    )
                }.toList()
        )
    }
}
