package com.x5.logistics.service.repair

import com.x5.logistics.repository.repair.RepairWidgetQtyCostsRepo
import com.x5.logistics.rest.dto.repair.workspace.qtycost.RepairWidgetQtyCostsReq
import com.x5.logistics.rest.dto.repair.workspace.qtycost.RepairWidgetQtyCostsResp
import com.x5.logistics.util.isAlmostZero
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.springframework.stereotype.Service

@Service
class RepairWidgetQtyCostsService(
    val repo: RepairWidgetQtyCostsRepo
) {
    suspend fun getCostsWidgetData(req: RepairWidgetQtyCostsReq): RepairWidgetQtyCostsResp = coroutineScope {
        val equnrCount = async(Dispatchers.IO) { repo.getEqunrCount(req) }
        val equnrCountBe = async(Dispatchers.IO) { repo.getEqunrCountBe(req) }
        val equnrCountPrevious = async(Dispatchers.IO) { repo.getEqunrCountPrevious(req) }
        val countExpenses = async(Dispatchers.IO) { repo.getCountExpenses(req) }
        val countExpensesBe = async(Dispatchers.IO) { repo.getCountExpensesBe(req) }
        val countExpensesPrevious = async(Dispatchers.IO) { repo.getCountExpensesPrevious(req) }

        val qtyRepair = countExpenses.await().orderCount / equnrCount.await()
        val qtyRepairPrev = countExpensesPrevious.await().orderCount /
                if (equnrCountPrevious.await() == 0.0) 1.0 else equnrCountPrevious.await()
        val lastQtyRepairVehicleVal = qtyRepair - qtyRepairPrev
        val qtyRepairBe = countExpensesBe.await().orderCount / (equnrCountBe.await() ?: 1.0)
        val avgQtyRepairVehicleVal = qtyRepair - qtyRepairBe
        val costRepair = countExpenses.await().repairExpenses?.div(equnrCount.await()) ?: 0.0
        val costRepairPrev = countExpensesPrevious.await().repairExpenses?.div(equnrCountPrevious.await()) ?: 0.0
        val lastQtyRepairVehiclePcs = costRepair - costRepairPrev
        val costRepairBe = countExpensesBe.await().repairExpenses?.div(equnrCountBe.await() ?: 1.0) ?: 0.0
        val avgQtyRepairVehiclePcs = costRepair - costRepairBe


        if (qtyRepair.isAlmostZero && costRepair.isAlmostZero) {
            RepairWidgetQtyCostsResp(
                qtyRepair = null,
                costRepair = null,
                avgQtyRepairVehiclePcs = null,
                lastQtyRepairVehiclePcs = null,
                avgQtyRepairVehicleVal = null,
                lastQtyRepairVehicleVal = null,
                isEmpty = true
            )
        } else {
            RepairWidgetQtyCostsResp(
                qtyRepair = qtyRepair.toBigDecimal(),
                costRepair = costRepair.toBigDecimal(),
                avgQtyRepairVehiclePcs = avgQtyRepairVehiclePcs.toBigDecimal(),
                lastQtyRepairVehiclePcs = lastQtyRepairVehiclePcs.toBigDecimal(),
                avgQtyRepairVehicleVal = avgQtyRepairVehicleVal.toBigDecimal(),
                lastQtyRepairVehicleVal = lastQtyRepairVehicleVal.toBigDecimal(),
                isEmpty = qtyRepair.isAlmostZero
                        && costRepair.isAlmostZero == true
                        && avgQtyRepairVehiclePcs.isAlmostZero == true
                        && lastQtyRepairVehiclePcs.isAlmostZero == true
                        && avgQtyRepairVehicleVal.isAlmostZero
                        && lastQtyRepairVehicleVal.isAlmostZero
            )
        }
    }
}