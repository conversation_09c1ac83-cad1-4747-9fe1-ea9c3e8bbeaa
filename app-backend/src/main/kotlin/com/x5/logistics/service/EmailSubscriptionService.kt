package com.x5.logistics.service

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.Pls
import com.x5.logistics.data.dictionary.UnitTable
import com.x5.logistics.data.subscriptions.MetadataTablesGroupViewTable
import com.x5.logistics.data.subscriptions.SubscriptionMailEventsTable
import com.x5.logistics.data.subscriptions.UserEmailSubscriptionSubjectTable
import com.x5.logistics.data.subscriptions.UserEmailSubscriptionTable
import com.x5.logistics.repository.CountAll
import com.x5.logistics.repository.Granularity
import com.x5.logistics.repository.countWithFilter
import com.x5.logistics.repository.dateLiteral
import com.x5.logistics.repository.getGranularityFrom
import com.x5.logistics.repository.getGranularityTo
import com.x5.logistics.repository.moscowZoneId
import com.x5.logistics.util.getLogger
import jakarta.mail.internet.MimeMessage
import org.apache.velocity.VelocityContext
import org.apache.velocity.app.Velocity
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.SqlExpressionBuilder.neq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.plus
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.sum
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import org.jetbrains.exposed.sql.vendors.ForUpdateOption
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.ClassPathResource
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.MimeMessageHelper
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.io.StringWriter
import java.time.Instant
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*


@Service
class EmailSubscriptionService(
    private val emailSender: JavaMailSender
) {
    @Autowired
    private lateinit var self: EmailSubscriptionService

    @Value("\${x5.logistics.mail.from}")
    lateinit var from: String

    private val log = getLogger()

    @Scheduled(cron = "\${x5.logistics.sendSubscriptionMails.cron}", zone = "Europe/Moscow")
    fun sendSubscriptionMails() {
        val activeSubscriptions = self.getActiveSubscriptions()
        val triggerId = "dailySubscriptions-${LocalDate.now()}"
        activeSubscriptions.forEach { (email, subject) ->
            self.addSubscriptionMailEvent(
                email = email,
                type = MailEventType.WAYBILLS_NOT_READY,
                triggerId = triggerId
            )
        }
    }

    @Scheduled(cron = "\${x5.logistics.sendSubscriptionMailsLoop.cron}", zone = "Europe/Moscow")
    fun sendingMailsLoop() {
        var sendEvent = self.sendingMailsLoopIteration()
        while (sendEvent != null) {
            sendEvent = self.sendingMailsLoopIteration(sendEvent)
        }
    }

    @ExposedTransactional
    fun addSubscriptionMailEvent(
        email: String,
        type: MailEventType,
        triggerId: String
    ) {
        try {
            SubscriptionMailEventsTable.insert {
                it[SubscriptionMailEventsTable.email] = email
                it[SubscriptionMailEventsTable.type] = type.name
                it[SubscriptionMailEventsTable.triggerId] = triggerId
            }
        } catch (_: Exception) { } //ignore duplicates
    }

    @ExposedTransactional
    fun getActiveSubscriptions(): Map<String, String> =
        UserEmailSubscriptionTable.join(
            otherTable = UserEmailSubscriptionSubjectTable,
            joinType = JoinType.INNER,
            onColumn = UserEmailSubscriptionTable.subject,
            otherColumn = UserEmailSubscriptionSubjectTable.id
        ).select(
            UserEmailSubscriptionTable.email,
            UserEmailSubscriptionSubjectTable.subject
        )
            .withDistinct()
            .where(
                UserEmailSubscriptionTable.unsubscriptionDate.isNull() or
                        (UserEmailSubscriptionTable.unsubscriptionDate less UserEmailSubscriptionTable.subscriptionDate)
            ).asSequence()
            .map { it[UserEmailSubscriptionTable.email] to it[UserEmailSubscriptionSubjectTable.subject] }
            .toMap()

    @ExposedTransactional
    fun sendingMailsLoopIteration(iterator: SendingMailsLoopIterationInfo? = null): SendingMailsLoopIterationInfo? {
        val info = SubscriptionMailEventsTable.selectAll()
            .forUpdate(ForUpdateOption.PostgreSQL.ForUpdate(ForUpdateOption.PostgreSQL.MODE.SKIP_LOCKED))
            .where(SubscriptionMailEventsTable.sendTime.isNull())
            .also { query ->
                if (iterator != null) {
                    query.andWhere {
                        (SubscriptionMailEventsTable.type greater iterator.type.name) or
                                (
                                        (SubscriptionMailEventsTable.type eq iterator.type.name) and
                                                (SubscriptionMailEventsTable.email greater iterator.email)
                                ) or
                                (
                                        (SubscriptionMailEventsTable.type eq iterator.type.name) and
                                                (SubscriptionMailEventsTable.email eq iterator.email) and
                                                (SubscriptionMailEventsTable.triggerId greater iterator.triggerId)
                                )
                    }
                }
            }
            .orderBy(SubscriptionMailEventsTable.type)
            .orderBy(SubscriptionMailEventsTable.email)
            .orderBy(SubscriptionMailEventsTable.triggerId)
            .limit(1)
            .map {
                SendingMailsLoopIterationInfo(
                    email = it[SubscriptionMailEventsTable.email],
                    type = MailEventType.valueOf(it[SubscriptionMailEventsTable.type]),
                    triggerId = it[SubscriptionMailEventsTable.triggerId],
                    state = it[SubscriptionMailEventsTable.state]
                )
            }.singleOrNull()
        if (info != null) {
            try {
                handleMessageEvent(info)
            } catch (e: Exception) {
                log.warn("exception sending mail", e)
            }
        }
        return info
    }

    fun handleMessageEvent(event: SendingMailsLoopIterationInfo) {
        when (event.type) {
            MailEventType.WAYBILLS_NOT_READY -> {
                val ready = MetadataTablesGroupViewTable.select(MetadataTablesGroupViewTable.isAllTableUpdate)
                    .where {
                        MetadataTablesGroupViewTable.tableGroup eq "PL"
                    }
                    .single()[MetadataTablesGroupViewTable.isAllTableUpdate]
                when {
                    ready -> {
                        emailSender.send(buildWbLongTimeOpenMail(event.email))
                        SubscriptionMailEventsTable.update({
                            (SubscriptionMailEventsTable.email eq event.email) and
                                    (SubscriptionMailEventsTable.type eq event.type.name) and
                                    (SubscriptionMailEventsTable.triggerId eq event.triggerId)
                        }) {
                            it[sendTime] = Instant.now()
                        }
                    }
                    event.state != SENT_NOT_READY -> {
                        emailSender.send(buildWbLongTimeOpenNotReadyMail(event.email))
                        SubscriptionMailEventsTable.update({
                            (SubscriptionMailEventsTable.email eq event.email) and
                                    (SubscriptionMailEventsTable.type eq event.type.name) and
                                    (SubscriptionMailEventsTable.triggerId eq event.triggerId)
                        }) {
                            it[state] = SENT_NOT_READY
                        }
                    }
                }
            }
        }
    }

    fun sendEmail(email: String, ready: Boolean) = transaction {
        if (ready) {
            emailSender.send(buildWbLongTimeOpenMail(email))
        } else {
            emailSender.send(buildWbLongTimeOpenNotReadyMail(email))
        }
    }

    private val SENT_NOT_READY = "sent not ready"

    private fun buildWbLongTimeOpenMail(email: String): MimeMessage {
        val mainPlNumber = countWithFilter(
            (Pls.plReserve neq true) and
                    (Pls.msaus neq true) and
                    (Pls.longTimeFlag eq true)
        )
        val totalMainPl = mainPlNumber.sum().over()
        val reservePlNumber = countWithFilter(
            (Pls.plReserve eq true) and
                    (Pls.msaus neq true) and
                    (Pls.longTimeFlag eq true)
        )
        val totalReservePl = reservePlNumber.sum().over()
        val repairPlNumber = countWithFilter(
            (Pls.msaus eq true) and (Pls.longTimeFlag eq true)
        )
        val totalRepairPl = repairPlNumber.sum().over()

        val endDate = Case().When(Pls.userStat.inList(listOf("ЧЗАК", "ЗАКР")), Pls.endDateFact).Else(Pls.endDatePlan.delegate)
        val allPlNumber = mainPlNumber + reservePlNumber + repairPlNumber
        val mvzCount = CountAll()
        var mvzNumber: Long? = null
        var totalMainNumber: Long? = null
        var totalReserveNumber: Long? = null
        var totalRepairNumber: Long? = null
        val dates = getWbLongTimeOpenDates()
        val items = Pls.select(
            Pls.mvzId,
            Pls.mvzName,
            mvzCount,
            mainPlNumber,
            reservePlNumber,
            repairPlNumber,
            totalMainPl,
            totalReservePl,
            totalRepairPl,
        )
            .where {
                Pls.tsGroup eq "Основное" and
                        (Pls.startDateFact.delegate lessEq dateLiteral(dates.weekTo)) and
                        (dateLiteral(dates.weekFrom) less endDate)
            }
            .groupBy(Pls.mvzId, Pls.mvzName)
            .having {
                allPlNumber greater 0
            }
            .orderBy(
                allPlNumber to SortOrder.DESC,
                mainPlNumber to SortOrder.DESC,
                reservePlNumber to SortOrder.DESC,
                repairPlNumber to SortOrder.DESC
            )
            .limit(10)
            .also { log.debug(it.prepareSQL(QueryBuilder(false))) }
            .map { row ->
                if (mvzNumber == null) mvzNumber = row[mvzCount]
                if (totalMainNumber == null) totalMainNumber = row[totalMainPl]
                if (totalReserveNumber == null) totalReserveNumber = row[totalReservePl]
                if (totalRepairNumber == null) totalRepairNumber = row[totalRepairPl]
                object {
                    val mvzId = row[Pls.mvzId]
                    val mvzName = row[Pls.mvzName]
                    val mainPlNumber = row[mainPlNumber]
                    val reservePlNumber = row[reservePlNumber]
                    val repairPlNumber = row[repairPlNumber]
                }
            }

        Velocity.init(Properties().apply {
            put("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader")
        })
        val context = VelocityContext(mapOf(
            "dates" to dates,
            "items" to items,
            "mainPlNumber" to totalMainNumber,
            "reservePlNumber" to totalReserveNumber,
            "repairPlNumber" to totalRepairNumber,
            "mvzNumber" to mvzNumber,
            "renderWbLongTime" to true,
            "renderWbLongTimeNotReady" to false,
        ))
        val body = StringWriter().use { out ->
            Velocity.getTemplate("subscriptionMail.vm").merge(context, out)
            out.toString()
        }
        val message = emailSender.createMimeMessage()
        val helper = MimeMessageHelper(message, true)
        helper.setFrom(<EMAIL>)
        helper.setTo(email)
        helper.setSubject(WB_LONG_TIME_OPEN_SUBJECT)
        helper.setText(body, true)
        helper.addFavicon()
        helper.addX5Logo()
        helper.addEnvelope()
        helper.addQr()
        helper.addDataTransportLogo()
        helper.addRightArrow()
        return message
    }

    private fun buildWbLongTimeOpenNotReadyMail(email: String): MimeMessage {
        Velocity.init(Properties().apply {
            put("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader")
        })
        val context = VelocityContext(mapOf(
            "dates" to getWbLongTimeOpenDates(),
            "renderWbLongTime" to false,
            "renderWbLongTimeNotReady" to true,
        ))
        val body = StringWriter().use { out ->
            Velocity.getTemplate("subscriptionMail.vm").merge(context, out)
            out.toString()
        }
        val message = emailSender.createMimeMessage()
        val helper = MimeMessageHelper(message, true)
        helper.setFrom(<EMAIL>)
        helper.setTo(email)
        helper.setSubject(WB_LONG_TIME_OPEN_SUBJECT)
        helper.setText(body, true)
        helper.addFavicon()
        helper.addX5Logo()
        helper.addExclamation()
        helper.addEnvelope()
        helper.addQr()
        helper.addDataTransportLogo()
        return message
    }
}

private const val PNG_CONTENT_TYPE = "image/png"
private const val WB_LONG_TIME_OPEN_SUBJECT = "Длительно открытые путевые листы"
private val dateStrFormat = DateTimeFormatter.ofPattern("dd.MM.yy")

private fun MimeMessageHelper.addFavicon() {
    addInline("favicon", { ClassPathResource("favicon.png").inputStream }, PNG_CONTENT_TYPE)
}

private fun MimeMessageHelper.addX5Logo() {
    addInline("x5Logo", { ClassPathResource("x5Logo.png").inputStream }, PNG_CONTENT_TYPE)
}

private fun MimeMessageHelper.addExclamation() {
    addInline("exclamation", { ClassPathResource("exclamation.png").inputStream }, PNG_CONTENT_TYPE)
}

private fun MimeMessageHelper.addEnvelope() {
    addInline("envelope", { ClassPathResource("envelope.png").inputStream }, PNG_CONTENT_TYPE)
}

private fun MimeMessageHelper.addQr() {
    addInline("qr", { ClassPathResource("qr.png").inputStream }, PNG_CONTENT_TYPE)
}

private fun MimeMessageHelper.addDataTransportLogo() {
    addInline("dataTransportLogo", { ClassPathResource("dataTransportLogo.png").inputStream }, PNG_CONTENT_TYPE)
}

private fun MimeMessageHelper.addRightArrow() {
    addInline("rightArrow", { ClassPathResource("rightArrow.png").inputStream }, PNG_CONTENT_TYPE)
}

private fun getWbLongTimeOpenDates() = run {
    val date = LocalDate.now(moscowZoneId).minusDays(1)
    var dateExpression = dateLiteral(date)
    var weekFromExpression = getGranularityFrom(Granularity.REPORTING_WEEK, dateExpression)
    var weekToExpression = getGranularityTo(Granularity.REPORTING_WEEK, dateExpression)
    var row = UnitTable.select(weekFromExpression, weekToExpression).single()
    var weekFrom = row[weekFromExpression]
    var weekTo = row[weekToExpression]
    if (weekTo > date) {
        dateExpression = dateLiteral(weekFrom.minusDays(1))
        weekFromExpression = getGranularityFrom(Granularity.REPORTING_WEEK, dateExpression)
        weekToExpression = getGranularityTo(Granularity.REPORTING_WEEK, dateExpression)
        row = UnitTable.select(weekFromExpression, weekToExpression).single()
        weekFrom = row[weekFromExpression]
        weekTo = row[weekToExpression]
    }
    object {
        val date = date
        val weekFrom = weekFrom
        val weekTo = weekTo

        val dateStr: String
            get() = date.format(dateStrFormat)

        val weekFromStr: String
            get() = weekFrom.format(dateStrFormat)

        val weekToStr: String
            get() = weekTo.format(dateStrFormat)
    }
}

class SendingMailsLoopIterationInfo(
    val email: String,
    val type: MailEventType,
    val triggerId: String,
    val state: String?
)

enum class MailEventType {
    WAYBILLS_NOT_READY
}
