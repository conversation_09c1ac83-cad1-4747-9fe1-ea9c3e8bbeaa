package com.x5.logistics.service.repshop

import com.x5.logistics.data.Month
import com.x5.logistics.data.repair.RepairPowerDto
import com.x5.logistics.repository.repair.repshop.RepshopProvisionExposedRepo
import com.x5.logistics.rest.dto.repair.workspace.repshop.RepshopProvisionWidgetReq
import com.x5.logistics.rest.dto.repair.workspace.repshop.RepshopProvisionWidgetResp
import com.x5.logistics.util.isAlmostZero
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.Period

@Service
class RepshopProvisionWidgetService(
    val repo: RepshopProvisionExposedRepo
) {

    @Value("\${x5.logistics.debug-values}")
    var debugValues: Boolean = false

    suspend fun getWidget(req: RepshopProvisionWidgetReq): RepshopProvisionWidgetResp =
        coroutineScope {
            val factProductionMap = async(Dispatchers.IO) { repo.getFactProduction(req) }
            val atpRequirement = async(Dispatchers.IO) { repo.getAtpRequirement(req.from, req.to, req.repshops) }
            val powerInfo = async(Dispatchers.IO) { getPowerInfo(req) }

            val totalFactProduction = factProductionMap.await().values.sum()
            val operationPerformance = powerInfo.await().sumOf { it.operationPower }
            val totalPower = powerInfo.await().sumOf { it.totalPower }
            val totalRequirements = atpRequirement.await()

            val provision = totalPower / totalRequirements
            val achievePlanDevel =
                totalFactProduction / listOf(totalRequirements, operationPerformance).filterNot { it == 0.0 }.min()
            val operationPower = if (totalRequirements == 0.0) 0.0 else operationPerformance / totalRequirements
            val factProduction = if (totalRequirements == 0.0) 0.0 else totalFactProduction / totalRequirements
            var result = RepshopProvisionWidgetResp(
                provision = provision,
                achievePlanDevel = achievePlanDevel,
                operationPower = operationPower,
                factProduction = factProduction,
                isEmpty = provision.isAlmostZero && achievePlanDevel.isAlmostZero && operationPower.isAlmostZero && factProduction.isAlmostZero
            )
            if (debugValues) result = result.copy(
                totalFactProduction = totalFactProduction,
                operationPerformance = operationPerformance,
                totalPower = totalPower,
                totalRequirements = totalRequirements
            )
            result
        }

    suspend fun getPowerInfo(req: RepshopProvisionWidgetReq): List<RepairPowerDto> {
        val properties = repo.getProperties(getMonths(req.from, req.to), req.repshops.map(Long::toInt))
        return properties.map { property ->
            val id = property.rsId
            val postPerformance = property.postPerformance * getDaysOfMonthInRangeDividedByMonthLength(
                req.from,
                req.to,
                LocalDate.of(property.year, property.month.ordinal + 1, 1)
            )
            RepairPowerDto(
                id = id,
                operationPower = (property.dayPosts + property.nightPosts) * postPerformance,
                totalPower = property.totalPosts * 2 * postPerformance
            )
        }
    }

    private suspend fun getDaysOfMonthInRangeDividedByMonthLength(
        from: LocalDate,
        to: LocalDate,
        month: LocalDate
    ): Double {
        val startOfMonth = month.withDayOfMonth(1)
        val endOfMonth = month.withDayOfMonth(month.lengthOfMonth())

        val intersectionStart = if (startOfMonth.isAfter(from)) startOfMonth else from
        val intersectionEnd = if (endOfMonth.isBefore(to)) endOfMonth else to

        val period = Period.between(intersectionStart, intersectionEnd.plusDays(1))

        val monthLength = month.lengthOfMonth()
        return (1.0 * period.months * monthLength + period.days) / monthLength
    }


    private suspend fun getMonths(from: LocalDate, to: LocalDate): List<Pair<Int, Month>> {
        val months = mutableListOf<Pair<Int, Month>>()

        var currentDate = from
        while (!currentDate.isAfter(to)) {
            val month = Month.entries[currentDate.monthValue - 1]
            val year = currentDate.year
            val pair = Pair(year, month)
            months.add(pair)
            currentDate = currentDate.plusMonths(1)
        }

        return months
    }
}