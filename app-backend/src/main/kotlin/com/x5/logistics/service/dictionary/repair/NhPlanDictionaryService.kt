package com.x5.logistics.service.dictionary.repair

import com.x5.logistics.repository.dictionary.repair.NhPlanDictionaryRepository
import com.x5.logistics.rest.dto.PagedResponse
import com.x5.logistics.rest.dto.dictionary.repair.NhDictionaryCheckNewDto
import com.x5.logistics.rest.dto.dictionary.repair.NhDictionaryImportStatus
import com.x5.logistics.rest.dto.dictionary.repair.NhDictionaryInsertRequest
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanChildrenDictionaryDto
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryColumn
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryDto
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryInsert
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryListRequest
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryUpdateRequest
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryValuesDto
import com.x5.logistics.rest.dto.dictionary.repair.NhVehicle
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.service.workbook
import com.x5.logistics.util.MAX_DATE
import com.x5.logistics.util.getLogger
import org.apache.poi.ss.usermodel.Font
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.time.LocalDate
import java.time.ZoneId
import java.util.*

private const val TOTAL_ROWS_TEXT = "Всего строк:"

@Service
class NhPlanDictionaryService(
    private val repo: NhPlanDictionaryRepository
) {
    private val log = getLogger()
    private val sheetName = "Справочник плановых НЧ"
    private lateinit var headerFont: Font
    private val orders = mapOf<NhPlanDictionaryColumn, Comparator<NhPlanDictionaryDto>>(
        NhPlanDictionaryColumn.vehicleId to compareBy { it.vehicleId },
        NhPlanDictionaryColumn.vehicleType to compareBy { it.vehicleType },
        NhPlanDictionaryColumn.vehicleBrand to compareBy { it.vehicleBrand },
        NhPlanDictionaryColumn.vehicleYear to compareBy { it.vehicleYear },
        NhPlanDictionaryColumn.vehicleTonnage to compareBy { it.vehicleTonnage },
        NhPlanDictionaryColumn.planNh to compareBy { it.planNh },
        NhPlanDictionaryColumn.startDate to compareBy { it.startDate },
        NhPlanDictionaryColumn.endDate to compareBy { it.endDate },
        NhPlanDictionaryColumn.author to compareBy { it.author },
    )

    fun getData(req: NhPlanDictionaryListRequest): PagedResponse<NhPlanDictionaryDto> {
        val today = LocalDate.now()
        val rowData = repo.getRawData(
            req.filters.filterNot { it.name == NhPlanDictionaryColumn.endDate },
            req.sort.filterNot { it.column == NhPlanDictionaryColumn.endDate }
        )
        log.debug(rowData.size.toString())
        val reqOrders = if (req.sort.isEmpty()) {
            compareBy<NhPlanDictionaryDto> { it.planNh }.reversed()
        } else {
            req.sort.map { order ->
                orders[order.column]!!.let { if (order.asc) it else it.reversed() }
            }.reduce { acc, comparator -> acc.then(comparator) }
        }
        val groups = rowData.groupBy { it.vehicleId }
        val result = groups.asSequence()
            .map { (_, plans) ->
                val currentPlan = plans
                    .filter { it.startDate != null && it.startDate <= today }
                    .maxByOrNull { it.startDate!! } ?: plans.first()
                val nextPlan = plans
                    .filter { it.startDate != null && it.startDate > currentPlan.startDate }
                    .minByOrNull { it.startDate!! }
                NhPlanDictionaryDto(
                    id = currentPlan.id,
                    vehicleId = currentPlan.vehicleId,
                    vehicleType = currentPlan.vehicleType,
                    vehicleBrand = currentPlan.vehicleBrand,
                    vehicleTonnage = currentPlan.vehicleTonnage,
                    vehicleYear = currentPlan.vehicleYear,
                    startDate = currentPlan.startDate,
                    endDate = nextPlan?.startDate?.minusDays(1) ?: MAX_DATE,
                    planNh = currentPlan.planNh,
                    author = currentPlan.author,
                    children = mapToChildrenList(plans)
                )
            }
            .sortedWith(reqOrders)
            .drop(req.pageNumber * req.pageSize)
            .take(req.pageSize)
            .toList()
        return PagedResponse(
            count = groups.size,
            pageNumber = req.pageNumber,
            pageSize = req.pageSize,
            items = result
        )
    }

    fun getDictionaryValues(): NhPlanDictionaryValuesDto = repo.getDictionary()

    fun checkNew(): NhDictionaryCheckNewDto = repo.checkNew()

    @Transactional
    fun update(req: NhPlanDictionaryUpdateRequest, author: String): List<NhPlanChildrenDictionaryDto> {
        val allPlans = repo.getAllByPlan(req.id)
        return when {
            allPlans.isEmpty() ->
                throw WrongRequestDataException("Группа ТС не найдена")

            req.planNh !in 0f..100f ->
                throw WrongRequestDataException("Значение План НЧ должно быть от 0 до 100")

            else -> {
                repo.update(req, author)
                mapToChildrenList(repo.getAllByPlan(req.id))
            }
        }
    }

    @Transactional
    fun new(req: NhDictionaryInsertRequest, author: String): List<NhPlanChildrenDictionaryDto> {
        val allPlans = repo.getAllByVehicleId(req.id)
        return when {
            allPlans.isEmpty() ->
                throw WrongRequestDataException("Группа ТС не найдена")

            req.planNh !in 0f..100f ->
                throw WrongRequestDataException("Значение План НЧ должно быть от 0 до 100")

            req.startDate in allPlans.map { it.startDate } ->
                throw WrongRequestDataException("НЧ для выбранной даты уже указаны.")

            else -> {
                repo.new(
                    NhPlanDictionaryInsert(
                        vehicleId = req.id,
                        startDate = req.startDate,
                        planNh = req.planNh,
                        author = author
                    )
                )
                mapToChildrenList(repo.getAllByVehicleId(req.id))
            }
        }
    }

    @Transactional
    fun delete(id: Long, author: String): List<NhPlanChildrenDictionaryDto> {
        repo.delete(id, author).also { if (it == 0) throw WrongRequestDataException("Запись не найдена") }
        return mapToChildrenList(repo.getAllByPlan(id))
    }

    private fun mapToChildrenList(
        plans: List<NhPlanDictionaryDto>
    ): List<NhPlanChildrenDictionaryDto> {
        val child = plans.map { child ->
            val next = plans
                .filter { it.startDate != null && it.startDate > child.startDate }
                .minByOrNull { it.startDate!! }
            NhPlanChildrenDictionaryDto(
                id = child.id,
                startDate = child.startDate,
                endDate = next?.startDate?.minusDays(1) ?: MAX_DATE,
                planNh = child.planNh,
                author = child.author
            )
        }.sortedByDescending { it.startDate }
        return child
    }

    private val columns = mapOf(
        NhPlanDictionaryColumn.vehicleType.columnTitle to 0,
        NhPlanDictionaryColumn.vehicleBrand.columnTitle to 1,
        NhPlanDictionaryColumn.vehicleTonnage.columnTitle to 2,
        NhPlanDictionaryColumn.vehicleYear.columnTitle to 3,
        NhPlanDictionaryColumn.planNh.columnTitle to 4,
        NhPlanDictionaryColumn.startDate.columnTitle to 5,
        NhPlanDictionaryColumn.endDate.columnTitle to 6,
        NhPlanDictionaryColumn.author.columnTitle to 7,
    )

    fun export(req: NhPlanDictionaryListRequest): ByteArray {
        val count = getData(req.copy(pageSize = 1)).count
        val data = getData(req.copy(pageNumber = 0, pageSize = count)).items
        return workbook {
            initFont(this.wb)
            style {
                alignment = HorizontalAlignment.CENTER
                verticalAlignment = VerticalAlignment.CENTER
            }

            val strStyle = style {
                dataFormat = wb.createDataFormat().getFormat("TEXT")
            }

            val intStyle = style {
                dataFormat = wb.createDataFormat().getFormat("#")
            }

            val floatStyle = style {
                dataFormat = wb.createDataFormat().getFormat("General")
            }

            val dateStyle = style {
                dataFormat = wb.createDataFormat().getFormat("YYYY-MM-DD")
            }

            sheet(sheetName) {
                header {
                    style {
                        setFont(headerFont)
                        alignment = HorizontalAlignment.CENTER
                        verticalAlignment = VerticalAlignment.CENTER
                    }
                    columns.forEach { head(it.key) }
                }
                data.forEach { item ->
                    row {
                        currStyle = strStyle
                        cell(item.vehicleType)
                        cell(item.vehicleBrand)
                        currStyle = floatStyle
                        cell(item.vehicleTonnage.toBigDecimal())
                        currStyle = intStyle
                        cell(item.vehicleYear)
                        currStyle = floatStyle
                        cell(item.planNh?.toBigDecimal())
                        currStyle = dateStyle
                        cell(item.startDate)
                        cell(item.endDate)
                        currStyle = strStyle
                        cell(item.author)
                    }
                }
                row {
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell(TOTAL_ROWS_TEXT)
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(count)
                }
            }.autosize()
        }.let {
            val stream = ByteArrayOutputStream()
            it.wb.write(stream)
            stream.toByteArray()
        }
    }

    fun import(body: InputStream, author: String): NhDictionaryImportStatus {
        val workbook = try {
            XSSFWorkbook(body)
        } catch (e: IllegalArgumentException) {
            throw WrongRequestDataException("Не удалось загрузить. Root cause: '$e'")
        }
        val sheet = workbook.getSheet(sheetName)
            ?: throw WrongRequestDataException("Лист '$sheetName' не найден")
        val headerStyle = workbook.createCellStyle().apply {
            setFont(
                workbook.createFont().apply {
                    bold = true
                }
            )
        }

        val headRow = sheet.getRow(0)
        val statusColumn = 8
        headRow.rowStyle = headerStyle
        headRow.createCell(statusColumn).apply {
            cellStyle = headerStyle
            setCellValue("Результат")
        }
        headRow
            .take(8)
            .forEach { cell ->
                if (columns[cell.stringCellValue] != cell.columnIndex) {
                    throw WrongRequestDataException("Структура файла не соответствует ожидаемой.")
                }
            }

        var totalRows = sheet.lastRowNum
        var errorsCount = 0
        val errors = mutableSetOf<String>()
        sheet.asSequence()
            .drop(1)
            .forEach { row ->
                try {
                    val vehicleType = row.getCell(0)?.stringCellValue ?: ""
                    if (vehicleType == TOTAL_ROWS_TEXT) {
                        totalRows--
                        return@forEach
                    }
                    val brand = row.getCell(1)?.stringCellValue ?: ""
                    val tonnage = row.getCell(2)?.numericCellValue?.toFloat() ?: -1f
                    val year = row.getCell(3)?.numericCellValue?.toInt() ?: -1
                    val planNh = row.getCell(4)?.numericCellValue?.toFloat() ?: -1f
                    val startDate = row.getCell(5)?.dateCellValue
                        ?.toInstant()
                        ?.atZone(ZoneId.systemDefault())
                        ?.toLocalDate()
                    val vehicle = repo.getVehiclesByDescription(
                        NhVehicle(
                            id = null,
                            type = vehicleType,
                            brand = brand,
                            tonnage = tonnage,
                            createYear = year,
                        )
                    ).firstOrNull()
                    log.debug("process vehicle {}, {}, {}", vehicle?.id, planNh, startDate)

                    when {
                        vehicleType.isBlank() || brand.isBlank() || tonnage < 0 || year < 0 || startDate == null ->
                            row.createCell(statusColumn)
                                .setCellValue("Ошибка в заполнении данных, проверьте формат данных")
                                .also {
                                    errorsCount++
                                    errors.add("Ошибка импорта данных. Формат файла не соответствует справочнику")
                                }

                        vehicle == null ->
                            row.createCell(statusColumn)
                                .setCellValue("Группа ТС не найдена")
                                .also {
                                    errorsCount++
                                    errors.add(
                                        "Ошибка импорта данных. " +
                                            "Вводится плановые НЧ для ТС, классификации которых нет в системе"
                                    )
                                }

                        planNh !in 0f..100f ->
                            row.createCell(statusColumn)
                                .setCellValue("Значение План НЧ должно быть от 0 до 100")
                                .also {
                                    errorsCount++
                                    errors.add("Ошибка импорта данных. Вводится некорректное значение плановых НЧ")
                                }

                        repo.getAllByVehicleId(vehicle.id!!).any { it.startDate == startDate } ->
                            row.createCell(statusColumn)
                                .setCellValue(
                                    "НЧ для выбранной даты уже указаны. Редактирование записи доступно на портале."
                                )
                                .also {
                                    errorsCount++
                                    errors.add(
                                        "Ошибка импорта данных. " +
                                            "Вводится плановые НЧ для даты начала, для которой уже есть данные"
                                    )
                                }

                        else -> {
                            repo.new(
                                NhPlanDictionaryInsert(
                                    vehicleId = vehicle.id,
                                    startDate = startDate,
                                    planNh = planNh,
                                    author = author,
                                )
                            )
                            row.getCell(7)
                                .setCellValue(author)
                            row.createCell(statusColumn)
                                .setCellValue("Успешно добавлено")
                        }
                    }
                } catch (e: IllegalStateException) {
                    row.createCell(statusColumn)
                        .setCellValue("Ошибка в заполнении данных, проверьте формат данных")
                    log.debug(e.message)
                    errorsCount++
                    errors.add("Ошибка импорта данных. Формат файла не соответствует справочнику")
                }
            }

        sheet.autoSizeColumn(statusColumn)
        return NhDictionaryImportStatus(
            totalRowsCount = totalRows,
            errorsCount = errorsCount,
            errors = errors,
            body = Base64.getEncoder().encodeToString(
                ByteArrayOutputStream().also { workbook.write(it) }.toByteArray()
            )
        )
    }

    private fun initFont(wb: Workbook) {
        headerFont = wb.createFont()
        headerFont.bold = true
    }
}
