package com.x5.logistics.service.charting

import com.x5.logistics.repository.charting.ChartFilterExposedRepo
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.charting.ChartColumn
import com.x5.logistics.rest.dto.charting.ChartColumnType
import com.x5.logistics.rest.dto.charting.ChartDataWidgetRequest
import com.x5.logistics.rest.dto.charting.ChartFilterDto
import com.x5.logistics.rest.dto.charting.ChartFilterValuesRequest
import com.x5.logistics.rest.dto.charting.ChartGroup
import com.x5.logistics.rest.dto.charting.ChartRow
import com.x5.logistics.rest.dto.charting.ChartTotalType
import com.x5.logistics.rest.dto.charting.FAKE_NAME
import com.x5.logistics.service.HeaderBuilder
import com.x5.logistics.service.WbBuilder
import com.x5.logistics.service.workbook
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.FillPatternType
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream
import kotlin.math.max

@Service
class ChartingService(
    val filterRepo: ChartFilterExposedRepo,
    val chartDataService: ChartDataService,
    val groupingsService: ChartGroupingsDictionaryService
) {
    fun getFilters(): List<ChartFilterDto> =
        ChartColumn.entries.filterNot { it.calculated || !it.shownInFilterList }.map { chartColumn ->
            ChartFilterDto(
                name = chartColumn.columnTitle,
                label = chartColumn,
                group = "string",
                restrictions = groupingsService.chartGroupings.first { it.sysName == chartColumn }.restrictions
            )
        }

    fun getFilterValues(req: ChartFilterValuesRequest): List<LabeledValue> {
        return filterRepo.getFilterValues(req)
    }

    fun exportToExcel(req: ChartDataWidgetRequest): ByteArray {
        val data = chartDataService.getChartData(req)
        val headers = data.columns
        val fullHeaders = getFullHeaders(headers)
        val rows = data.rows.map { it.ifEmpty { listOf(ChartRow("")) } }
        val values = data.values

        return workbook {
            val headerStyle = style {
                alignment = HorizontalAlignment.CENTER
                verticalAlignment = VerticalAlignment.CENTER
                setFont(
                    wb.createFont().apply {
                        bold = true
                    }
                )
            }
            val commonStyle = style {
                setFont(
                    wb.createFont().apply {
                        bold = false
                    }
                )
            }
            sheet("Данные") {
                style {
                    alignment = HorizontalAlignment.CENTER
                    verticalAlignment = VerticalAlignment.CENTER
                }
                if (headers.first().name != FAKE_NAME) {
                    header {
                        currStyle = headerStyle
                        repeat(max(rows.first().size, 1)) {
                            head("")
                        }
                        createHeaders(this, headers)
                    }
                }
                rows.forEachIndexed { index, rowTitles ->
                    row {
                        currStyle = getStyle(this@workbook, null, rowTitles, StyleType.ROW_TITLE)
                        rowTitles.forEach {
                            cell(it.name)
                        }
                        currStyle = commonStyle
                        fullHeaders.forEach { column ->
                            values[index].getOrDefault(column.label, null)?.let {
                                val styleType = when {
                                    column.isPercentageType -> StyleType.PERCENT_DATA
                                    else -> StyleType.COMON_DATA
                                }
                                currStyle = getStyle(this@workbook, column, rowTitles, styleType)
                                cell(it.first())
                            } ?: cell(0.0)
                        }
                    }
                }
            }.autosize()
        }.let {
            val stream = ByteArrayOutputStream()
            it.wb.write(stream)
            stream.toByteArray()
        }
    }

    private fun getStyle(
        wbBuilder: WbBuilder,
        column: ExcelHeader?,
        rowTitles: List<ChartRow>,
        styleType: StyleType = StyleType.COMON_DATA
    ): CellStyle {
        val wb = wbBuilder.wb
        return wbBuilder.style {
            when (styleType) {
                StyleType.COLUMN_TITLE -> {
                    alignment = HorizontalAlignment.CENTER
                    verticalAlignment = VerticalAlignment.CENTER
                    setFont(
                        wb.createFont().apply {
                            bold = true
                        }
                    )
                }

                StyleType.ROW_TITLE -> {
                    alignment = HorizontalAlignment.LEFT
                    verticalAlignment = VerticalAlignment.CENTER
                    wrapText = true
                    setFont(
                        wb.createFont().apply {
                            bold = true
                        }
                    )
                }

                StyleType.COMON_DATA -> {
                    setFont(
                        wb.createFont().apply {
                            bold = false
                        }
                    )
                }

                StyleType.PERCENT_DATA -> {
                    dataFormat = wb.createDataFormat().getFormat("0.00%")
                }
            }
            when {
                column != null && column.isTotal -> {
                    fillForegroundColor = IndexedColors.LIGHT_GREEN.index
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                }

                column != null && column.isSubTotal && rowTitles.any { it.total != ChartTotalType.total } -> {
                    fillForegroundColor = IndexedColors.GREY_25_PERCENT.index
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                }

                else -> {
                    when {
                        rowTitles.any { it.total == ChartTotalType.total } -> {
                            fillForegroundColor = IndexedColors.LIGHT_GREEN.index
                            fillPattern = FillPatternType.SOLID_FOREGROUND
                        }

                        rowTitles.any { it.total == ChartTotalType.subtotal } -> {
                            fillForegroundColor = IndexedColors.GREY_25_PERCENT.index
                            fillPattern = FillPatternType.SOLID_FOREGROUND
                        }

                        else -> {
                            fillPattern = FillPatternType.NO_FILL
                        }
                    }
                }
            }
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun createHeaders(hb: HeaderBuilder, headers: List<ChartGroup>) {
        headers.forEach { chartGroup ->
            if (chartGroup.children is List<*>) {
                val children = chartGroup.children as List<ChartGroup>
                hb.head(chartGroup.name) {
                    setHeaderStyle(hb, chartGroup.total)
                    createHeaders(this, children)
                }
            } else {
                setHeaderStyle(hb, chartGroup.total)
                hb.head(chartGroup.name)
            }
        }
    }

    private fun setHeaderStyle(hb: HeaderBuilder, totalType: ChartTotalType?) {
        hb.style {
            val wb = hb.sheet.workbook
            alignment = HorizontalAlignment.CENTER
            verticalAlignment = VerticalAlignment.CENTER
            setFont(
                wb.createFont().apply {
                    bold = true
                }
            )
            when (totalType) {
                ChartTotalType.total -> {
                    fillForegroundColor = IndexedColors.LIGHT_GREEN.index
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                }

                ChartTotalType.subtotal -> {
                    fillForegroundColor = IndexedColors.GREY_25_PERCENT.index
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                }

                null -> {
                    fillPattern = FillPatternType.NO_FILL
                }
            }
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun getFullHeaders(headers: List<Any>): List<ExcelHeader> {
        val fullHeaders = mutableListOf<ExcelHeader>()
        headers.forEach {
            val header = it as ChartGroup
            when (header.children) {
                is String -> fullHeaders.add(
                    ExcelHeader(
                        label = header.children as String,
                        isPercentageType = header.type == ChartColumnType.share,
                        isSubTotal = header.total == ChartTotalType.subtotal,
                        isTotal = header.total == ChartTotalType.total,
                    )
                )

                is List<*> -> fullHeaders += getFullHeaders(header.children as List<Any>)
            }
        }
        return fullHeaders
    }
}

data class ExcelHeader(
    val label: String,
    val isPercentageType: Boolean,
    val isSubTotal: Boolean,
    val isTotal: Boolean,
)

enum class StyleType {
    COLUMN_TITLE,
    ROW_TITLE,
    COMON_DATA,
    PERCENT_DATA
}
