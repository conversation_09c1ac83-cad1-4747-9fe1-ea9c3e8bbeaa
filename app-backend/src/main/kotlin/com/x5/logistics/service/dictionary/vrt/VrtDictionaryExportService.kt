package com.x5.logistics.service.dictionary.vrt

import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryExportReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtLevel
import com.x5.logistics.rest.dto.dictionary.vrt.VrtSubtypeDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.dataRequest
import com.x5.logistics.service.workbook
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream

private const val TORO_WORKS_TYPES_SHEET_NAME = "Распределение видов ВРТ"
private const val TORO_WORKS_SUBTYPES_SHEET_NAME = "Распределение подвидов ВРТ"

@Service
class VrtDictionaryExportService(
    private val service: VrtDictionaryService
) {
    fun exportToroWorks(req: VrtDictionaryExportReq): ByteArray {
        val dataRequest = req.dataRequest
        dataRequest.setDefaultSort(isSubtypeDictionary = when (req.vrtLevel) {
            VrtLevel.TYPES -> false
            VrtLevel.SUBTYPES -> true
        })
        val count = service.countVrtDictionaryGroupList(dataRequest)
        val data = service.getVrtDictionaryGroupList(dataRequest.copy(pageSize = count, pageNumber = 0))
        val sheetName = when (req.vrtLevel) {
            VrtLevel.TYPES -> TORO_WORKS_TYPES_SHEET_NAME
            VrtLevel.SUBTYPES -> TORO_WORKS_SUBTYPES_SHEET_NAME
        }
        return workbook {
            val headerStyle = style { setFont(wb.createFont().apply { bold = true }) }
            val strStyle = style { dataFormat = wb.createDataFormat().getFormat("TEXT") }

            sheet(sheetName) {
                header {
                    currStyle = headerStyle
                    head("Код ВРТ")
                    head("Название ВРТ")
                    head(when (req.vrtLevel) {
                        VrtLevel.TYPES -> "Вид ВРТ"
                        VrtLevel.SUBTYPES -> "Подвид ВРТ"
                    })
                    if (req.vrtLevel == VrtLevel.SUBTYPES) {
                        head("% работ на РЗ")
                    }
                }
                data.forEach { item ->
                    row {
                        currStyle = strStyle
                        cell(item.id)
                        cell(item.name)
                        cell(when (req.vrtLevel) {
                            VrtLevel.TYPES -> item.type?.name
                            VrtLevel.SUBTYPES -> item.subtype?.name
                        })
                        if (req.vrtLevel == VrtLevel.SUBTYPES) {
                            cell(item.useNh?.let {
                                when (it) {
                                    true -> "Участвует"
                                    false -> "Исключено"
                                }
                            })
                        }
                    }
                }
                row {
                    cell("Всего строк")
                    cell(count)
                }
            }.autosize()
        }.let {
            val stream = ByteArrayOutputStream()
            it.wb.write(stream)
            stream.toByteArray()
        }
    }

    fun exportTypes(req: VrtTypeDictionaryListReq): ByteArray {
        val count = service.countVrtTypeDictionaryItems(req)
        val data = service.getVrtTypeDictionaryItems(req.copy(pageSize = count, pageNumber = 0))
        return workbook {
            val headerStyle = style { setFont(wb.createFont().apply { bold = true }) }
            val strStyle = style { dataFormat = wb.createDataFormat().getFormat("TEXT") }

            sheet("Виды ВРТ") {
                header {
                    currStyle = headerStyle
                    head("Вид ВРТ")
                    head("Сотрудник")
                }
                data.forEach { item ->
                    row {
                        currStyle = strStyle
                        cell(item.name)
                        cell(item.updatedBy ?: item.createdBy)
                    }
                }
                row {
                    cell("Всего строк")
                    cell(count)
                }
            }.autosize()
        }.let {
            val stream = ByteArrayOutputStream()
            it.wb.write(stream)
            stream.toByteArray()
        }
    }

    fun exportSubtypes(req: VrtSubtypeDictionaryListReq): ByteArray {
        val count = service.countVrtSubtypeDictionaryItems(req)
        val data = service.getVrtSubtypeDictionaryItems(req.copy(pageSize = count, pageNumber = 0))
        return workbook {
            val headerStyle = style { setFont(wb.createFont().apply { bold = true }) }
            val strStyle = style { dataFormat = wb.createDataFormat().getFormat("TEXT") }

            sheet("Подвиды ВРТ") {
                header {
                    currStyle = headerStyle
                    head("Подвид ВРТ")
                    head("Сотрудник")
                }
                data.forEach { item ->
                    row {
                        currStyle = strStyle
                        cell(item.name)
                        cell(item.updatedBy ?: item.createdBy)
                    }
                }
                row {
                    cell("Всего строк")
                    cell(count)
                }
            }.autosize()
        }.let {
            val stream = ByteArrayOutputStream()
            it.wb.write(stream)
            stream.toByteArray()
        }
    }
}
