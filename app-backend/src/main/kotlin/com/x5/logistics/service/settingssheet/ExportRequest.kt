package com.x5.logistics.service.settingssheet

import com.x5.logistics.rest.dto.GeoFilter
import java.time.LocalDate

data class ExportRequest(
    val from: LocalDate,
    val to: LocalDate,
    val granularitySupported: Boolean = false,
    val granularityValue: String? = null,
    val userName: String?,
    val geoFilter: GeoFilter? = null,
    val columns: List<String>?,
    val filters: List<Filter>?,
    val sort: List<SortOrder>?,
    val isRepairPlace: Boolean = false
)