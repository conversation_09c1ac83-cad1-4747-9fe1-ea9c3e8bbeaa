package com.x5.logistics.service.charting

import com.x5.logistics.data.charting.ChartCompositeAggregateTable
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateCreateRequest
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateDto
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateRequest
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateUpdateRequest
import com.x5.logistics.rest.exception.WrongRequestDataException
import org.jetbrains.exposed.sql.NotOp
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insertAndGetId
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
class ChartCompositeAggregatesService {
    fun getUserAggregates(user: String): List<ChartCompositeAggregateDto> = transaction {
        val usedAggregates = mutableListOf<Long>()
        exec(
            "select jsonb_array_elements((settings::jsonb->>'valuesCombine')::jsonb) ids\n" +
                    "from chart_widget;"
        ) {
            while (it.next()) {
                usedAggregates.add(it.getLong("ids"))
            }
        }
        ChartCompositeAggregateTable
            .selectAll()
            .where {
                ChartCompositeAggregateTable.createdBy eq user and
                        (NotOp(ChartCompositeAggregateTable.deleted) or
                                (ChartCompositeAggregateTable.id.inList(usedAggregates)))
            }.map(::toDto)
    }

    fun create(user: String, req: ChartCompositeAggregateCreateRequest): Long = transaction {
        validate(req)
        ChartCompositeAggregateTable.insertAndGetId {
            it[name] = req.name
            it[settings] = req.settings
            it[deleted] = false
            it[createdBy] = user
            it[createdAt] = LocalDateTime.now()
        }.value
    }

    fun edit(user: String, req: ChartCompositeAggregateUpdateRequest): Unit = transaction {
        validate(req)
        ChartCompositeAggregateTable
            .selectAll()
            .where {
                ChartCompositeAggregateTable.id eq req.id and (ChartCompositeAggregateTable.createdBy eq user)
            }
            .map(::toDto)
            .ifEmpty {
                throw WrongRequestDataException("not found aggregate with id ${req.id} for user $user")
            }
        ChartCompositeAggregateTable.update({ ChartCompositeAggregateTable.id eq req.id }) {
            it[name] = req.name
            it[settings] = req.settings
        }
    }

    fun delete(user: String, id: Long): Unit = transaction {
        ChartCompositeAggregateTable
            .selectAll()
            .where {
                ChartCompositeAggregateTable.id eq id and (ChartCompositeAggregateTable.createdBy eq user)
            }
            .map(::toDto)
            .ifEmpty {
                throw WrongRequestDataException("not found aggregate with id $id for user $user")
            }
        ChartCompositeAggregateTable.update({ ChartCompositeAggregateTable.id eq id }) {
            it[deleted] = true
        }
    }

    private fun validate(req: ChartCompositeAggregateRequest) {
        if (req.name.length > 50) {
            throw WrongRequestDataException("name too long (should not exceed 50)")
        }
        if (req.settings.expressions.isBlank() || req.settings.parameters.isEmpty()) {
            throw WrongRequestDataException("settings or parameters are empty")
        }
    }

    private fun toDto(row: ResultRow): ChartCompositeAggregateDto =
        ChartCompositeAggregateDto(
            id = row[ChartCompositeAggregateTable.id].value,
            name = row[ChartCompositeAggregateTable.name],
            settings = row[ChartCompositeAggregateTable.settings],
            deleted = row[ChartCompositeAggregateTable.deleted],
            createdBy = row[ChartCompositeAggregateTable.createdBy],
            createdAt = row[ChartCompositeAggregateTable.createdAt]
        )
}