package com.x5.logistics.service.airflowmonitoring

import com.x5.logistics.repository.airflowmonitoring.DataStatusRepo
import com.x5.logistics.rest.dto.airflowmonitoring.DataStatusDto
import com.x5.logistics.util.getLogger
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@Service
class DataStatusService(
    val repo: DataStatusRepo
) {
    val logger = getLogger()
    val dateTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")

    fun getDataStatus(testTime: LocalDateTime? = null): DataStatusDto {
        val currentDateTime = testTime ?: LocalDateTime.now(ZoneId.of("Europe/Moscow"))
        val currentDate: LocalDate = currentDateTime.toLocalDate()
        val currentTime: LocalTime = currentDateTime.toLocalTime()

        logger.debug("Get airflow update status")

        val lastUpdateDate: LocalDate = repo.getLastUpdateDate()

        return when {
            //Дата начала последнего обновления = текущая дата
            lastUpdateDate == currentDate ->
                DataStatusDto(
                    date = currentDateTime,
                    currentStatus = """
                        Данные актуальны
                        по ${currentDate.minusDays(1).format(dateTimeFormatter)} 23:59 МСК
                    """.trimIndent(),
                    nextUpdateStatus = """
                        Обновлённые данные поступят 
                        ${currentDate.plusDays(1).format(dateTimeFormatter)} до 10:00 МСК
                        и будут актуальны
                        по ${currentDate.format(dateTimeFormatter)} 23:59 МСК
                    """.trimIndent()
                )
            //Дата начала последнего обновления != Текущая дата
            //И Текущее время по МСК между 0:00 и 10:00 МСК
            lastUpdateDate != currentDate && currentTime.isBefore(LocalTime.of(10, 0)) ->
                DataStatusDto(
                    date = currentDateTime,
                    currentStatus = """
                        Данные актуальны
                        по ${lastUpdateDate.minusDays(1).format(dateTimeFormatter)} 23:59 МСК
                    """.trimIndent(),
                    nextUpdateStatus = """
                        Обновлённые данные поступят
                        ${currentDate.format(dateTimeFormatter)} до 10:00 МСК
                        и будут актуальны
                        по ${currentDate.minusDays(1).format(dateTimeFormatter)} 23:59 МСК
                    """.trimIndent()
                )

            else ->
                //Дата начала последнего обновления != Текущая дата
                //И Текущее время по МСК между 10:01 и 23:59 МСК
                DataStatusDto(
                    date = currentDateTime,
                    currentStatus = """
                        Данные актуальны
                        по ${lastUpdateDate.minusDays(1).format(dateTimeFormatter)} 23:59 МСК
                    """.trimIndent(),
                    nextUpdateStatus = """
                        Обновлённые данные поступят
                        в ближайшее время
                        и будут актуальны
                        по ${currentDate.minusDays(1).format(dateTimeFormatter)} 23:59 МСК
                    """.trimIndent()
                )
        }
    }
}
