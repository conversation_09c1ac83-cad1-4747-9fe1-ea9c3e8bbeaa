package com.x5.logistics.service.repair

import com.x5.logistics.repository.repair.RepairModalWidgetRepo
import com.x5.logistics.rest.dto.repair.modal.RepairItem
import com.x5.logistics.rest.dto.repair.modal.RepairModalWidgetReq
import com.x5.logistics.rest.dto.repair.modal.RepairModalWidgetResp
import com.x5.logistics.rest.dto.repair.modal.RepairType
import com.x5.logistics.rest.dto.repair.modal.TypeRepairItem
import com.x5.logistics.rest.dto.repair.modal.VehicleRepairItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.springframework.stereotype.Service

@Service
class RepairModalWidgetService(
    private val repo: RepairModalWidgetRepo,
) {
    suspend fun getData(req: RepairModalWidgetReq): RepairModalWidgetResp = coroutineScope {
        val defVehicleMainExp = async(Dispatchers.IO) { repo.getMainExpenses(req) }
        val defVehicleSubExp = async(Dispatchers.IO) { repo.getSubExpenses(req) }
        val defVehicleMainMileage = async(Dispatchers.IO) { repo.getMainMileage(req) }
        val defVehicleSubMileage = async(Dispatchers.IO) { repo.getSubMileage(req) }
        val defTotalPlan = async(Dispatchers.IO) { repo.getTotalTariffs(req) }
        val defTotalMainPlan = async(Dispatchers.IO) { repo.getTotalMainTariffs(req) }
        val defTotalSubPlan = async(Dispatchers.IO) { repo.getTotalSubTariffs(req) }
        val defVehicleMainPlan = async(Dispatchers.IO) { repo.getMainTariffs(req) }
        val defVehicleSubPlan = async(Dispatchers.IO) { repo.getSubTariffs(req) }
        val defRepType = async(Dispatchers.IO) {
            when (req.repairType) {
                RepairType.TOTAL -> repo.getTypeTotal(req)
                RepairType.TIRES -> repo.getTypeTires(req)
                RepairType.PARTS,
                RepairType.OUR_WORKSHOP_SERVICES,
                RepairType.EXT_WORKSHOP_SERVICES -> repo.getOtherTypes(req, req.repairType)
            }
        }

        val vehicleMainExp = defVehicleMainExp.await()
        val vehicleSubExp = defVehicleSubExp.await()
        val vehicleMainMileage = defVehicleMainMileage.await()
        val vehicleSubMileage = defVehicleSubMileage.await()
        val totalPlan = defTotalPlan.await()
        val totalMainPlan = defTotalMainPlan.await()
        val totalSubPlan = defTotalSubPlan.await()
        val vehicleMainPlan = defVehicleMainPlan.await()
        val vehicleSubPlan = defVehicleSubPlan.await()
        var repType = defRepType.await()
        if (repType.size >= 5) {
            val types = repType.take(5)
            val remaining = repType.drop(5)
            repType = types + Pair("Остальные", remaining.sumOf { it.second })
        }

        var mainExpenses = 0.0
        var mainOrders = 0L
        var mainVehicles = 0L
        var mainMileage = 0.0

        var subExpenses = 0.0
        var subOrders = 0L
        var subVehicles = 0L
        var subMileage = 0.0

        var totalExpenses = 0.0
        var totalOrders = 0L
        var totalVehicles = 0L
        var totalMileage = 0.0

        val main = vehicleMainExp.map {
            val mileage = vehicleMainMileage[it.key] ?: 0.0

            mainExpenses += it.value.expenses ?: 0.0
            mainOrders += it.value.orders ?: 0L
            mainVehicles += it.value.vehicles ?: 0L
            mainMileage += mileage

            totalExpenses += it.value.expenses ?: 0.0
            totalOrders += it.value.orders ?: 0L
            totalVehicles += it.value.vehicles ?: 0L
            totalMileage += mileage

            VehicleRepairItem(
                brand = it.key.marka ?: "-",
                tonnage = it.key.tonnage?.toFloat() ?: 0.0f,
                repairSum = it.value.expenses ?: 0.0,
                repairRubKmFact = (it.value.expenses ?: 0.0) / if (mileage == 0.0) 1.0 else mileage,
                repairRubKmPlan = (vehicleMainPlan[it.key] ?: 0.0) / if (mileage == 0.0) 1.0 else mileage,
                countExpensiveSum = it.value.orders?.toDouble() ?: 0.0,
                countVehicleRepair = it.value.vehicles?.toDouble() ?: 0.0
            )
        }

        val sub = vehicleSubExp.map {
            val mileage = vehicleSubMileage[it.key] ?: 0.0

            subExpenses += it.value.expenses ?: 0.0
            subOrders += it.value.orders ?: 0L
            subVehicles += it.value.vehicles ?: 0L
            subMileage += mileage

            totalExpenses += it.value.expenses ?: 0.0
            totalOrders += it.value.orders ?: 0L
            totalVehicles += it.value.vehicles ?: 0L
            totalMileage += mileage

            VehicleRepairItem(
                brand = it.key.marka ?: "-",
                tonnage = it.key.tonnage?.toFloat() ?: 0.0f,
                repairSum = it.value.expenses ?: 0.0,
                repairRubKmFact = (it.value.expenses ?: 0.0) / if (mileage == 0.0) 1.0 else mileage,
                repairRubKmPlan = vehicleSubPlan[it.key] ?: 0.0,
                countExpensiveSum = it.value.orders?.toDouble() ?: 0.0,
                countVehicleRepair = it.value.vehicles?.toDouble() ?: 0.0
            )
        }

        val totals = RepairItem(
            repairSum = totalExpenses,
            repairRubKmFact = totalExpenses / if (totalMileage == 0.0) 1.0 else totalMileage,
            repairRubKmPlan = totalPlan / if (totalMileage == 0.0) 1.0 else totalMileage,
            countExpensiveSum = totalOrders.toDouble(),
            countVehicleRepair = totalVehicles.toDouble(),
        )

        val totalsMain = RepairItem(
            repairSum = mainExpenses,
            repairRubKmFact = mainExpenses / if (mainMileage == 0.0) 1.0 else mainMileage,
            repairRubKmPlan = totalMainPlan / if (mainMileage == 0.0) 1.0 else mainMileage,
            countExpensiveSum = mainOrders.toDouble(),
            countVehicleRepair = mainVehicles.toDouble(),
        )

        val totalsSub = RepairItem(
            repairSum = subExpenses,
            repairRubKmFact = subExpenses / if (subMileage == 0.0) 1.0 else subMileage,
            repairRubKmPlan = totalSubPlan / if (subMileage == 0.0) 1.0 else subMileage,
            countExpensiveSum = subOrders.toDouble(),
            countVehicleRepair = subVehicles.toDouble(),
        )

        RepairModalWidgetResp(
            totalRepairInfo = totals,
            totalRepairInfoGroupMain = totalsMain,
            totalRepairInfoGroupSub = totalsSub,
            vehicleGroupMain = main,
            vehicleGroupSub = sub,
            typeRepair = repType.map { 
                TypeRepairItem(
                    groupName = it.first,
                    repairSum = it.second,
                    percent = it.second / totalExpenses
                )
            }
        )
    }
}