package com.x5.logistics.service

import com.x5.logistics.data.OperationalReport
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.repository.Extract
import com.x5.logistics.repository.JsonAgg
import com.x5.logistics.repository.StringDistinct
import com.x5.logistics.repository.aliasOnlyExpression
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.div
import com.x5.logistics.repository.mapper
import com.x5.logistics.repository.repair.OperReportExposedRepo
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.operreport.OperReportColumn
import com.x5.logistics.rest.dto.operreport.OperReportColumnFilter
import com.x5.logistics.rest.dto.operreport.OperReportColumnItem
import com.x5.logistics.rest.dto.operreport.OperReportFilterReq
import com.x5.logistics.rest.dto.operreport.OperReportMassFilterReq
import com.x5.logistics.rest.dto.operreport.OperReportRequest
import com.x5.logistics.rest.dto.operreport.OperReportResponse
import com.x5.logistics.rest.dto.operreport.OperReportResponseItem
import com.x5.logistics.rest.exception.ExportTimeoutException
import com.x5.logistics.service.settingssheet.ExportRequest
import com.x5.logistics.service.settingssheet.Filter
import com.x5.logistics.service.settingssheet.ReportName
import com.x5.logistics.service.settingssheet.SettingsSheetService
import com.x5.logistics.util.DateFormatUtils.formatCalendarWeek
import com.x5.logistics.util.DateFormatUtils.formatMonth
import com.x5.logistics.util.DateFormatUtils.formatQuarter
import com.x5.logistics.util.DateFormatUtils.formatReportWeek
import com.x5.logistics.util.DateFormatUtils.formatYear
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import com.x5.logistics.util.EXPORT_TIMEOUT
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.checkFilterValues
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.moscowDateTime
import kotlinx.coroutines.runBlocking
import org.apache.poi.hssf.util.HSSFColor
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.hibernate.query.sqm.tree.SqmNode.log
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.addLogger
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.castTo
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import java.net.URLEncoder
import kotlin.time.Duration


@Service
class OperReportService(
    val repo: OperReportExposedRepo,
    val settingsSheetService: SettingsSheetService
) {

    val logger = getLogger()

    suspend fun getOperReportColumns(): List<OperReportColumnItem> =
        OperReportColumn.entries
            .map { column ->
            OperReportColumnItem(
                name = column.title,
                label = column.name,
                group = column.group,
                filterable = column.filterable
            )
        }

    suspend fun getOperReport(req: OperReportRequest): OperReportResponse = newSuspendedTransaction {
        addLogger(StdOutSqlLogger)
        val rep = repo.prepareBaseQuery(req)
        logger.info(rep.prepareSQL(QueryBuilder(false)))
        var totalCount: Long? = null
        val items = with(OperationalReport) {
            rep.map { row ->
                if (totalCount == null) totalCount = row[count]

                OperReportResponseItem(
                    periodDay = if (req.columns.contains(OperReportColumn.periodDay)) {
                        row[vehicleDate.aliasOnlyExpression()]?.toString() ?: "-"
                    } else null,
                    periodReportWeek = if (req.columns.contains(OperReportColumn.periodReportWeek)) {
                        row[reportingWeek.aliasOnlyExpression()]?.let { formatReportWeek(it) } ?: "-"
                    } else null,
                    periodCalendarWeek = if (req.columns.contains(OperReportColumn.periodCalendarWeek)) {
                        row[week.aliasOnlyExpression()]?.let { formatCalendarWeek(it) } ?: "-"
                    } else null,
                    periodMonth = if (req.columns.contains(OperReportColumn.periodMonth)) {
                        row[month.aliasOnlyExpression()]?.let { formatMonth(it) } ?: "-"
                    } else null,
                    periodQuarter = if (req.columns.contains(OperReportColumn.periodQuarter)) {
                        row[quarter.aliasOnlyExpression()]?.let { formatQuarter(it) } ?: "-"
                    } else null,
                    periodYear = if (req.columns.contains(OperReportColumn.periodYear)) {
                        row[year.aliasOnlyExpression()]?.let { formatYear(it) } ?: "-"
                    } else null,
                    vehicleLicense = if (req.columns.contains(OperReportColumn.vehicleLicense)) {
                        row[vehicleLicense.aliasOnlyExpression()] ?: "-"
                    } else null,
                    vehicleGroup = if (req.columns.contains(OperReportColumn.vehicleGroup)) {
                        row[vehicleGroup.aliasOnlyExpression()] ?: "-"
                    } else null,
                    vehicleType = if (req.columns.contains(OperReportColumn.vehicleType)) {
                        row[vehicleType.aliasOnlyExpression()] ?: "-"
                    } else null,
                    vehicleBrand = if (req.columns.contains(OperReportColumn.vehicleBrand)) {
                        row[vehicleBrand.aliasOnlyExpression()] ?: "-"
                    } else null,
                    vehicleModel = if (req.columns.contains(OperReportColumn.vehicleModel)) {
                        row[vehicleModel.aliasOnlyExpression()] ?: "-"
                    } else null,
                    vehicleTonnage = if (req.columns.contains(OperReportColumn.vehicleTonnage)) {
                        row[vehicleTonnage.aliasOnlyExpression()]
                    } else null,
                    vehicleCreateYear = if (req.columns.contains(OperReportColumn.vehicleCreateYear)) {
                        row[vehicleCreateYear.aliasOnlyExpression()]
                    } else null,
                    mvz = if (req.columns.contains(OperReportColumn.mvz)) {
                        row[OrganizationalUnitsTimelineTable.mvzId.aliasOnlyExpression()] ?: "-"
                    } else null,
                    mvzName = if (req.columns.contains(OperReportColumn.mvzName)) {
                        row[OrganizationalUnitsTimelineTable.mvzName.aliasOnlyExpression()] ?: "-"
                    } else null,
                    retailNetwork = if (req.columns.contains(OperReportColumn.retailNetwork)) {
                        row[OrganizationalUnitsTimelineTable.retailNetwork.aliasOnlyExpression()] ?: "-"
                    } else null,
                    ter = if (req.columns.contains(OperReportColumn.ter)) {
                        row[OrganizationalUnitsTimelineTable.territoryName.aliasOnlyExpression()] ?: "-"
                    } else null,
                    mr = if (req.columns.contains(OperReportColumn.mr)) {
                        row[OrganizationalUnitsTimelineTable.mrName.aliasOnlyExpression()] ?: "-"
                    } else null,
                    atp = if (req.columns.contains(OperReportColumn.atp)) {
                        row[OrganizationalUnitsTimelineTable.atpName.aliasOnlyExpression()] ?: "-"
                    } else null,
                    vehicleCount = if (req.columns.contains(OperReportColumn.vehicleCount)) {
                        row[vehicleCount]
                    } else null,
                    rcRetailNetwork = if (req.columns.contains(OperReportColumn.rcRetailNetwork)) {
                        row[rcRetailNetwork.aliasOnlyExpression()] ?: "-"
                    } else null,
                    rcName = if (req.columns.contains(OperReportColumn.rcName)) {
                        row[rcName.aliasOnlyExpression()] ?: "-"
                    } else null,
                    rcCode = if (req.columns.contains(OperReportColumn.rcCode)) {
                        row[idRcSap.aliasOnlyExpression()] ?: "-"
                    } else null,
                    logisticsRcName = if (req.columns.contains(OperReportColumn.logisticsRcName)) {
                        row[rcNameSapLogistics.aliasOnlyExpression()] ?: "-"
                    } else null,
                    idLogisticsRcSAP = if (req.columns.contains(OperReportColumn.idLogisticsRcSAP)) {
                        row[idRcSapLogistics.aliasOnlyExpression()] ?: "-"
                    } else null,
                    tripsCount = if (req.columns.contains(OperReportColumn.tripsCount)) {
                        row[tripsCount]
                    } else null,
                    pointsCount = if (req.columns.contains(OperReportColumn.pointsCount)) {
                        row[pointsCountSum]
                    } else null,
                    charaterDelivery = if (req.columns.contains(OperReportColumn.charaterDelivery)) {
                        row[charaterDelvr.aliasOnlyExpression()] ?: "-"
                    } else null,
                    rnAndCharaterDelivery = if (req.columns.contains(OperReportColumn.rnAndCharaterDelivery)) {
                        row[rnAndCharaterDelivery.aliasOnlyExpression()] ?: "-"
                    } else null,
                    ourTripText = if (req.columns.contains(OperReportColumn.ourTripText)) {
                        row[ourTripText.aliasOnlyExpression()] ?: "-"
                    } else null,
                    kipHours = if (req.columns.contains(OperReportColumn.kipHours)) {
                        row[kipHours]
                    } else null,
                    kipNoReserveHours = if (req.columns.contains(OperReportColumn.kipNoReserveHours)) {
                        row[kipNoReserveHours]
                    } else null,
                    kipShare = if (req.columns.contains(OperReportColumn.kipShare)) {
                        row[kipShare]
                    } else null,
                    kipNoReserveShare = if (req.columns.contains(OperReportColumn.kipNoReserveShare)) {
                        row[kipNoReserveShare]
                    } else null,
                    ktgShare = if (req.columns.contains(OperReportColumn.ktgShare)) {
                        row[ktgShare]
                    } else null,
                    rgShare = if (req.columns.contains(OperReportColumn.rgShare)) {
                        row[rgShare]
                    } else null,
                    comeOffCount = if (req.columns.contains(OperReportColumn.comeOffCount)) {
                        row[comeOffCount]
                    } else null,
                    comeOff = if (req.columns.contains(OperReportColumn.comeOff)) {
                        row[comeOffPercent]
                    } else null,
                    carInTime = if (req.columns.contains(OperReportColumn.carInTime)) {
                        row[carInTime]
                    } else null,
                    carInTimeOwn = if (req.columns.contains(OperReportColumn.carInTimeOwn)) {
                        row[carInTimeOwn]
                    } else null,
                    carInGPSOwn = if (req.columns.contains(OperReportColumn.carInGPSOwn)) {
                        row[carInGpsOwn]
                    } else null,
                    carInTimeHired = if (req.columns.contains(OperReportColumn.carInTimeHired)) {
                        row[carInTimeHired]
                    } else null,
                    carInGPSHired = if (req.columns.contains(OperReportColumn.carInGPSHired)) {
                        row[carInGpsHired]
                    } else null,
                    deliveryInPlan = if (req.columns.contains(OperReportColumn.deliveryInPlan)) {
                        row[deliveryInPlan]
                    } else null,
                    temperature = if (req.columns.contains(OperReportColumn.temperature)) {
                        row[temperature]
                    } else null,
                )
            }
        }

        OperReportResponse(
            count = totalCount ?: 0,
            pageNumber = req.pageNumber ?: 0,
            pageSize = req.pageSize ?: 0,
            items = items
        )
    }

    suspend fun getFilterValues(req: OperReportFilterReq): List<LabeledValue> = newSuspendedTransaction {
        val requested = OperReportColumnFilter(
            name = req.request.name,
            condition = FilterCondition.equal,
            value = listOf(req.request.value)
        )
        val dataRequest = OperReportRequest(
            pageSize = null,
            pageNumber = null,
            from = req.from,
            to = req.to,
            columns = req.columns,
            filters = req.filters,
            sort = emptyList(),
            geoFilter = req.geoFilter
        )

        val exp = requested.name.exposedExpression.aliasOnlyExpression()
        val filterValuesAgg = JsonAgg(StringDistinct(exp)).alias("filterValues")

        val rep = repo.prepareBaseQuery(dataRequest, filterValuesAgg, req.request.name).apply {
            if (req.request.value.isNotEmpty()) {
                andWhere {
                    buildFilterPredicate(
                        condition = FilterCondition.contain,
                        values = req.request.value,
                        type = req.request.name.type,
                        exposedExpression = req.request.name.exposedExpression.aliasOnlyExpression(),
                        strictFilter = false
                    )
                }
            }
        }.withDistinct()
        rep.flatMap { row ->
            val value = row[filterValuesAgg]
            mapper.readValue(value, List::class.java).map { it?.toString() }
        }.distinct().map {
            LabeledValue(
                label = it ?: "Пусто",
                value = it.toString(),
            )
        }.sortedBy { it.label }
    }

    fun exportToXlsx(req: OperReportRequest, username: String?): ResponseEntity<Resource> {
        val timeout = System.currentTimeMillis() + EXPORT_TIMEOUT
        logger.debug("Exporting repair parts report for {}", req)
        val singleResult = runBlocking{ getOperReport(req.copy(pageNumber = 0, pageSize = 1)) }
        val size = singleResult.count
        logger.debug("Exporting $size rows")
        val columns = req.columns
        val columnsNumber = columns.size + 1
        val out = streamWorkbook {
            val greyFont = wb.createFont()
            greyFont.color = HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.index
            val blackFont = wb.createFont()
            blackFont.color = HSSFColor.HSSFColorPredefined.BLACK.index

            val headerStyle = ExcelStyles.Header.style(this)
            val generalStyle = ExcelStyles.General.style(this)
            val tonnageStyle = ExcelStyles.Tonnage.style(this)
            val floatStyle = ExcelStyles.Float.style(this)
            val dateStyle = ExcelStyles.Date.style(this)
            val percentStyle = ExcelStyles.Percent.style(this)
            val styles = mapOf(
                "general" to generalStyle,
                "tonnage" to tonnageStyle,
                "float" to floatStyle,
                "date" to dateStyle,
                "percent" to percentStyle,
            )
            val reportReq = ExportRequest(
                from = req.from,
                to = req.to,
                userName = username,
                geoFilter = req.geoFilter,
                columns = req.columns.map { it.title },
                filters = req.filters.map { Filter(it.name.title, it.condition.description, it.value) },
                sort = req.sort.map { com.x5.logistics.service.settingssheet.SortOrder(it.column.title, it.asc) },
            )
            settingsSheetService.addSettingsSheet(this, reportReq)
            sheet(ReportName.OPER_REPORT.title) {
                style {
                    alignment = HorizontalAlignment.CENTER
                    verticalAlignment = VerticalAlignment.CENTER
                }
                header {
                    currStyle = headerStyle
                    columns
                        .forEach { column ->
                                head(column.title)
                        }
                }
                for (pageNumber in 0..(size / EXPORT_BATCH_SIZE).toInt()) {
                    if (System.currentTimeMillis() > timeout) throw ExportTimeoutException("export timeout")
                    log.debug("Fetching page $pageNumber, page size $EXPORT_BATCH_SIZE")
                    val data = runBlocking {
                        getOperReport(req.copy(pageNumber = pageNumber, pageSize = EXPORT_BATCH_SIZE))
                    }
                    data.items.forEach { item ->
                        row {
                            columns
                                .forEach { column ->
                                    cellByColumnType(this, styles, column, item)
                                }
                        }
                    }
                    (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
                }
                row {
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Всего строк:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(size)
                }
            }.autosize(columnsNumber)
            setActiveSheet(ReportName.OPER_REPORT.title)
        }.toInputStream()
        val fileName = "${ReportName.OPER_REPORT.title} ${moscowDateTime()}.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${
                    URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")
                }"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(out))
    }

    private fun cellByColumnType(
        rb: RowBuilder,
        styles: Map<String, CellStyle>,
        column: OperReportColumn,
        item: OperReportResponseItem
    ) = when (column) {
        OperReportColumn.periodDay -> {
            rb.cell(item.periodDay).cellStyle = styles["general"]
        }

        OperReportColumn.periodReportWeek -> {
            rb.cell(item.periodReportWeek).cellStyle = styles["general"]
        }

        OperReportColumn.periodCalendarWeek -> {
            rb.cell(item.periodCalendarWeek).cellStyle = styles["general"]
        }

        OperReportColumn.periodMonth -> {
            rb.cell(item.periodMonth).cellStyle = styles["general"]
        }

        OperReportColumn.periodQuarter -> {
            rb.cell(item.periodQuarter).cellStyle = styles["general"]
        }

        OperReportColumn.periodYear -> {
            rb.cell(item.periodYear).cellStyle = styles["general"]
        }

        OperReportColumn.ter -> {
            rb.cell(item.ter).cellStyle = styles["general"]
        }

        OperReportColumn.mr -> {
            rb.cell(item.mr).cellStyle = styles["general"]
        }

        OperReportColumn.atp -> {
            rb.cell(item.atp).cellStyle = styles["general"]
        }

        OperReportColumn.mvz -> {
            rb.cell(item.mvz).cellStyle = styles["general"]
        }

        OperReportColumn.mvzName -> {
            rb.cell(item.mvzName).cellStyle = styles["general"]
        }

        OperReportColumn.retailNetwork -> {
            rb.cell(item.retailNetwork).cellStyle = styles["general"]
        }

        OperReportColumn.vehicleLicense -> {
            rb.cell(item.vehicleLicense).cellStyle = styles["general"]
        }

        OperReportColumn.vehicleGroup -> {
            rb.cell(item.vehicleGroup).cellStyle = styles["general"]
        }

        OperReportColumn.vehicleType -> {
            rb.cell(item.vehicleType).cellStyle = styles["general"]
        }

        OperReportColumn.vehicleBrand -> {
            rb.cell(item.vehicleBrand).cellStyle = styles["general"]
        }

        OperReportColumn.vehicleModel -> {
            rb.cell(item.vehicleModel).cellStyle = styles["general"]
        }

        OperReportColumn.vehicleTonnage -> {
            rb.cell(item.vehicleTonnage).cellStyle = styles["tonnage"]
        }

        OperReportColumn.vehicleCreateYear -> {
            rb.cell(item.vehicleCreateYear).cellStyle = styles["general"]
        }

        OperReportColumn.comeOffCount -> {
            rb.cell(item.comeOffCount).cellStyle = styles["general"]
        }

        OperReportColumn.comeOff -> {
            rb.cell(item.comeOffCount).cellStyle = styles["general"]
        }

        OperReportColumn.carInTime -> {
            rb.cell(item.carInTime).cellStyle = styles["general"]
        }

        OperReportColumn.carInTimeOwn -> {
            rb.cell(item.carInTimeOwn).cellStyle = styles["general"]
        }

        OperReportColumn.carInGPSOwn -> {
            rb.cell(item.carInGPSOwn).cellStyle = styles["general"]
        }

        OperReportColumn.carInTimeHired -> {
            rb.cell(item.carInTimeHired).cellStyle = styles["general"]
        }

        OperReportColumn.carInGPSHired -> {
            rb.cell(item.carInGPSHired).cellStyle = styles["general"]
        }

//        OperReportColumn.deliveryInWindow -> {
//            rb.cell(item.deliveryInWindow).cellStyle = styles["general"]
//        }

        OperReportColumn.deliveryInPlan -> {
            rb.cell(item.deliveryInPlan).cellStyle = styles["general"]
        }

//        OperReportColumn.deliveryInWindowWithPlan -> {
//            rb.cell(item.deliveryInWindowWithPlan).cellStyle = styles["general"]
//        }

        OperReportColumn.temperature -> {
            rb.cell(item.temperature).cellStyle = styles["general"]
        }

        OperReportColumn.vehicleCount -> {
            rb.cell(item.vehicleCount).cellStyle = styles["general"]
        }

        OperReportColumn.rcRetailNetwork -> {
            rb.cell(item.rcRetailNetwork).cellStyle = styles["general"]
        }

        OperReportColumn.rcName -> {
            rb.cell(item.rcName).cellStyle = styles["general"]
        }

        OperReportColumn.rcCode -> {
            rb.cell(item.rcCode).cellStyle = styles["general"]
        }

        OperReportColumn.logisticsRcName -> {
            rb.cell(item.logisticsRcName).cellStyle = styles["general"]
        }

        OperReportColumn.idLogisticsRcSAP -> {
            rb.cell(item.idLogisticsRcSAP).cellStyle = styles["general"]
        }

        OperReportColumn.tripsCount -> {
            rb.cell(item.tripsCount).cellStyle = styles["general"]
        }

        OperReportColumn.pointsCount -> {
            rb.cell(item.pointsCount).cellStyle = styles["general"]
        }

        OperReportColumn.charaterDelivery -> {
            rb.cell(item.charaterDelivery).cellStyle = styles["general"]
        }

        OperReportColumn.rnAndCharaterDelivery -> {
            rb.cell(item.rnAndCharaterDelivery).cellStyle = styles["general"]
        }

        OperReportColumn.ourTripText -> {
            rb.cell(item.ourTripText).cellStyle = styles["general"]
        }

        OperReportColumn.kipHours -> {
            rb.cell(item.kipHours).cellStyle = styles["general"]
        }

        OperReportColumn.kipNoReserveHours -> {
            rb.cell(item.kipNoReserveHours).cellStyle = styles["general"]
        }

        OperReportColumn.kipShare -> {
            rb.cell(item.kipShare).cellStyle = styles["general"]
        }

        OperReportColumn.kipNoReserveShare -> {
            rb.cell(item.kipNoReserveShare).cellStyle = styles["general"]
        }

        OperReportColumn.ktgShare -> {
            rb.cell(item.ktgShare).cellStyle = styles["general"]
        }

        OperReportColumn.rgShare -> {
            rb.cell(item.rgShare).cellStyle = styles["general"]
        }
    }

    suspend fun getMassFilterValues(req: OperReportMassFilterReq): MassFilterResp {
        val requested = OperReportColumnFilter(
            name = req.request.name,
            condition = FilterCondition.equal,
            value = req.request.value
        )
        val request = OperReportFilterReq(
            from = req.from,
            to = req.to,
            columns = req.columns,
            filters = req.filters,
            request = requested,
            geoFilter = req.geoFilter,

            )
        return checkFilterValues(req.request.value, getFilterValues(request))
    }

    fun Expression<Long>.toDouble(): Expression<Double> = this.castTo(DoubleColumnType())
    fun Expression<Duration?>.toHours(): Expression<Double> =
        Extract("epoch", this).castTo(DoubleColumnType()) / doubleLiteral(3600.0)


}
