package com.x5.logistics.service

import com.x5.logistics.repository.repair.RepairsDetailedRepository
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.repair.detailed.RepairDetailedColumnFilter
import com.x5.logistics.rest.dto.repair.detailed.RepairsDetailedReq
import com.x5.logistics.rest.dto.repair.detailed.RepairsFilterReq
import org.springframework.stereotype.Service

@Service
class RepairFilterService(
    val repo: RepairsDetailedRepository
) {
    fun getFilterValues(request: RepairsFilterReq): List<LabeledValue> {
        val req = RepairsDetailedReq(
            pageNumber = 0,
            pageSize = Int.MAX_VALUE,
            from = request.from,
            to = request.to,
            geoFilter = request.geoFilter,
            columns = request.columns,
            sort = emptyList(),
            filters = request.filters + if (request.request.value.isNotEmpty()) {
                listOf(RepairDetailedColumnFilter(
                    name = request.request.name,
                    condition = FilterCondition.contain,
                    value = request.request.value
                ))
            } else {
                emptyList()
            },
        )

        val result = repo.getFilterValues(request.request.name, req)
        return result.map { LabeledValue(it ?: "Пусто", it.toString()) }
    }
}
