package com.x5.logistics.service.carin

import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.som.SomTripsTable
import com.x5.logistics.repository.CountFiltered
import com.x5.logistics.repository.CountStar
import com.x5.logistics.repository.NullIf
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.castToDouble
import com.x5.logistics.repository.times
import com.x5.logistics.rest.dto.carin.desktopwidget.CarInDesktopWidgetRequest
import com.x5.logistics.rest.dto.carin.desktopwidget.CarInDesktopWidgetResponse
import com.x5.logistics.service.DesktopWidgetsService
import com.x5.logistics.service.toDoubleSafely
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.OrOp
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SqlExpressionBuilder.div
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class CarInService(
    val desktopWidgetsService: DesktopWidgetsService
) {
    val log = getLogger()

    private inner class MetricBuilder() {
        fun buildOwnCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val baseCondition = ourTripFlag eq true
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val conditions = listOfNotNull(baseCondition, dateCondition).reduce { acc, expr -> acc and expr }

                val numerator = CountFiltered(conditions and (carIn eq true)).castToDouble()
                val denominator = CountFiltered(conditions).castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildGpsOwnCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val baseCondition = ourTripFlag eq true
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val conditions = listOfNotNull(baseCondition, dateCondition).reduce { acc, expr -> acc and expr }

                val numerator = CountFiltered(conditions and (carInGps eq true)).castToDouble()
                val denominator = CountFiltered(conditions).castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildHiredCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val baseCondition = ourTripFlag eq false
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val conditions = listOfNotNull(baseCondition, dateCondition).reduce { acc, expr -> acc and expr }

                val numerator = CountFiltered(conditions and (carIn eq true)).castToDouble()
                val denominator = CountFiltered(conditions).castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildGpsHiredCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val baseCondition = ourTripFlag eq false
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val conditions = listOfNotNull(baseCondition, dateCondition).reduce { acc, expr -> acc and expr }

                val numerator = CountFiltered(conditions and (carInGps eq true)).castToDouble()
                val denominator = CountFiltered(conditions).castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildAllCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val numeratorConditions = when {
                    dateCondition != null -> dateCondition and (carIn eq true)
                    else -> carIn eq true
                }

                val numerator = CountFiltered(numeratorConditions).castToDouble()
                val denominator = when {
                    dateCondition != null -> CountFiltered(dateCondition)
                    else -> CountStar
                }.castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }

        fun buildGpsAllCarsMetric(dateRange: Pair<LocalDate, LocalDate>? = null): Expression<Double> =
            with(SomTripsTable) {
                val dateCondition = dateRange?.let {
                    (dtRegRcDate greaterEq it.first) and (dtRegRcDate lessEq it.second)
                }

                val numeratorConditions = when {
                    dateCondition != null -> dateCondition and (carInGps eq true)
                    else -> carIn eq true
                }

                val numerator = CountFiltered(numeratorConditions).castToDouble()
                val denominator = when {
                    dateCondition != null -> CountFiltered(dateCondition)
                    else -> CountStar
                }.castToDouble()

                numerator / NullIf(denominator, doubleLiteral(0.0)) * doubleLiteral(100.0)
            }
    }

    suspend fun getCarInDesktopWidgetData(req: CarInDesktopWidgetRequest): CarInDesktopWidgetResponse =
        newSuspendedTransaction {
            val metricBuilder = MetricBuilder()
            val dateOffsets = desktopWidgetsService.getDateOffsets(req.from, req.to)

            val baseMetrics = listOf(
                metricBuilder.buildOwnCarsMetric().alias("own_cars_in"),
                metricBuilder.buildHiredCarsMetric().alias("hired_cars_in"),
                metricBuilder.buildAllCarsMetric().alias("all_cars_in"),
            )

            val baseGpsMetrics = when {
                req.flagGPS -> listOf(
                    metricBuilder.buildGpsOwnCarsMetric().alias("gps_own_cars_in"),
                    metricBuilder.buildGpsHiredCarsMetric().alias("gps_hired_cars_in"),
                    metricBuilder.buildGpsAllCarsMetric().alias("gps_all_cars_in")
                )

                else -> emptyList()
            }

            val diagramMetrics = dateOffsets.flatMapIndexed { index, offset ->
                listOf(
                    metricBuilder.buildOwnCarsMetric(offset).alias("own_cars_in_$index"),
                    metricBuilder.buildHiredCarsMetric(offset).alias("hired_cars_in_$index"),
                    metricBuilder.buildAllCarsMetric(offset).alias("all_cars_in_$index")
                )
            }

            val query = SomTripsTable
                .join(OrganizationalUnitsTimelineTable, JoinType.LEFT) {
                    (OrganizationalUnitsTimelineTable.mvzId eq SomTripsTable.mvzId) and
                            (OrganizationalUnitsTimelineTable.startDate lessEq SomTripsTable.dtRegRcDate) and
                            (SomTripsTable.dtRegRcDate less OrganizationalUnitsTimelineTable.endDate)
                }
                .select(baseMetrics + baseGpsMetrics + diagramMetrics)
                .applyFilters(req)

            log.info("Query: ${query.prepareSQL(QueryBuilder(false))}")

            query.single().toResponse(baseMetrics, baseGpsMetrics, diagramMetrics)
        }

    private fun ResultRow.toResponse(
        baseMetrics: List<Expression<*>>,
        baseGpsMetrics: List<Expression<*>>,
        diagramMetrics: List<Expression<*>>
    ): CarInDesktopWidgetResponse {
        val ownCars = this.getOrNull(baseMetrics[0])?.toDoubleSafely()
        val gpsOwnCars = if (baseGpsMetrics.isNotEmpty()) this.getOrNull(baseGpsMetrics[0])?.toDoubleSafely() else null
        val hiredCars = this.getOrNull(baseMetrics[1])?.toDoubleSafely()
        val gpsHiredCars =
            if (baseGpsMetrics.isNotEmpty()) this.getOrNull(baseGpsMetrics[1])?.toDoubleSafely() else null
        val allCars = when {
            ownCars == null || hiredCars == null -> null
            else -> this.getOrNull(baseMetrics[2])?.toDoubleSafely()
        }
        val gpsAllCars = when {
            baseGpsMetrics.isEmpty() || gpsOwnCars == null || gpsHiredCars == null -> null
            else -> this.getOrNull(baseGpsMetrics[2])?.toDoubleSafely()
        }
        val diagramPointsOwnCars = (diagramMetrics.indices step 3).map {
            this.getOrNull(diagramMetrics[it])?.toDoubleSafely()
        }
        val diagramPointsHiredCars = (diagramMetrics.indices step 3).map {
            this.getOrNull(diagramMetrics[it + 1])?.toDoubleSafely()
        }
        val diagramPointsAllCars = when {
            diagramPointsOwnCars.isEmpty() || diagramPointsHiredCars.isEmpty() -> emptyList()
            else -> (diagramMetrics.indices step 3).map {
                this.getOrNull(diagramMetrics[it + 2])?.toDoubleSafely()
            }
        }
        val pointsOwnCarsAllNulls = diagramPointsOwnCars.all { it == null }
        val pointsHiredCarsAllNulls = diagramPointsHiredCars.all { it == null }
        return CarInDesktopWidgetResponse(
            ownCars = ownCars,
            gpsOwnCars = gpsOwnCars,
            hiredCars = hiredCars,
            gpsHiredCars = gpsHiredCars,
            allCars = allCars,
            gpsAllCars = gpsAllCars,
            diagramPointsOwnCars = if (pointsOwnCarsAllNulls) emptyList() else diagramPointsOwnCars,
            diagramPointsHiredCars = if (pointsHiredCarsAllNulls) emptyList() else diagramPointsHiredCars,
            diagramPointsAllCars = if (pointsOwnCarsAllNulls || pointsHiredCarsAllNulls) emptyList() else diagramPointsAllCars,
            isEmpty = pointsOwnCarsAllNulls && pointsHiredCarsAllNulls
        )
    }

    private fun Query.applyFilters(req: CarInDesktopWidgetRequest) = apply {
        this.where {
            (SomTripsTable.dtRegRcDate greaterEq req.from) and
                    (SomTripsTable.dtRegRcDate lessEq req.to) and
                    OrOp(
                        req.characterDeliveryForRetailNetwork.map {
                            (SomTripsTable.rcRetailNetwork eq it.retailNetwork) and
                                    (SomTripsTable.charaterDelvr inList it.characterDelivery)
                        }
                    )
        }
            .applyGeoFilter(req.geoFilter)
    }
}

