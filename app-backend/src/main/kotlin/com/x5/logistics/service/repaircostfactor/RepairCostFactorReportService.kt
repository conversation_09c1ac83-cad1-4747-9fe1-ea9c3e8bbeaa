package com.x5.logistics.service.repaircostfactor

import com.x5.logistics.repository.repaircostfactor.RepairCostFactorExposedRepo
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportColumn
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportColumnDto
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportItem
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportMassFilterReq
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportReq
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportResp
import com.x5.logistics.rest.exception.ExportTimeoutException
import com.x5.logistics.service.RowBuilder
import com.x5.logistics.service.settingssheet.ExportRequest
import com.x5.logistics.service.settingssheet.Filter
import com.x5.logistics.service.settingssheet.ReportName
import com.x5.logistics.service.settingssheet.SettingsSheetService
import com.x5.logistics.service.settingssheet.SortOrder
import com.x5.logistics.service.streamWorkbook
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import com.x5.logistics.util.EXPORT_TIMEOUT
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.moscowDateTime
import org.apache.poi.hssf.util.HSSFColor
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.FillPatternType
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import java.net.URLEncoder

@Service
class RepairCostFactorReportService(
    val settingsSheetService: SettingsSheetService,
    val repo: RepairCostFactorExposedRepo
) {
    private val log = getLogger()

    fun getReportColumns() = RepairCostFactorReportColumn.entries.map {
        RepairCostFactorReportColumnDto(
            label = it.name,
            name = it.columnTitle,
            group = it.group.name,
            filterable = it.filterable
        )
    }

    fun getReport(req: RepairCostFactorReportReq): RepairCostFactorReportResp {
        log.debug("RepairCostFactor report. [req={}]", req)
        return repo.getReport(req)
    }

    fun getFilterValues(req: RepairCostFactorReportMassFilterReq): List<LabeledValue> {
        log.debug("RepairCostFactor filter [req={}]", req)
        return repo.getFilterValues(req)
    }

    fun getMassFilter(req: RepairCostFactorReportMassFilterReq): MassFilterResp {
        log.debug("RepairCostFactor Mass filter [req={}]", req)
        return repo.checkMassFilter(req)
    }

    fun exportReport(req: RepairCostFactorReportReq, username: String?): ResponseEntity<Resource> {
        log.debug("RepairCostFactor report export. [req={}]", req)
        val timeout = System.currentTimeMillis() + EXPORT_TIMEOUT
        val columns = req.columns
        val firstRow = repo.getReport(req.copy(pageSize = 1, pageNumber = 0))
        val totalCount = firstRow.count
        val totalRepairExpensesFull = firstRow.totalRepairExpensesFull
        val totalRepairRubFact = firstRow.totalRepairRubFact

        val granularColumns = if (req.granularity != null) {
            listOf(
                RepairCostFactorReportColumn.repairExpensesFullPlan to firstRow.items.first().granularityRepairExpensesFullPlan,
                RepairCostFactorReportColumn.repairExpensesFull to firstRow.items.first().granularityRepairExpensesFull,
                RepairCostFactorReportColumn.repairExpensesFullDeviation to firstRow.items.first().granularityRepairExpensesFullDeviation,
                RepairCostFactorReportColumn.repairRubPlan to firstRow.items.first().granularityRepairRubPlan,
                RepairCostFactorReportColumn.repairRub to firstRow.items.first().granularityRepairRub,
                RepairCostFactorReportColumn.repairRubDeviation to firstRow.items.first().granularityRepairRubDeviation
            ).filter {
                it.first in req.columns && it.second != null
            }.toMap()
        } else emptyMap()

        val out = streamWorkbook {
            val greyFont = wb.createFont()
            greyFont.color = HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.index
            val blackFont = wb.createFont()
            blackFont.color = HSSFColor.HSSFColorPredefined.BLACK.index

            val headerStyle = ExcelStyles.Header.style(this)
            val generalStyle = ExcelStyles.General.style(this)
            val floatStyle = ExcelStyles.Float.style(this)
            val dateStyle = ExcelStyles.Date.style(this)
            val redColoredFloatStyle = ExcelStyles.Float.style(this).apply {
                val customColor = wb.creationHelper.createExtendedColor()
                customColor.argbHex = "FFFFD2D2"
                setFillForegroundColor(customColor)
                fillPattern = FillPatternType.SOLID_FOREGROUND
            }

            val styles = mapOf(
                "general" to generalStyle,
                "float" to floatStyle,
                "date" to dateStyle,
            )
            style {
                alignment = HorizontalAlignment.CENTER
                verticalAlignment = VerticalAlignment.CENTER
            }
            val reportReq = ExportRequest(
                from = req.from,
                to = req.to,
                userName = username,
                geoFilter = req.geoFilter,
                columns = req.columns.map { it.columnTitle },
                filters = req.filters.map { Filter(it.name.columnTitle, it.condition.description, it.value) },
                sort = req.sort.map { SortOrder(it.column.columnTitle, it.asc) },
                granularitySupported = true,
                granularityValue = req.granularity?.description
            )
            settingsSheetService.addSettingsSheet(this, reportReq)
            sheet(ReportName.REPAIR_COST_FACTOR.title) {
                header {
                    currStyle = headerStyle
                    columns.forEach {
                        when {
                            it in granularColumns -> {
                                head(it.columnTitle) {
                                    granularColumns[it]?.forEach { item ->
                                        style {
                                            if (item.partPeriod) {
                                                setFont(greyFont)
                                            } else {
                                                setFont(blackFont)
                                            }
                                        }
                                        head(item.label)
                                        style { setFont(blackFont) }
                                    }
                                }

                                head(it.columnTitle)
                            }

                            else -> head(it.columnTitle)
                        }
                    }
                }
                for (pageNumber in 0..(totalCount / EXPORT_BATCH_SIZE)) {
                    if (System.currentTimeMillis() > timeout) throw ExportTimeoutException("export timeout")
                    val data = repo.getReport(
                        req.copy(pageNumber = pageNumber, pageSize = EXPORT_BATCH_SIZE)
                    )
                    data.items.forEach { item ->
                        row {
                            columns.forEach { column ->
                                currStyle = generalStyle
                                if (column in granularColumns) {
                                    when (column) {
                                        RepairCostFactorReportColumn.repairExpensesFullPlan -> {
                                            item.granularityRepairExpensesFullPlan?.forEach { cell(it.value) }
                                            cellByColumnType(this, styles, column, item)
                                        }

                                        RepairCostFactorReportColumn.repairExpensesFull -> {
                                            item.granularityRepairExpensesFull?.forEach { cell(it.value) }
                                            cellByColumnType(this, styles, column, item)
                                        }

                                        RepairCostFactorReportColumn.repairExpensesFullDeviation -> {
                                            item.granularityRepairExpensesFullDeviation?.forEach { v ->
                                                val value = v.value as Double?
                                                cell(value, cc = {
                                                    it.cellStyle =
                                                        if (value != null && value > 0.0) {
                                                            redColoredFloatStyle
                                                        } else {
                                                            floatStyle
                                                        }
                                                })
                                            }

                                            cell(item.repairExpensesFullDeviation, cc = {
                                                it.cellStyle =
                                                    if (item.repairExpensesFullDeviation != null &&
                                                        item.repairExpensesFullDeviation > 0.0
                                                    ) {
                                                        redColoredFloatStyle
                                                    } else floatStyle
                                            })
                                        }

                                        RepairCostFactorReportColumn.repairRubPlan -> {
                                            item.granularityRepairRubPlan?.forEach { cell(it.value) }
                                            cellByColumnType(this, styles, column, item)
                                        }

                                        RepairCostFactorReportColumn.repairRub -> {
                                            item.granularityRepairRub?.forEach { cell(it.value) }
                                            cellByColumnType(this, styles, column, item)
                                        }

                                        RepairCostFactorReportColumn.repairRubDeviation -> {
                                            item.granularityRepairRubDeviation?.forEach { v ->
                                                val value = v.value as Double?
                                                cell(value, cc = {
                                                    it.cellStyle =
                                                        if (value != null && value > 0.0) {
                                                            redColoredFloatStyle
                                                        } else {
                                                            floatStyle
                                                        }
                                                })
                                            }
                                            cell(item.repairRubDeviation, cc = {
                                                it.cellStyle =
                                                    if (item.repairRubDeviation != null &&
                                                        item.repairRubDeviation > 0.0
                                                    ) {
                                                        redColoredFloatStyle
                                                    } else {
                                                        floatStyle
                                                    }
                                            })
                                        }

                                        else -> throw IllegalArgumentException("Unsupported column: $column")
                                    }
                                } else cellByColumnType(this, styles, column, item)
                            }
                        }
                    }
                    (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
                }
                row {
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Затраты, руб:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(totalRepairExpensesFull)
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Факт, руб/км:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(totalRepairRubFact)
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Всего строк:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(totalCount)
                }
            }.autosize()
            setActiveSheet(ReportName.REPAIR_COST_FACTOR.title)
        }.toInputStream()

        val fileName = "${ReportName.REPAIR_COST_FACTOR.title} ${moscowDateTime()}.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(out))
    }

    private fun cellByColumnType(
        rb: RowBuilder,
        styles: Map<String, CellStyle>,
        column: RepairCostFactorReportColumn,
        item: RepairCostFactorReportItem
    ) = when (column) {
        RepairCostFactorReportColumn.ter -> rb.cell(item.ter).cellStyle = styles["general"]
        RepairCostFactorReportColumn.mr -> rb.cell(item.mr).cellStyle = styles["general"]
        RepairCostFactorReportColumn.atp -> rb.cell(item.atp).cellStyle = styles["general"]
        RepairCostFactorReportColumn.mvz -> rb.cell(item.mvz).cellStyle = styles["general"]
        RepairCostFactorReportColumn.mvzName -> rb.cell(item.mvzName).cellStyle = styles["general"]
        RepairCostFactorReportColumn.atpType -> rb.cell(item.atpType).cellStyle = styles["general"]
        RepairCostFactorReportColumn.mvzType -> rb.cell(item.mvzType).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vehicleLicense -> rb.cell(item.vehicleLicense).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vehicleGroup -> rb.cell(item.vehicleGroup).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vehicleType -> rb.cell(item.vehicleType).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vehicleBrand -> rb.cell(item.vehicleBrand).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vehicleModel -> rb.cell(item.vehicleModel).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vehicleTonnage -> rb.cell(item.vehicleTonnage).cellStyle = styles["float"]
        RepairCostFactorReportColumn.vehicleYear -> rb.cell(item.vehicleYear).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vehicleCreateDate -> rb.cell(item.vehicleCreateDate).cellStyle = styles["date"]
        RepairCostFactorReportColumn.vehicleVin -> rb.cell(item.vehicleVin).cellStyle = styles["general"]
        RepairCostFactorReportColumn.equnr -> rb.cell(item.equnr).cellStyle = styles["general"]
        RepairCostFactorReportColumn.gbo -> rb.cell(item.gbo).cellStyle = styles["general"]
        RepairCostFactorReportColumn.compartAmount -> rb.cell(item.compartAmount).cellStyle = styles["general"]
        RepairCostFactorReportColumn.trailerLicenseNum -> rb.cell(item.trailerLicenseNum).cellStyle = styles["general"]
        RepairCostFactorReportColumn.repairExpensesFull -> rb.cell(item.repairExpensesFull).cellStyle = styles["float"]
        RepairCostFactorReportColumn.mileage -> rb.cell(item.mileage).cellStyle = styles["float"]
        RepairCostFactorReportColumn.repairRub -> rb.cell(item.repairRub).cellStyle = styles["float"]
        RepairCostFactorReportColumn.structureName -> rb.cell(item.structureName).cellStyle = styles["general"]
        RepairCostFactorReportColumn.repairPlace -> rb.cell(item.repairPlace).cellStyle = styles["general"]
        RepairCostFactorReportColumn.reqType -> rb.cell(item.reqType).cellStyle = styles["general"]
        RepairCostFactorReportColumn.reqSubtype -> rb.cell(item.reqSubtype).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vrt -> rb.cell(item.vrt).cellStyle = styles["general"]
        RepairCostFactorReportColumn.vrtName -> rb.cell(item.vrtName).cellStyle = styles["general"]
        RepairCostFactorReportColumn.eventId -> rb.cell(item.eventId).cellStyle = styles["general"]
        RepairCostFactorReportColumn.eventText -> rb.cell(item.eventText).cellStyle = styles["general"]
        RepairCostFactorReportColumn.repairExpensesFullPlan -> rb.cell(item.repairExpensesFullPlan).cellStyle =
            styles["float"]

        RepairCostFactorReportColumn.repairExpensesFullDeviation -> rb.cell(item.repairExpensesFullDeviation).cellStyle =
            styles["float"]

        RepairCostFactorReportColumn.repairRubPlan -> rb.cell(item.repairRubPlan).cellStyle = styles["float"]
        RepairCostFactorReportColumn.repairRubDeviation -> rb.cell(item.repairRubDeviation).cellStyle = styles["float"]
    }
}