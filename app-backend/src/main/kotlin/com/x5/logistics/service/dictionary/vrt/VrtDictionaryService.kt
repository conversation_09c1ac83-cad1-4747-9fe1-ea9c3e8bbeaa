package com.x5.logistics.service.dictionary.vrt

import com.x5.logistics.data.dictionary.vrt.ToroWork
import com.x5.logistics.data.dictionary.vrt.ToroWorksSubtype
import com.x5.logistics.data.dictionary.vrt.ToroWorksType
import com.x5.logistics.repository.dictionary.vrt.ToroWorkJpaRepository
import com.x5.logistics.repository.dictionary.vrt.ToroWorksExposedRepo
import com.x5.logistics.repository.dictionary.vrt.ToroWorksSubtypeJpaRepository
import com.x5.logistics.repository.dictionary.vrt.ToroWorksTypeJpaRepository
import com.x5.logistics.rest.dto.SortItem
import com.x5.logistics.rest.dto.dictionary.vrt.AddVrtSubtypeDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.AddVrtTypeDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.EditVrtDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.EditVrtSubtypeDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.EditVrtTypeDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryCheckNewResponse
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryFilterItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryFilterValues
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryListReqFilterField
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryListReqSortField
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionarySubtypeFilterItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryTypeFilterItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtSubtypeDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtSubtypeDictionaryListReqFilterField
import com.x5.logistics.rest.dto.dictionary.vrt.VrtSubtypeDictionaryListReqSortField
import com.x5.logistics.rest.dto.dictionary.vrt.VrtSubtypeItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeDictionaryListReqFilterField
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeDictionaryListReqSortField
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeItem
import com.x5.logistics.rest.exception.WrongRequestDataException
import org.springframework.stereotype.Service
import java.time.Instant
import jakarta.persistence.criteria.CriteriaBuilder
import jakarta.persistence.criteria.JoinType
import jakarta.persistence.criteria.Order
import jakarta.persistence.criteria.Predicate
import jakarta.persistence.criteria.Root
import jakarta.transaction.Transactional

@Service
class VrtDictionaryService(
    private val toroWorkJpaRepository: ToroWorkJpaRepository,
    private val toroWorksTypeJpaRepository: ToroWorksTypeJpaRepository,
    private val toroWorksSubtypeJpaRepository: ToroWorksSubtypeJpaRepository,
    private val toroWorksExposedRepo: ToroWorksExposedRepo
) {
    fun getVrtDictionaryGroupList(req: VrtDictionaryListReq): List<VrtDictionaryItem> {
        val typeIds = toroWorkJpaRepository.findAllTypeIds().toSet()
        val subtypeIds = toroWorkJpaRepository.findAllSubtypeIds().toSet()
        return getToroWorkDaos(req).map { it.toDto(typeIds, subtypeIds) }
    }

    fun countVrtDictionaryGroupList(req: VrtDictionaryListReq): Int =
        toroWorkJpaRepository.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(Long::class.javaObjectType)
            val root = query.from(ToroWork::class.java)
            query.select(builder.count(root))
            req.getPredicate(builder, root)?.let { predicate ->
                query.where(predicate)
            }
            val typedQuery = createQuery(query)
            typedQuery.singleResult.toInt()
        }

    fun getVrtDictionaryFilterItems(): List<VrtDictionaryFilterItem> =
        toroWorkJpaRepository.findAllFixed().map {
            VrtDictionaryFilterItem(
                id = it.id,
                name = it.name
            )
        }

    fun getVrtDictionaryFilterValues(): VrtDictionaryFilterValues = VrtDictionaryFilterValues(
        vrtId = toroWorkJpaRepository.findAllIds(),
        vrtName = toroWorkJpaRepository.findAllNames(),
        types = toroWorksTypeJpaRepository.findDependentNames(),
        subtypes = toroWorksSubtypeJpaRepository.findDependentNames(),
        useNh = toroWorkJpaRepository.findAllUseNh().map {
            when (it) {
                null -> "Пусто"
                false -> "Исключено"
                true -> "Участвует"
            }
        },
    )

    fun getVrtDictionaryTypeFilterItems(): List<VrtDictionaryTypeFilterItem> =
        toroWorksTypeJpaRepository.findAll().filterNot { it.deleted }.map {
            VrtDictionaryTypeFilterItem(
                id = it.id,
                name = it.name
            )
        }.sortedBy { it.name }

    fun getVrtDictionarySubtypeFilterItems(): List<VrtDictionarySubtypeFilterItem> =
        toroWorksSubtypeJpaRepository.findAll().filterNot { it.deleted }.map {
            VrtDictionarySubtypeFilterItem(
                id = it.id,
                name = it.name
            )
        }.sortedBy { it.name }

    fun getVrtTypeDictionaryItems(req: VrtTypeDictionaryListReq): List<VrtTypeItem> {
        val typeIds = toroWorkJpaRepository.findAllTypeIds().toSet()
        return toroWorksTypeJpaRepository.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(ToroWorksType::class.java)
            val root = query.from(ToroWorksType::class.java)
            query.select(root)
            req.getPredicate(builder, root)?.let { predicate ->
                query.where(predicate)
            }
            req.sort.map { it.getOrder(builder, root) }.takeIf { it.isNotEmpty() }?.let {
                query.orderBy(it)
            }
            val typedQuery = createQuery(query)
            typedQuery.firstResult = req.pageNumber * req.pageSize
            typedQuery.maxResults = req.pageSize
            typedQuery.resultList
        }.map { it.toDto(typeIds) }
    }

    fun countVrtTypeDictionaryItems(req: VrtTypeDictionaryListReq): Int =
        toroWorksTypeJpaRepository.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(Long::class.javaObjectType)
            val root = query.from(ToroWorksType::class.java)
            query.select(builder.count(root))
            req.getPredicate(builder, root)?.let { predicate ->
                query.where(predicate)
            }
            val typedQuery = createQuery(query)
            typedQuery.singleResult.toInt()
        }

    fun getVrtSubtypeDictionaryItems(req: VrtSubtypeDictionaryListReq): List<VrtSubtypeItem> {
        val subtypeIds = toroWorkJpaRepository.findAllSubtypeIds().toSet()
        return toroWorksTypeJpaRepository.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(ToroWorksSubtype::class.java)
            val root = query.from(ToroWorksSubtype::class.java)
            query.select(root)
            req.getPredicate(builder, root)?.let { predicate ->
                query.where(predicate)
            }
            req.sort.map { it.getOrder(builder, root) }.takeIf { it.isNotEmpty() }?.let {
                query.orderBy(it)
            }
            val typedQuery = createQuery(query)
            typedQuery.firstResult = req.pageNumber * req.pageSize
            typedQuery.maxResults = req.pageSize
            typedQuery.resultList
        }.map { it.toDto(subtypeIds) }
    }

    fun countVrtSubtypeDictionaryItems(req: VrtSubtypeDictionaryListReq): Int =
        toroWorksTypeJpaRepository.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(Long::class.javaObjectType)
            val root = query.from(ToroWorksSubtype::class.java)
            query.select(builder.count(root))
            req.getPredicate(builder, root)?.let { predicate ->
                query.where(predicate)
            }
            val typedQuery = createQuery(query)
            typedQuery.singleResult.toInt()
        }

    fun editVrtDictionaryItem(req: EditVrtDictionaryItemReq, author: String): VrtDictionaryItem {
        var item = toroWorkJpaRepository.findById(req.id).orElseThrow {
            WrongRequestDataException("vrt dictionary item is not found")
        }
        val typeIds = toroWorkJpaRepository.findAllTypeIds().toSet()
        val subtypeIds = toroWorkJpaRepository.findAllSubtypeIds().toSet()
        req.name?.let {
            item = item.copy(name = it)
        }
        req.useNh?.let {
            item = item.copy(useNh = it)
        }
        req.typeId?.let {
            val type = toroWorksTypeJpaRepository.findById(it).orElseThrow {
                WrongRequestDataException("vrt dictionary type item is not found")
            }
            item = item.copy(type = type)
        }
        req.subtypeId?.let {
            val subtype = toroWorksSubtypeJpaRepository.findById(it).orElseThrow {
                WrongRequestDataException("vrt dictionary subtype item is not found")
            }
            item = item.copy(subtype = subtype)
        }
        item = item.copy(
            updatedBy = author,
            updatedAt = Instant.now()
        )
        item = toroWorkJpaRepository.save(item)
        return item.toDto(typeIds, subtypeIds)
    }

    fun editVrtTypeDictionaryItem(req: EditVrtTypeDictionaryItemReq, author: String): VrtTypeItem {
        var item = toroWorksTypeJpaRepository.findById(req.id).orElseThrow {
            WrongRequestDataException("vrt dictionary type item is not found")
        }
        val typeIds = toroWorkJpaRepository.findAllTypeIds().toSet()
        req.name?.let {
            item = item.copy(name = it)
        }
        item = item.copy(
            updatedBy = author,
            updatedAt = Instant.now()
        )
        item = toroWorksTypeJpaRepository.save(item)
        return item.toDto(typeIds)
    }

    @Transactional
    fun addVrtTypeDictionaryItem(req: AddVrtTypeDictionaryItemReq, author: String): VrtTypeItem {
        if (req.name == "") {
            throw WrongRequestDataException("Заполните вид ремонта")
        }
        if (toroWorksTypeJpaRepository.checkNameDuplicates(req.name) > 0) {
            throw WrongRequestDataException("VRT type with the same name already exists")
        }
        val typeIds = toroWorkJpaRepository.findAllTypeIds().toSet()
        val item = ToroWorksType(
            id = 0,
            name = req.name,
            createdBy = author,
            createdAt = Instant.now()
        )
        return toroWorksTypeJpaRepository.save(item).toDto(typeIds)
    }

    fun deleteVrtTypeDictionaryItem(id: Long, author: String) {
        var item = toroWorksTypeJpaRepository.findById(id).orElseThrow {
            WrongRequestDataException("vrt dictionary type item is not found")
        }
        if (toroWorkJpaRepository.findAllTypeIds().toSet().contains(id.toInt())) {
            throw WrongRequestDataException("В других таблицах присутствуют связи с удаляемым объектом")
        }
        item = item.copy(
            updatedBy = author,
            updatedAt = Instant.now(),
            deleted = true
        )
        toroWorksTypeJpaRepository.save(item)
    }

    fun editVrtSubtypeDictionaryItem(req: EditVrtSubtypeDictionaryItemReq, author: String): VrtSubtypeItem {
        var item = toroWorksSubtypeJpaRepository.findById(req.id).orElseThrow {
            WrongRequestDataException("vrt dictionary subtype item is not found")
        }
        val subtypeIds = toroWorkJpaRepository.findAllSubtypeIds().toSet()
        req.name?.let {
            item = item.copy(name = it)
        }
        req.color?.let {
            item = item.copy(color = it)
        }
        item = item.copy(
            updatedBy = author,
            updatedAt = Instant.now()
        )
        item = toroWorksSubtypeJpaRepository.save(item)
        return item.toDto(subtypeIds)
    }

    @Transactional
    fun addVrtSubtypeDictionaryItem(req: AddVrtSubtypeDictionaryItemReq, author: String): VrtSubtypeItem {
        if (req.name == "") {
            throw WrongRequestDataException("Заполните подвид ремонта")
        }
        if (toroWorksSubtypeJpaRepository.checkNameDuplicates(req.name) > 0) {
            throw WrongRequestDataException("VRT subtype with the same name already exists")
        }
        val subtypeIds = toroWorkJpaRepository.findAllSubtypeIds().toSet()
        val item = ToroWorksSubtype(
            id = 0,
            name = req.name,
            color = req.color,
            createdBy = author,
            createdAt = Instant.now()
        )
        return toroWorksSubtypeJpaRepository.save(item).toDto(subtypeIds)
    }

    fun deleteVrtSubtypeDictionaryItem(id: Long, author: String) {
        var item = toroWorksSubtypeJpaRepository.findById(id).orElseThrow {
            WrongRequestDataException("vrt dictionary subtype item is not found")
        }
        if (toroWorkJpaRepository.findAllSubtypeIds().toSet().contains(id.toInt())) {
            throw WrongRequestDataException("В других таблицах присутствуют связи с удаляемым объектом")
        }
        item = item.copy(
            updatedBy = author,
            updatedAt = Instant.now(),
            deleted = true
        )
        toroWorksSubtypeJpaRepository.save(item)
    }

    fun getNewVrts() = VrtDictionaryCheckNewResponse(
        vrtSubtypes = toroWorksExposedRepo.getVrtsWithoutSubtype(),
        vrtTypes = toroWorksExposedRepo.getVrtsWithoutType()
    )

    private fun getToroWorkDaos(req: VrtDictionaryListReq): List<ToroWork> = toroWorkJpaRepository.getByJpa {
        val builder = criteriaBuilder
        val query = builder.createQuery(ToroWork::class.java)
        val root = query.from(ToroWork::class.java)
        query.select(root)
        req.getPredicate(builder, root)?.let { predicate ->
            query.where(predicate)
        }
        req.sort.map { it.getOrder(builder, root) }.takeIf { it.isNotEmpty() }?.let {
            query.orderBy(it)
        }
        val typedQuery = createQuery(query)
        typedQuery.firstResult = req.pageNumber * req.pageSize
        typedQuery.maxResults = req.pageSize
        typedQuery.resultList
    }

    private fun VrtDictionaryListReq.getPredicate(
        criteriaBuilder: CriteriaBuilder,
        root: Root<ToroWork>
    ): Predicate? {
        val predicates = buildList {
            filters?.forEach { filter ->
                val path = when (filter.name) {
                    VrtDictionaryListReqFilterField.ID -> {
                       root.get<String>("id")
                    }
                    VrtDictionaryListReqFilterField.NAME -> {
                        root.get<String?>("name")
                    }
                    VrtDictionaryListReqFilterField.USE_NH -> {
                        root.get<Boolean?>("useNh")
                    }
                    VrtDictionaryListReqFilterField.TYPE_NAME -> {
                        root.get<ToroWorksType>("type").get<String?>("name")
                    }
                    VrtDictionaryListReqFilterField.SUBTYPE_NAME -> {
                        root.get<ToroWorksSubtype>("subtype").get<String?>("name")
                    }
                }
                if (filter.value.any { it == null || it == "Пусто" }) {
                    add(criteriaBuilder.or(path.`in`(filter.value), path.isNull))
                } else {
                    add(path.`in`(filter.value))
                }
            }
        }.toTypedArray()
        return if (predicates.isEmpty()) {
            null
        } else if (predicates.size == 1) {
            predicates[0]
        } else {
            criteriaBuilder.and(*predicates)
        }
    }

    private fun VrtTypeDictionaryListReq.getPredicate(
        criteriaBuilder: CriteriaBuilder,
        root: Root<ToroWorksType>
    ): Predicate? {
        val predicates = buildList {
            val deletedPath = root.get<Boolean>("deleted")
            add(deletedPath.`in`(false))
            filters?.forEach {
                val path = when (it.name) {
                    VrtTypeDictionaryListReqFilterField.ID -> root.get<Long>("id")
                    VrtTypeDictionaryListReqFilterField.NAME -> root.get<String>("name")
                }
                add(path.`in`(it.value))
            }
        }.toTypedArray()
        return if (predicates.isEmpty()) {
            null
        } else if (predicates.size == 1) {
            predicates[0]
        } else {
            criteriaBuilder.and(*predicates)
        }
    }

    private fun VrtSubtypeDictionaryListReq.getPredicate(
        criteriaBuilder: CriteriaBuilder,
        root: Root<ToroWorksSubtype>
    ): Predicate? {
        val predicates = buildList {
            val deletedPath = root.get<Boolean>("deleted")
            add(deletedPath.`in`(false))
            filters?.forEach {
                val path = when (it.name) {
                    VrtSubtypeDictionaryListReqFilterField.ID -> root.get<Long>("id")
                    VrtSubtypeDictionaryListReqFilterField.NAME -> root.get<String>("name")
                }
                add(path.`in`(it.value))
            }
        }.toTypedArray()
        return if (predicates.isEmpty()) {
            null
        } else if (predicates.size == 1) {
            predicates[0]
        } else {
            criteriaBuilder.and(*predicates)
        }
    }

    @JvmName("getOrderVrt")
    private fun SortItem<VrtDictionaryListReqSortField>.getOrder(
        criteriaBuilder: CriteriaBuilder,
        root: Root<ToroWork>
    ): Order {
        val path = when (column) {
            VrtDictionaryListReqSortField.ID -> root.get<String>("id")
            VrtDictionaryListReqSortField.NAME -> root.get<String>("name")
            VrtDictionaryListReqSortField.USE_NH -> root.get<Boolean>("useNh")
            VrtDictionaryListReqSortField.UPDATED_BY -> root.get<String>("updatedBy")
            VrtDictionaryListReqSortField.UPDATED_AT -> root.get<Instant>("updatedAt")
            VrtDictionaryListReqSortField.TYPE_ID ->
                root.join<ToroWork, ToroWorksType>("type", JoinType.LEFT).get<Long>("id")
            VrtDictionaryListReqSortField.TYPE_NAME ->
                root.join<ToroWork, ToroWorksType>("type", JoinType.LEFT).get<String>("name")
            VrtDictionaryListReqSortField.SUBTYPE_ID ->
                root.join<ToroWork, ToroWorksSubtype>("subtype", JoinType.LEFT).get<Long>("id")
            VrtDictionaryListReqSortField.SUBTYPE_NAME ->
                root.join<ToroWork, ToroWorksSubtype>("subtype", JoinType.LEFT).get<String>("name")
            VrtDictionaryListReqSortField.SUBTYPE_IS_NULL -> root.get<Boolean>("subtypeIsNull")
            VrtDictionaryListReqSortField.TYPE_IS_NULL -> root.get<Boolean>("typeIsNull")
        }
        return if (asc) {
            criteriaBuilder.asc(path)
        } else {
            criteriaBuilder.desc(path)
        }
    }

    @JvmName("getOrderTypeVrt")
    private fun SortItem<VrtTypeDictionaryListReqSortField>.getOrder(
        criteriaBuilder: CriteriaBuilder,
        root: Root<ToroWorksType>
    ): Order {
        val path = when (column) {
            VrtTypeDictionaryListReqSortField.ID -> root.get<Long>("id")
            VrtTypeDictionaryListReqSortField.NAME -> root.get<String>("name")
            VrtTypeDictionaryListReqSortField.AUTHOR -> criteriaBuilder.coalesce(
                root.get<String>("updatedBy"), root.get<String>("createdBy"))
            VrtTypeDictionaryListReqSortField.CREATED_BY -> root.get<String>("createdBy")
            VrtTypeDictionaryListReqSortField.CREATED_AT -> root.get<Instant>("created_at")
            VrtTypeDictionaryListReqSortField.UPDATED_BY -> root.get<String>("updated_by")
            VrtTypeDictionaryListReqSortField.UPDATED_AT -> root.get<Instant>("updated_at")
        }
        return if (asc) {
            criteriaBuilder.asc(path)
        } else {
            criteriaBuilder.desc(path)
        }
    }

    @JvmName("getOrderSubtypeVrt")
    private fun SortItem<VrtSubtypeDictionaryListReqSortField>.getOrder(
        criteriaBuilder: CriteriaBuilder,
        root: Root<ToroWorksSubtype>
    ): Order {
        val path = when (column) {
            VrtSubtypeDictionaryListReqSortField.ID -> root.get<Long>("id")
            VrtSubtypeDictionaryListReqSortField.NAME -> root.get<String>("name")
            VrtSubtypeDictionaryListReqSortField.COLOR -> root.get<String>("color")
            VrtSubtypeDictionaryListReqSortField.AUTHOR -> criteriaBuilder.coalesce(
                root.get<String>("updatedBy"), root.get<String>("createdBy"))
            VrtSubtypeDictionaryListReqSortField.CREATED_BY -> root.get<String>("createdBy")
            VrtSubtypeDictionaryListReqSortField.CREATED_AT -> root.get<Instant>("createdAt")
            VrtSubtypeDictionaryListReqSortField.UPDATED_BY -> root.get<String>("updatedBy")
            VrtSubtypeDictionaryListReqSortField.UPDATED_AT -> root.get<Instant>("updatedAt")
        }
        return if (asc) {
            criteriaBuilder.asc(path)
        } else {
            criteriaBuilder.desc(path)
        }
    }

    private fun ToroWork.toDto(typeIds: Set<Int>, subtypeIds: Set<Int>): VrtDictionaryItem =
        VrtDictionaryItem(
            id = id,
            name = name,
            useNh = useNh,
            type = type?.takeIf { !it.deleted }?.toDto(typeIds),
            subtype = subtype?.takeIf { !it.deleted }?.toDto(subtypeIds),
            updatedBy = updatedBy,
            updatedAt = updatedAt
        )

    private fun ToroWorksType.toDto(typeIds: Set<Int>): VrtTypeItem =
        VrtTypeItem(
            id = id,
            name = name,
            createdBy = createdBy,
            createdAt = createdAt,
            updatedBy = updatedBy,
            updatedAt = updatedAt,
            deletable = !typeIds.contains(id.toInt())
        )

    private fun ToroWorksSubtype.toDto(subtypeIds: Set<Int>): VrtSubtypeItem =
        VrtSubtypeItem(
            id = id,
            name = name,
            color = color,
            createdBy = createdBy,
            createdAt = createdAt,
            updatedBy = updatedBy,
            updatedAt = updatedAt,
            deletable = !subtypeIds.contains(id.toInt())
        )
}
