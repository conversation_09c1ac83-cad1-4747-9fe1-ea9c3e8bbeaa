package com.x5.logistics.service

import com.x5.logistics.repository.WaybillDetailsRepository
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.waybill.WaybillFilterReq
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn
import com.x5.logistics.rest.dto.waybill.WaybillLabel
import org.springframework.stereotype.Service

@Service
class WaybillDetailsService(val repo: WaybillDetailsRepository) {
    fun getFilterValues(req: WaybillFilterReq): List<LabeledValue> {
        val requestedFilter = req.request
        val filters = req.filters
        val result = repo.getFilterValues(req, requestedFilter, filters).mapNotNull {
            if (requestedFilter.name == WaybillItemDetailsColumn.labels) {
                WaybillLabel.valueOf(it).label.takeIf { label ->
                    requestedFilter.value.isEmpty() || requestedFilter.value.any { value ->
                        label.contains(value.toString(), true)
                    }
                }?.let { LabeledValue(it, it) }
            } else {
                LabeledValue(if (it == "null") "Пусто" else it, it)
            }
        }.sortedBy(LabeledValue::label)
        return result
    }
}
