package com.x5.logistics.service.som

import com.x5.logistics.repository.som.SomReportRepo
import com.x5.logistics.rest.dto.som.SomColumns
import com.x5.logistics.rest.dto.som.SomReportItem
import com.x5.logistics.rest.dto.som.SomReportReq
import com.x5.logistics.rest.exception.ExportTimeoutException
import com.x5.logistics.service.RowBuilder
import com.x5.logistics.service.settingssheet.ExportRequest
import com.x5.logistics.service.settingssheet.Filter
import com.x5.logistics.service.settingssheet.ReportName
import com.x5.logistics.service.settingssheet.SettingsSheetService
import com.x5.logistics.service.settingssheet.SortOrder
import com.x5.logistics.service.streamWorkbook
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import com.x5.logistics.util.EXPORT_TIMEOUT
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.moscowDateTime
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Component
import java.net.URLEncoder

@Component
class SomReportExportService(
    private val repo: SomReportRepo,
    private val settingsSheetService: SettingsSheetService
) {
    fun exportReport(req: SomReportReq, username: String): ResponseEntity<Resource> {
        val timeout = System.currentTimeMillis() + EXPORT_TIMEOUT

        val columns = req.columns
        val totalCount = repo.getReport(req.copy(pageSize = 1, pageNumber = 0)).count

        val out = streamWorkbook {
            val styleMap = ExcelStyles.entries.associateWith { it.style(this) }
            style {
                alignment = HorizontalAlignment.CENTER
                verticalAlignment = VerticalAlignment.CENTER
            }
            val reportReq = ExportRequest(
                from = req.from,
                to = req.to,
                userName = username,
                geoFilter = req.geoFilter,
                columns = req.columns.map { it.columnName },
                filters = req.filters.map { Filter(it.name.columnName, it.condition.description, it.value) },
                sort = req.sort.map { SortOrder(it.column.columnName, it.asc) }
            )
            settingsSheetService.addSettingsSheet(this, reportReq)
            sheet(ReportName.SOM.title) {
                header {
                    currStyle = styleMap[ExcelStyles.Header]
                    columns.map { it.columnName }.forEach { head(it) }
                }
                for (pageNumber in 0..(totalCount / EXPORT_BATCH_SIZE)) {
                    if (System.currentTimeMillis() > timeout) throw ExportTimeoutException("export timeout")
                    val data = repo.getReport(
                        req.copy(pageNumber = pageNumber.toInt(), pageSize = EXPORT_BATCH_SIZE)
                    )
                    data.items.forEach { item ->
                        row {
                            currStyle = styleMap[ExcelStyles.General]
                            columns.forEach { column -> cellByColumnType(this, styleMap, column, item) }
                        }
                    }
                    (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
                }
                row {
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Всего строк:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(totalCount)
                }
            }.autosize()
            setActiveSheet(ReportName.SOM.title)
        }.toInputStream()

        val fileName = "${ReportName.SOM.title} ${moscowDateTime()}.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(out))
    }

    private fun cellByColumnType(
        rb: RowBuilder,
        styles: Map<ExcelStyles, CellStyle>,
        column: SomColumns,
        item: SomReportItem
    ) = when (column) {
        SomColumns.vehicleLicense -> rb.cell(item.vehicleLicense).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleGroup -> rb.cell(item.vehicleGroup).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleType -> rb.cell(item.vehicleType).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleBrand -> rb.cell(item.vehicleBrand).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleModel -> rb.cell(item.vehicleModel).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleTonnage -> rb.cell(item.vehicleTonnage).cellStyle = styles[ExcelStyles.Tonnage]
        SomColumns.vehicleCreateYear -> rb.cell(item.vehicleCreateYear).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleVin -> rb.cell(item.vehicleVin).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleId -> rb.cell(item.vehicleId).cellStyle = styles[ExcelStyles.General]
        SomColumns.trailerLicenseNum -> rb.cell(item.trailerLicenseNum).cellStyle = styles[ExcelStyles.General]
        SomColumns.ter -> rb.cell(item.ter).cellStyle = styles[ExcelStyles.General]
        SomColumns.mr -> rb.cell(item.mr).cellStyle = styles[ExcelStyles.General]
        SomColumns.atp -> rb.cell(item.atp).cellStyle = styles[ExcelStyles.General]
        SomColumns.mvz -> rb.cell(item.mvz).cellStyle = styles[ExcelStyles.General]
        SomColumns.mvzName -> rb.cell(item.mvzName).cellStyle = styles[ExcelStyles.General]
        SomColumns.retailNetwork -> rb.cell(item.retailNetwork).cellStyle = styles[ExcelStyles.General]
        SomColumns.atpType -> rb.cell(item.atpType).cellStyle = styles[ExcelStyles.General]
        SomColumns.mvzType -> rb.cell(item.mvzType).cellStyle = styles[ExcelStyles.General]
        SomColumns.TMSRouteNum -> rb.cell(item.TMSRouteNum).cellStyle = styles[ExcelStyles.General]
        SomColumns.NQRouteNum -> rb.cell(item.NQRouteNum).cellStyle = styles[ExcelStyles.General]
        SomColumns.retailNetworkOrder -> rb.cell(item.retailNetworkOrder).cellStyle = styles[ExcelStyles.General]
        SomColumns.rcName -> rb.cell(item.rcName).cellStyle = styles[ExcelStyles.General]
        SomColumns.idRcSAP -> rb.cell(item.idRcSAP).cellStyle = styles[ExcelStyles.General]
        SomColumns.logisticsRcName -> rb.cell(item.logisticsRcName).cellStyle = styles[ExcelStyles.General]
        SomColumns.idLogisticsRcSAP -> rb.cell(item.idLogisticsRcSAP).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleArriveDate -> rb.cell(item.vehicleArriveDate).cellStyle = styles[ExcelStyles.Date]
        SomColumns.vehicleArriveTime -> rb.cell(item.vehicleArriveTime).cellStyle = styles[ExcelStyles.General]
        SomColumns.vehicleRcRegDate -> rb.cell(item.vehicleRcRegDate).cellStyle = styles[ExcelStyles.Date]
        SomColumns.vehicleRcRegTime -> rb.cell(item.vehicleRcRegTime).cellStyle = styles[ExcelStyles.General]
        SomColumns.ourTripFlag -> rb.cell(item.ourTripFlag).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttCodeSAP -> rb.cell(item.ttCodeSAP).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttCodeNQ -> rb.cell(item.ttCodeNQ).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttSending -> rb.cell(item.ttSending).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttArrival -> rb.cell(item.ttArrival).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttVisitingWayPlan -> rb.cell(item.ttVisitingWayPlan).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttVisitingWayFact -> rb.cell(item.ttVisitingWayFact).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttArriveDatePlan -> rb.cell(item.ttArriveDatePlan).cellStyle = styles[ExcelStyles.Date]
        SomColumns.ttArriveTimePlan -> rb.cell(item.ttArriveTimePlan).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttArriveDateFact -> rb.cell(item.ttArriveDateFact).cellStyle = styles[ExcelStyles.Date]
        SomColumns.ttArriveTimeFact -> rb.cell(item.ttArriveTimeFact).cellStyle = styles[ExcelStyles.General]
        SomColumns.ttScanPalletDate -> rb.cell(item.ttScanPalletDate).cellStyle = styles[ExcelStyles.Date]
        SomColumns.ttScanPalletTime -> rb.cell(item.ttScanPalletTime).cellStyle = styles[ExcelStyles.General]
        SomColumns.charaterDelivery -> rb.cell(item.charaterDelivery).cellStyle = styles[ExcelStyles.General]
        SomColumns.rnAndCharaterDelivery -> rb.cell(item.rnAndCharaterDelivery).cellStyle = styles[ExcelStyles.General]
        SomColumns.deliveryInHourGPSStatus -> rb.cell(item.deliveryInHourGPSStatus).cellStyle = styles[ExcelStyles.General]
        SomColumns.deliveryInHourPalletStatus -> rb.cell(item.deliveryInHourPalletStatus).cellStyle = styles[ExcelStyles.General]
        SomColumns.carInTime -> rb.cell(item.carInTime?.div(100.0)).cellStyle = styles[ExcelStyles.Percent]
        SomColumns.carInGPS -> rb.cell(item.carInGPS?.div(100.0)).cellStyle = styles[ExcelStyles.Percent]
        SomColumns.deliveryInHourPlan -> rb.cell(item.deliveryInHourPlan?.div(100.0)).cellStyle = styles[ExcelStyles.Percent]
        SomColumns.temperaturePlan -> rb.cell(item.temperaturePlan?.div(100.0)).cellStyle = styles[ExcelStyles.Percent]
        SomColumns.deliveryInPlan -> rb.cell(item.deliveryInPlan?.div(100.0)).cellStyle = styles[ExcelStyles.Percent]
        SomColumns.temperature -> rb.cell(item.temperature?.div(100.0)).cellStyle = styles[ExcelStyles.Percent]
        SomColumns.tempStatus -> rb.cell(item.tempStatus).cellStyle = styles[ExcelStyles.General]
        SomColumns.tripsCount -> rb.cell(item.tripsCount).cellStyle = styles[ExcelStyles.General]
        SomColumns.pointsCount -> rb.cell(item.pointsCount).cellStyle = styles[ExcelStyles.General]
    }
}