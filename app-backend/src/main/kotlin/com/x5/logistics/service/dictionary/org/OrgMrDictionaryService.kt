package com.x5.logistics.service.dictionary.org

import com.x5.logistics.data.dictionary.org.AtpLogTable
import com.x5.logistics.data.dictionary.org.MrEntity
import com.x5.logistics.data.dictionary.org.MrLogEntity
import com.x5.logistics.data.dictionary.org.MrLogTable
import com.x5.logistics.data.dictionary.org.MrTable
import com.x5.logistics.data.dictionary.org.TerritoryEntity
import com.x5.logistics.data.dictionary.org.TerritoryTable
import com.x5.logistics.repository.aliasOnlyExpression
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.castToLong
import com.x5.logistics.repository.refreshOrgUnit
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryColumn
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryColumnFilter
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryCreateLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryCreateRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryDeleteLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryFilters
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryItemDto
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryListRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryPagedResponse
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionarySelects
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryUpdateLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryUpdateRequest
import com.x5.logistics.service.RowBuilder
import com.x5.logistics.service.getStyles
import com.x5.logistics.service.streamWorkbook
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import com.x5.logistics.util.getLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.jetbrains.exposed.sql.AbstractQuery
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.addLogger
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.booleanLiteral
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.notExists
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.io.InputStream
import java.time.Instant

@Service
class OrgMrDictionaryService {

    val log = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    suspend fun getList(req: OrgMrDictionaryListRequest): OrgMrDictionaryPagedResponse = newSuspendedTransaction {
        if (logQuery) addLogger(StdOutSqlLogger)

        val atpLogSubQuery = AtpLogTable.atpLogMrMinDateSubquery
        val queryWithFilters = MrLogTable.mrLogSubqueryAlias
            .join(atpLogSubQuery, JoinType.LEFT, MrLogTable.mrLogSubqueryAlias[MrLogTable.mrId], atpLogSubQuery[AtpLogTable.mrId])
            .select(MrLogTable.mrTableMrId.aliasOnlyExpression())
            .applyFiltersAndSort(req)
            .andWhere { MrLogTable.isCurrent.aliasOnlyExpression() eq booleanLiteral(true) }
        val count = queryWithFilters.count()

        val actualMrIds = queryWithFilters
            .limit(req.pageSize, (req.pageNumber * req.pageSize).toLong())
            .map {
                it[MrLogTable.mrTableMrId.aliasOnlyExpression()].value
            }

        val query = MrLogTable.mrLogSubqueryAlias
            .join(atpLogSubQuery, JoinType.LEFT, MrLogTable.mrLogSubqueryAlias[MrLogTable.mrId], atpLogSubQuery[AtpLogTable.mrId])
            .select(
                MrLogTable.mrLogSubqueryAlias.columns
                + MrLogTable.mrTableMrId.aliasOnlyExpression()
                + MrLogTable.mrName.aliasOnlyExpression()
                + MrLogTable.terTableTerId.aliasOnlyExpression()
                + MrLogTable.terName.aliasOnlyExpression()
                + MrLogTable.minStartDate.aliasOnlyExpression()
                + MrLogTable.endDate.aliasOnlyExpression()
                + MrLogTable.isCurrent.aliasOnlyExpression()
                + AtpLogTable.mrLifeStart
            )
            .where {
                MrLogTable.mrLogSubqueryAlias[MrLogTable.mrTableMrId].castToLong() inList actualMrIds
            }
        val items = query.toDto(MrLogTable.mrLogSubqueryAlias)
            .groupBy { it.mrId }
            .mapValues { entry ->
                entry.value
                    .first { it.isCurrent }
                    .copy(children = entry.value.map { OrgMrDictionaryItemDto.Child.of(it) }
                        .filter { it.id != null }
                        .sortedBy { it.startDate }
                        .reversed())
            }

        OrgMrDictionaryPagedResponse(
            count = count,
            pageNumber = req.pageNumber,
            pageSize = req.pageSize,
            items = actualMrIds.mapNotNull { items[it] }
        )
    }

    suspend fun createMr(req: OrgMrDictionaryCreateRequest, author: String): OrgMrDictionaryItemDto {
        validateCreateUpdateRequest(req.mrName, terId = req.terId.toInt())
        val mr = newSuspendedTransaction {
            MrEntity.new {
                name = req.mrName
                createdBy = author
                createdAt = Instant.now()
                updatedBy = author
                updatedAt = Instant.now()
            }
        }
        return createLogEntry(
            OrgMrDictionaryCreateLogEntryRequest(
                mrId = mr.id.value,
                terId = req.terId,
                startDate = req.startDate,
            ),
            author
        )
    }

    suspend fun updateMr(req: OrgMrDictionaryUpdateRequest, author: String): OrgMrDictionaryItemDto {
        validateCreateUpdateRequest(req.mrName, mrId = req.mrId)
        newSuspendedTransaction {
            MrEntity.findByIdAndUpdate(req.mrId) {
                it.name = req.mrName
                it.updatedBy = author
                it.updatedAt = Instant.now()
            }
        }
        return getItemById(req.mrId)
    }

    suspend fun createLogEntry(req: OrgMrDictionaryCreateLogEntryRequest, author: String): OrgMrDictionaryItemDto {
        validateCreateLogEntryRequest(req)

        newSuspendedTransaction {
            MrLogEntity.new {
                this.mr = MrEntity.findById(req.mrId)!!
                this.territory = TerritoryEntity.findById(req.terId.toInt())!!
                this.startDate = req.startDate
                this.createdBy = author
                this.createdAt = Instant.now()
                this.updatedBy = author
                this.updatedAt = Instant.now()
            }
        }
        return getItemById(req.mrId)
    }

    suspend fun updateLogEntry(req: OrgMrDictionaryUpdateLogEntryRequest, author: String): OrgMrDictionaryItemDto {
        validateUpdateLogEntryRequest(req)
        newSuspendedTransaction {
            val logEntry = MrLogEntity.find {
                (MrLogTable.deleted eq false) and
                        (MrLogTable.id eq req.id) and
                        (MrLogTable.mrId eq req.mrId)
            }.firstOrNull()
                ?: throw IllegalArgumentException("Связка МР-ТЕР с id=${req.id} не соответствует МР с id=${req.mrId}")
            logEntry.territory = TerritoryEntity.findById(req.terId.toInt())!!
            logEntry.startDate = req.startDate
            logEntry.updatedBy = author
            logEntry.updatedAt = Instant.now()
        }
        return getItemById(req.mrId)
    }

    suspend fun deleteLogEntry(req: OrgMrDictionaryDeleteLogEntryRequest, author: String): OrgMrDictionaryItemDto {
        val logEntry = newSuspendedTransaction {
            checkMrLog(req.id)
            MrLogEntity.findById(req.id)!!.apply {
                deleted = true
                updatedBy = author
                updatedAt = Instant.now()
            }
        }
        return getItemById(logEntry.mr.id.value)
    }

    suspend fun getFilters(): OrgMrDictionaryFilters = newSuspendedTransaction {
        val mrDeferred = coroutineScope {
            async(Dispatchers.IO) {
                val query = MrTable
                    .select(MrTable.id, MrTable.name)
                    .where { MrTable.deleted eq false }
                    .withDistinct()
                    .orderBy(MrTable.name)
                if (logQuery) log.info(query.prepareSQL(QueryBuilder(false)))
                query.mapNotNull { it[MrTable.name] }
            }
        }
        val terDeferred = coroutineScope {
            async(Dispatchers.IO) {
                val query = MrLogTable.mrLogSubqueryAlias
                    .select(MrLogTable.terName.aliasOnlyExpression())
                    .where {
                        (MrLogTable.isCurrent.aliasOnlyExpression() eq booleanLiteral(true)) and
                                (MrLogTable.deleted.aliasOnlyExpression() eq booleanLiteral(false))
                    }
                    .withDistinct()
                    .orderBy(MrLogTable.terName)
                if (logQuery) log.info(query.prepareSQL(QueryBuilder(false)))
                query.mapNotNull { it[MrLogTable.terName.aliasOnlyExpression()] }
            }
        }
        OrgMrDictionaryFilters(
            mrName = mrDeferred.await(),
            terName = terDeferred.await()
        )
    }

    suspend fun getSelects(): OrgMrDictionarySelects = newSuspendedTransaction {
        OrgMrDictionarySelects(
            terName = TerritoryEntity.find { TerritoryTable.deleted eq false }.map {
                OrgMrDictionarySelects.Territory(
                    id = it.id.value.toLong(),
                    name = it.name
                )
            }
        )
    }

    suspend fun export(req: OrgMrDictionaryListRequest): InputStream {
        val column = OrgMrDictionaryColumn.entries
        var page: List<OrgMrDictionaryItemDto>
        val size = getList(req.copy(pageNumber = 0, pageSize = 1)).count.toInt()
        val out = streamWorkbook {
            val styles = getStyles()
            sheet("Справочник МР") {
                style {
                    alignment = HorizontalAlignment.CENTER
                    verticalAlignment = VerticalAlignment.CENTER
                }
                header {
                    column.forEach { column ->
                        head(column.columnTitle)
                    }
                }
                for (pageNumber in 0..(size / EXPORT_BATCH_SIZE)) {
                    runBlocking {
                        page = getList(req.copy(pageNumber = pageNumber, pageSize = EXPORT_BATCH_SIZE)).items
                    }
                    page.forEach { item ->
                        row {
                            column.forEach {
                                cellByColumnType(this, styles, it, item)
                            }
                        }
                    }
                    (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
                }
            }.autosize(column.size)
        }.toInputStream()
        return out
    }

    private suspend fun validateCreateLogEntryRequest(req: OrgMrDictionaryCreateLogEntryRequest) {
        checkMr(req.mrId)
        checkTerritory(req.terId.toInt())
        if (MrLogEntity.find {
                (MrLogTable.mrId eq req.mrId) and
                        (MrLogTable.deleted eq false) and
                        (MrLogTable.startDate eq req.startDate)
            }.count() > 0L) {
            throw IllegalArgumentException("Для МР с id=${req.mrId} уже создана связь от ${req.startDate}")
        }
    }

    private suspend fun getItemById(id: Long): OrgMrDictionaryItemDto {
        refreshOrgUnit()
        return getList(
            OrgMrDictionaryListRequest(
                pageNumber = 0,
                pageSize = 1, sort = listOf(),
                filters = listOf(
                    OrgMrDictionaryColumnFilter(
                        name = OrgMrDictionaryColumn.mrId,
                        condition = FilterCondition.equal,
                        value = listOf(id)
                    )
                )

            )
        ).items.first()
    }

    private suspend fun validateUpdateLogEntryRequest(req: OrgMrDictionaryUpdateLogEntryRequest) {
        checkMrLog(req.id)
        checkTerritory(req.terId.toInt())
        if (MrLogEntity.find {
                (MrLogTable.deleted eq false) and
                        (MrLogTable.mrId eq req.mrId) and
                        (MrLogTable.startDate eq req.startDate) and
                        (MrLogTable.id neq req.id)
            }.count() > 0
        ) {
            throw IllegalArgumentException("МР с id=${req.mrId} уже создана связь от ${req.startDate}")
        }
    }

    private suspend fun checkMr(mrId: Long) {
        if (MrEntity.find { (MrTable.id eq mrId) and (MrTable.deleted eq false) }.count() == 0L) {
            throw IllegalArgumentException("Не найден МР с id=${mrId}")
        }
    }

    private suspend fun checkMrLog(id: Int) {
        if (MrLogEntity.find { (MrLogTable.id eq id) and (MrLogTable.deleted eq false) }.count() == 0L) {
            throw IllegalArgumentException("Не найдена связь МР-ТЕР с id=${id}")
        }
    }

    private suspend fun validateCreateUpdateRequest(mrName: String, mrId: Long? = null, terId: Int? = null) {
        if (MrEntity.find {
                (MrTable.name.lowerCase() eq mrName.lowercase()) and
                        (MrTable.deleted eq false) and
                        (MrTable.id neq mrId)
            }.count() > 0L
        ) {
            log.error("МР с именем $mrName уже существует")
            throw IllegalArgumentException("МР с именем $mrName уже существует")
        }
        if (terId != null) checkTerritory(terId)
    }

    private fun checkTerritory(terId: Int) {
        if (TerritoryEntity.find { (TerritoryTable.id eq terId) and (TerritoryTable.deleted eq false) }.count() == 0L) {
            throw IllegalArgumentException("Не найдена Территория с id=${terId}")
        }
    }

    private fun AbstractQuery<*>.toDto(mrLogSubquery: QueryAlias): List<OrgMrDictionaryItemDto> =
        with(MrLogTable) {
            map {
                OrgMrDictionaryItemDto(
                    logId = it[mrLogSubquery[id]]?.value,
                    mrId = it[mrTableMrId.aliasOnlyExpression()]?.value,
                    mrName = it[mrName.aliasOnlyExpression()],
                    terId = it[terTableTerId.aliasOnlyExpression()]?.value?.toLong(),
                    terName = it[terName.aliasOnlyExpression()],
                    mrStartDate = it[AtpLogTable.mrLifeStart],
                    startDate = it[mrLogSubquery[startDate]],
                    endDate = it[endDate.aliasOnlyExpression()],
                    updatedBy = it[mrLogSubquery[updatedBy]],
                    updatedAt = it[mrLogSubquery[updatedAt]],
                    children = listOf(),
                    isCurrent = it[isCurrent.aliasOnlyExpression()]
                )
            }
        }

    private fun Query.applyFiltersAndSort(req: OrgMrDictionaryListRequest) = apply {
        req.filters.forEach { filter ->
            andWhere {
                buildFilterPredicate(
                    condition = filter.condition,
                    values = filter.value,
                    type = filter.name.type,
                    exposedExpression = filter.name.exposedExpression,
                    strictFilter = false
                )
            }
        }
        if (req.sort.isEmpty()) {
            applyDefaultSort()
        } else {
            applySort(req)
        }
    }

    private fun Query.applySort(req: OrgMrDictionaryListRequest) = apply {
        orderBy(
            *req.sort.map {
                it.column.exposedExpression to if (it.asc) SortOrder.ASC else SortOrder.DESC
            }.toTypedArray()
        )
    }

    private fun Query.applyDefaultSort() = orderBy(
        Case()
            .When(
                notExists(
                    MrLogTable
                        .select(intLiteral(1))
                        .where {
                            (MrLogTable.mrId.aliasOnlyExpression().castToLong() eq MrLogTable.mrTableMrId.aliasOnlyExpression().castToLong()) and
                                    (MrLogTable.deleted.aliasOnlyExpression() eq booleanLiteral(false))
                        }
                ),
                intLiteral(1)
            )
            .When(
                MrLogTable.minStartDate.aliasOnlyExpression() greater AtpLogTable.mrLifeStart.delegate,
                intLiteral(2)
            )
            .Else(intLiteral(3)) to SortOrder.ASC,
        MrLogTable.mrTableMrId.aliasOnlyExpression() to SortOrder.ASC
    )

    private fun cellByColumnType(
        rb: RowBuilder,
        styles: Map<String, CellStyle>,
        column: OrgMrDictionaryColumn,
        item: OrgMrDictionaryItemDto
    ) = when (column) {
        OrgMrDictionaryColumn.mrId -> rb.cell(item.mrId).cellStyle = styles["general"]
        OrgMrDictionaryColumn.mrName -> rb.cell(item.mrName).cellStyle = styles["general"]
        OrgMrDictionaryColumn.terName -> rb.cell(item.terName).cellStyle = styles["general"]
        OrgMrDictionaryColumn.startDate -> rb.cell(item.startDate).cellStyle = styles["date"]
        OrgMrDictionaryColumn.endDate -> rb.cell(item.endDate).cellStyle = styles["date"]
        OrgMrDictionaryColumn.updatedBy -> rb.cell(item.updatedBy).cellStyle = styles["general"]
    }

}