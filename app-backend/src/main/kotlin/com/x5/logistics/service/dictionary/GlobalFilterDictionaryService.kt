package com.x5.logistics.service.dictionary

import com.x5.logistics.repository.RepairShopsDictionaryRepository
import com.x5.logistics.repository.dictionary.GlobalFilterDictionaryRepository
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.dictionary.GlobalFilterDictionaryValuesDto
import org.springframework.stereotype.Service

@Service
class GlobalFilterDictionaryService(
    val repo: GlobalFilterDictionaryRepository,
    val repshopsRepo: RepairShopsDictionaryRepository,
) {
    fun getGlobalFilterDictionary(): GlobalFilterDictionaryValuesDto = repo.getGlobalFilterDictionary()
        .apply {
            territory.children.add(LabeledValue("Не определено", -1))
            mr.children.add(LabeledValue("Не определено", -1))
            atp.children.add(LabeledValue("Не определено", -1))
            mvzType.children.add(LabeledValue("Не определено", "Не определено"))
        }

    fun getGlobalRepshopsDictionary(): List<LabeledValue> =
        repshopsRepo.getActiveRepairShops() + LabeledValue("Не определено", -1)
}
