package com.x5.logistics.service.charting

import com.x5.logistics.repository.charting.desktop.ChartDesktopRepo
import com.x5.logistics.repository.charting.desktop.ChartWidgetRepo
import com.x5.logistics.rest.dto.charting.desktop.ChartDesktopDto
import com.x5.logistics.rest.dto.charting.desktop.ChartDesktopInfoDto
import com.x5.logistics.rest.dto.charting.desktop.ChartNewDesktopDto
import com.x5.logistics.rest.exception.WrongRequestDataException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ChartDesktopService(
    private val desktopRepo: ChartDesktopRepo,
    private val widgetRepo: ChartWidgetRepo
) {
    fun getAllByUser(user: String): List<ChartDesktopInfoDto> = desktopRepo.getAllByUser(user)
        .sortedBy { it.name }
        .map { ChartDesktopInfoDto(
            id = it.id,
            name = it.name!!,
            createDate = it.createDate,
            updateDate = it.updateDate
        ) }

    fun getById(id: Long): ChartDesktopDto = desktopRepo.getById(id).copy(
        widgets = widgetRepo.getAllByDesktop(id)
    )

    @Transactional
    fun new(desktop: ChartNewDesktopDto): ChartDesktopDto {
        if (desktopRepo.getCountQuery(desktop.owner) >= 50)
            throw WrongRequestDataException("Desktop count is greater than 50 for owner ${desktop.owner}")

        val id = desktopRepo.new(desktop)
        return desktopRepo.getById(id)
    }

    @Transactional
    fun update(desktop: ChartDesktopDto): ChartDesktopDto {
        val desktopFromDb = desktopRepo.getById(desktop.id)
        if (desktopFromDb.owner != desktop.owner)
            throw WrongRequestDataException("Desktop owner ${desktop.owner} is not request owner ${desktopFromDb.owner}")

        if (!desktop.widgets.isNullOrEmpty()) {
            val (new, update) = desktop.widgets.partition { it.id == null }
            if (new.isNotEmpty()) new.forEach { widgetRepo.new(it) }
            if (update.isNotEmpty()) update.forEach { widgetRepo.update(it) }
        }

        if (!desktop.widgetsToDelete.isNullOrEmpty()) desktop.widgetsToDelete.forEach { widgetRepo.delete(it) }
        desktopRepo.update(desktop.copy(name = desktop.name ?: desktopFromDb.name))

        return desktopRepo.getById(desktop.id).copy(
            widgets = widgetRepo.getAllByDesktop(desktop.id)
        )
    }

    @Transactional
    fun delete(id: Long, user: String) {
        widgetRepo.deleteAllByDesktop(id, user)
        desktopRepo.delete(id, user)
    }
}
