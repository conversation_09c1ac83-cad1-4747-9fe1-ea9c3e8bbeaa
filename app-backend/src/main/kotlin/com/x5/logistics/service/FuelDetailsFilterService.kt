package com.x5.logistics.service

import com.x5.logistics.repository.fuel.FuelDetailsRepo
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.fuel.details.FuelDetailsMassFilterReq
import com.x5.logistics.rest.dto.fuel.details.FuelDetailsReq
import com.x5.logistics.util.checkFilterValues
import com.x5.logistics.util.getLogger
import org.springframework.stereotype.Service

@Service
class FuelDetailsFilterService(
    val repo: FuelDetailsRepo
) {
    val log = getLogger()

    fun getFilterValues(req: FuelDetailsMassFilterReq): List<LabeledValue> {
        val fuelDetailsReq = FuelDetailsReq(
            pageNumber = null,
            pageSize = 0,
            from = req.from,
            to = req.to,
            geoFilter = req.geoFilter,
            columns = req.columns,
            sort = listOf(),
            filters = req.filters,
            granularity = null
        )
        val result = repo.getFilterValues(req.request, fuelDetailsReq)
        return result.map { LabeledValue(it, it) }
    }

    fun getMassFilterValues(req: FuelDetailsMassFilterReq): MassFilterResp {
        val fuelDetailsReq = FuelDetailsReq(
            pageNumber = null,
            pageSize = 0,
            from = req.from,
            to = req.to,
            geoFilter = req.geoFilter,
            columns = req.columns,
            sort = listOf(),
            filters = req.filters,
            granularity = null
        )
        val values = repo.getFilterValues(req.request, fuelDetailsReq)
        val userValues = req.request.value
        return checkFilterValues(userValues, values)
    }
}
