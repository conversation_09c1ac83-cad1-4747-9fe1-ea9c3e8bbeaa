package com.x5.logistics.service.dictionary.org

import com.x5.logistics.rest.dto.dictionary.DictionaryInfoDto
import com.x5.logistics.service.dictionary.DictionaryUpdateInfo
import com.x5.logistics.util.getLogger
import jakarta.persistence.EntityManager
import jakarta.persistence.Tuple
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.sql.Timestamp

@Suppress("UNCHECKED_CAST")
@Service
class OrgDictionaryInfoService(val em: EntityManager) : DictionaryUpdateInfo {

    val log = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    override fun getLastUpdateInfo(): DictionaryInfoDto {
        val query = """
            select updateat, author
            from ((select updated_at as updateat, updated_by as author
             from mvz_log
             where updated_by != 'system'
             order by updated_at desc nulls last
             limit 1)
            union
            (select updated_at as updateat, updated_by as author
             from mvz_codes
             where updated_by != 'system'
             order by updated_at desc nulls last
             limit 1)
            union
            (select updated_at as updateat, updated_by as author
             from atp_log
             where updated_by != 'system'
             order by updated_at desc nulls last
             limit 1)
            union
            (select updated_at as updateat, updated_by as author
             from atp
             where updated_by != 'system'
             order by updated_at desc nulls last
             limit 1)
            union
            (select updated_at as updateat, updated_by as author
             from mr_log
             where updated_by != 'system'
             order by updated_at desc nulls last
             limit 1)
            union
            (select updated_at as updateat, updated_by as author
             from macro_region
             where updated_by != 'system'
             order by updated_at desc nulls last
             limit 1)
            union
            (select updated_at as updateat, updated_by as author
             from territory
             where updated_by != 'system'
             order by updated_at desc nulls last
             limit 1)) _
            order by updateat desc nulls last
            limit 1
        """.trimIndent()
        val tupleRes = (em.createNativeQuery(query, Tuple::class.java)
            .resultList as List<Tuple>).firstOrNull()
        return DictionaryInfoDto(
            name = "unit",
            updatedBy = tupleRes?.get("author") as String?,
            updatedAt = (tupleRes?.get("updateat") as Timestamp?)?.toLocalDateTime(),
        )
    }
}
