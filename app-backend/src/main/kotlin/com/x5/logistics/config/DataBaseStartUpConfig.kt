package com.x5.logistics.config

import com.x5.logistics.ActiveDB
import com.x5.logistics.DataBaseConfigurations
import com.x5.logistics.getSingleConnectionHikariDataSource
import com.x5.logistics.rest.controller.DictionaryController
import liquibase.Liquibase
import liquibase.database.DatabaseFactory
import liquibase.database.jvm.JdbcConnection
import liquibase.resource.ClassLoaderResourceAccessor
import org.springframework.context.ApplicationListener
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.stereotype.Component

@Component
class DataBaseStartUpConfig(
    val dataBaseConfigurations: DataBaseConfigurations,
    val dictionaryController: DictionaryController
) : ApplicationListener<ContextRefreshedEvent> {
    override fun onApplicationEvent(event: ContextRefreshedEvent) {
        dataBaseConfigurations.dbConfigurations.forEach { (_, config) ->
            val changeLog = "/db/changelog/master.xml"
            val resourceAccessor = ClassLoaderResourceAccessor()
            val hcpSource = getSingleConnectionHikariDataSource(config).connection
            val dataBaseConnection = JdbcConnection(hcpSource)
            val dataBase = DatabaseFactory.getInstance().findCorrectDatabaseImplementation(dataBaseConnection)
            dataBase.databaseChangeLogTableName = "x5_db_migration_log"
            dataBase.databaseChangeLogLockTableName = "x5_db_migration_lock"
            val liquibase = Liquibase(changeLog, resourceAccessor, dataBase)
            liquibase.update()
            hcpSource.close()
        }
        val allSourcesInfo = dataBaseConfigurations.dbConfigurations.map { (name, config) ->
            val hcpSource = getSingleConnectionHikariDataSource(config).connection
            val resultSet = JdbcConnection(hcpSource)
                .prepareStatement("SELECT max(switch_at) FROM db_status;")
                .executeQuery()
            resultSet.next()
            val info = resultSet.getTimestamp(1)
            hcpSource.close()
            name to info
        }
        ActiveDB.name = allSourcesInfo.maxByOrNull { it.second }!!.first
        dictionaryController.init() //HACK для старта обновления словаря только после определения активной БД
    }
}
