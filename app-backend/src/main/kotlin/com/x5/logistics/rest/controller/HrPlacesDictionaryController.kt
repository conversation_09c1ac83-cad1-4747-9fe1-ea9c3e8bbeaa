package com.x5.logistics.rest.controller

import com.x5.logistics.rest.dto.PagedResponse
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesCreateReq
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesDeleteReq
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesDeleteRes
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesDictRes
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesEditReq
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesLogGroupDto
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesLogReq
import com.x5.logistics.service.dictionary.hr.places.HrPlacesDictionaryService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URLEncoder
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
@RequestMapping("api/dictionary/hrplaces")
class HrPlacesDictionaryController(
    private val service: HrPlacesDictionaryService
) {
    val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss")

    @Tag(name = "Словарь Площадок - Итоговых подразделений")
    @Operation(summary = "Получение записей словаря.")
    @PostMapping("list")
    fun getHrPlacesLog(
        @RequestBody req: HrPlacesLogReq
    ): PagedResponse<HrPlacesLogGroupDto> =
        service.getHrPlacesLog(req)

    @Tag(name = "Словарь Площадок - Итоговых подразделений")
    @Operation(summary = "Экспорт записей словаря.")
    @SwaggerReqBody(
        description = "Запрос на экспорт словаря Площадок - Итоговых подразделений",
        content = [Content(schema = Schema(implementation = HrPlacesLogReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(type = "string", format = "binary")
                )]
            )
        ]
    )
    @PostMapping("/list/export")
    fun export(@RequestBody req: HrPlacesLogReq): ResponseEntity<Resource> {
        val fileName = "Справочник Площадки и итоговые подразделения ${dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))}.xlsx"

        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(service.export(req)))
    }

    @Tag(name = "Словарь Площадок - Итоговых подразделений")
    @Operation(summary = "Создание/изменения записи.")
    @PostMapping("edit")
    fun editHrPlacesLog(
        @RequestBody req: HrPlacesEditReq,
        token: JwtToken?
    ): HrPlacesLogGroupDto =
        service.editHrPlacesEntity(req, token.username)

    @Tag(name = "Словарь Площадок - Итоговых подразделений")
    @Operation(summary = "Создание/изменения записи.")
    @PostMapping("new")
    fun createHrPlacesLog(
        @RequestBody req: HrPlacesCreateReq,
        token: JwtToken?
    ): HrPlacesLogGroupDto? =
        service.createHrPlacesEntity(req, token.username)

    @Tag(name = "Словарь Площадок - Итоговых подразделений")
    @Operation(summary = "Удаление записи.")
    @PostMapping("delete")
    fun deleteHrPlacesLog(
        @RequestBody req: HrPlacesDeleteReq,
        token: JwtToken?
    ): HrPlacesDeleteRes {
        val updatedGroup = service.deleteHrPlacesEntity(req, token.username)
        return HrPlacesDeleteRes(
            fullDeleted = updatedGroup != null,
            updated = updatedGroup
        )
    }

    @Tag(name = "Словарь Площадок - Итоговых подразделений")
    @Operation(summary = "Получения словарных значений.")
    @GetMapping("dict")
    fun getDict(): HrPlacesDictRes =
        service.getHrPlacesDict()

    @Tag(name = "Словарь Площадок - Итоговых подразделений")
    @Operation(summary = "Получения словарных значений для фильтров.")
    @GetMapping("filters")
    fun getFilters(): HrPlacesDictRes =
        service.getHrPlacesFilters()
}
