package com.x5.logistics.rest.dto.dictionary.repair

import com.x5.logistics.data.dictionary.NhPlanDictionaryView
import com.x5.logistics.repository.ColumnType
import org.jetbrains.exposed.sql.ExpressionWithColumnType

enum class NhPlanDictionaryColumn(
    val columnTitle: String,
    val type: ColumnType,
    val exposedExpression: ExpressionWithColumnType<*>
) {
    vehicleId(
        columnTitle = "Id ТС",
        exposedExpression = NhPlanDictionaryView.vehicleId,
        type = ColumnType.NUMBER
    ),
    vehicleType (
        columnTitle = "Тип ТС",
        exposedExpression = NhPlanDictionaryView.type,
        type = ColumnType.STRING
    ),
    vehicleBrand(
        columnTitle = "Марка",
        exposedExpression = NhPlanDictionaryView.brand,
        type = ColumnType.STRING
    ),
    vehicleYear(
        columnTitle = "Год выпуска",
        exposedExpression = NhPlanDictionaryView.createYear,
        type = ColumnType.NUMBER
    ),
    vehicleTonnage(
        columnTitle = "Тоннаж",
        exposedExpression = NhPlanDictionaryView.tonnage,
        type = ColumnType.NUMBER
    ),
    planNh(
        columnTitle = "План НЧ",
        exposedExpression = NhPlanDictionaryView.planNh,
        type = ColumnType.NUMBER
    ),
    startDate(
        columnTitle = "Начало",
        exposedExpression = NhPlanDictionaryView.startDate,
        type = ColumnType.DATE
    ),
    endDate(
        columnTitle = "Конец",
        exposedExpression = NhPlanDictionaryView.endDate,
        type = ColumnType.DATE
    ),
    author(
        columnTitle = "Сотрудник",
        exposedExpression = NhPlanDictionaryView.author,
        type = ColumnType.STRING
    )
}
