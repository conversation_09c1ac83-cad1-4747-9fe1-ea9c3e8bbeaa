package com.x5.logistics.rest.dto.repair.modal.orders

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

@Schema(description = "Информация по набору заказов")
data class RepairModalOrdersBarData(

    @Schema(description = "Набор заказов")
    val orders: List<BigDecimal>?,

    @Schema(description = "Сумма заказов")
    val barSum: BigDecimal,

    @Schema(description = "Максимальная стоимость одного заказа")
    val maxOrder: BigDecimal
)
