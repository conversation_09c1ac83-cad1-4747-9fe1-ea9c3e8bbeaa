package com.x5.logistics.rest.dto.hr.dictionaries

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Данные об автоколонне ТС")
data class TsOrgUnitDto(

    @Schema(description = "Уникальный номер связки Автоколонна-МВЗ ТС")
    val uid: Int,

    @Schema(description = "Код автоколонны ТС")
    val orgUnitId: String,

    @Schema(description = "Наименование автоколонны ТС")
    val orgUnitName: String,

    @Schema(description = "Идентификатор МВЗ")
    val costPointId: String,

    @Schema(description = "Наименование МВЗ")
    val costPointName: String
)
