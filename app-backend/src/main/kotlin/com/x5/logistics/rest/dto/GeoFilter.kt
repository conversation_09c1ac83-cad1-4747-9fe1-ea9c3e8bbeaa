package com.x5.logistics.rest.dto

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Глобальный фильтр подразделений")
data class GeoFilter(
    @Schema(description = "Макрорегионы")
    val mr: List<Long>?,
    @Schema(description = "АТП")
    val atp: List<Long>?,
    @Schema(description = "МВЗ")
    val mvz: List<String>?,
    @Schema(description = "Торговая сеть АТП")
    val retailNetwork: List<String>?,
    @Schema(description = "Вид транспортировки")
    val atpType: List<String>?,
    @Schema(description = "Тип МВЗ")
    val mvzType: List<String?>?,
    @Schema(description = "Территория")
    val territory: List<Long>?,
)

val GeoFilter.isEmpty: Boolean
    get() = mr.isNullOrEmpty() && atp.isNullOrEmpty() && mvz.isNullOrEmpty() && retailNetwork.isNullOrEmpty() &&
            atpType.isNullOrEmpty() && mvzType.isNullOrEmpty() && territory.isNullOrEmpty()

val GeoFilter.containsOnlyMvz: Boolean
    get() = territory.isNullOrEmpty() && mr.isNullOrEmpty() && atp.isNullOrEmpty() && atpType.isNullOrEmpty() &&
            retailNetwork.isNullOrEmpty() && mvzType.isNullOrEmpty() && mvz?.isNotEmpty() == true