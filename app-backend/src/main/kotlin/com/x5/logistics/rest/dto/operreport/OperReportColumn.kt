package com.x5.logistics.rest.dto.operreport

import com.x5.logistics.data.OperationalReport
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.rest.util.FilterGroup
import com.x5.logistics.util.PRECISION_THRESHOLD
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.alias

const val WEAK_PRECISION_THRESHOLD = 0.05

@Suppress("EnumEntryNameCase", "EnumNaming", "EnumEntryName")
enum class OperReportColumn(
    val title: String,
    val group: FilterGroup,
    val filterable: Boolean,
    val type: ColumnType = ColumnType.STRING,
    val exposedExpression: ExpressionAlias<*>,
    val isAggregated: Boolean = false,
    val precision: Double = PRECISION_THRESHOLD
) {
    periodDay(
        title = "День",
        group = FilterGroup.string,
        filterable = false,
        type = ColumnType.DATE,
        exposedExpression = OperationalReport.vehicleDate.alias("vehicle_date"),
    ),

    periodReportWeek(
        title = "Неделя отчётная",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OperationalReport.reportingWeek,
    ),

    periodCalendarWeek(
        title = "Неделя календарная",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OperationalReport.week,
    ),

    periodMonth(
        title = "Месяц",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OperationalReport.month,
    ),

    periodQuarter(
        title = "Квартал",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OperationalReport.quarter,
    ),

    periodYear(
        title = "Год",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OperationalReport.year,
    ),

    vehicleLicense(
        title = "Гос номер машины",
        group = FilterGroup.stringSearch,
        filterable = true,
        exposedExpression = OperationalReport.vehicleLicense.alias("vehicle_license"),
    ),

    vehicleGroup(
        title = "Вид ТС",
        group = FilterGroup.string,
        filterable = true,
        exposedExpression = OperationalReport.vehicleGroup.alias("vehicle_group"),
    ),

    vehicleType(
        title = "Тип ТС",
        group = FilterGroup.string,
        filterable = true,
        exposedExpression = OperationalReport.vehicleType.alias("vehicle_type"),
    ),

    vehicleTonnage(
        title = "Тоннаж",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.vehicleTonnage.alias("vehicle_tonnage"),
    ),

    vehicleBrand(
        title = "Марка",
        group = FilterGroup.string,
        filterable = true,
        exposedExpression = OperationalReport.vehicleBrand.alias("vehicle_brand"),
    ),

    vehicleModel(
        title = "Модель",
        group = FilterGroup.string,
        filterable = true,
        exposedExpression = OperationalReport.vehicleModel.alias("vehicle_model"),
    ),

    vehicleCreateYear(
        title = "Год ввода в эксплуатацию",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.vehicleCreateYear.alias("vehicle_create_year"),
    ),

    mvz(
        title = "МВЗ ТС",
        group = FilterGroup.stringSearch,
        filterable = true,
        exposedExpression = OrganizationalUnitsTimelineTable.mvzId.alias("mvz_id"),
    ),

    mvzName(
        title = "Название МВЗ ТС",
        group = FilterGroup.stringSearch,
        filterable = true,
        exposedExpression = OrganizationalUnitsTimelineTable.mvzName.alias("mvz_name"),
    ),

    retailNetwork(
        title = "Торговая сеть АТП",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OrganizationalUnitsTimelineTable.retailNetwork.alias("retail_network"),
    ),

    ter(
        title = "Территория",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OrganizationalUnitsTimelineTable.territoryName.alias("territory_name"),
    ),

    mr(
        title = "Макрорегион",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OrganizationalUnitsTimelineTable.mrName.alias("mr_name"),
    ),

    atp(
        title = "АТП",
        group = FilterGroup.string,
        filterable = false,
        exposedExpression = OrganizationalUnitsTimelineTable.atpName.alias("atp_name"),
    ),

    vehicleCount(
        title = "Ср. кол-во ТС",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.vehicleCount,
        isAggregated = true,
    ),

    rcRetailNetwork(
        title = "Торговая сеть РЦ отгрузки",
        group = FilterGroup.string,
        filterable = true,
        exposedExpression = OperationalReport.rcRetailNetwork.alias("rc_retail_network"),
    ),

    rcName(
        title = "РЦ отгрузки",
        group = FilterGroup.stringSearch,
        filterable = true,
        exposedExpression = OperationalReport.rcName.alias("rc_name"),
    ),

    rcCode(
        title = "Код РЦ отгрузки ТОРГ",
        group = FilterGroup.stringSearch,
        filterable = true,
        exposedExpression = OperationalReport.idRcSap.alias("id_rc_sap"),
    ),

    logisticsRcName(
        title = "Логистический РЦ",
        group = FilterGroup.stringSearch,
        filterable = true,
        exposedExpression = OperationalReport.rcNameSapLogistics.alias(OperationalReport.rcNameSapLogistics.name),
    ),

    idLogisticsRcSAP(
        title = "Код логист. РЦ ТОРГ",
        group = FilterGroup.stringSearch,
        filterable = true,
        exposedExpression = OperationalReport.idRcSapLogistics.alias(OperationalReport.idRcSapLogistics.name),
    ),

    tripsCount(
        title = "Кол-во рейсов",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.tripsCount,
        isAggregated = true,
    ),

    pointsCount(
        title = "Кол-во доставок",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.pointsCountSum,
        isAggregated = true,
    ),

    charaterDelivery(
        title = "Цепочка поставки",
        group = FilterGroup.string,
        filterable = true,
        exposedExpression = OperationalReport.charaterDelvr.alias(OperationalReport.charaterDelvr.name),
    ),

    rnAndCharaterDelivery(
        title = "ТС отгрузки, цепочка поставки",
        group = FilterGroup.string,
        filterable = true,
        exposedExpression = OperationalReport.rnAndCharaterDelivery.alias(OperationalReport.rnAndCharaterDelivery.name),
    ),

    ourTripText(
        title = "СТ/НТ",
        group = FilterGroup.string,
        filterable = true,
        exposedExpression = OperationalReport.ourTripText.alias("our_trip_text"),
        isAggregated = false,
    ),

    kipHours(
        title = "КИП, мч",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.kipHours,
        isAggregated = true,
    ),

    kipNoReserveHours(
        title = "КИП без резервных ПЛ, мч",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.kipNoReserveHours,
        isAggregated = true,
    ),

    kipShare(
        title = "КИП, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.kipShare,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    kipNoReserveShare(
        title = "КИП без резервных ПЛ, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.kipNoReserveShare,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    ktgShare(
        title = "КТГ, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.ktgShare,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    rgShare(
        title = "РГ, %",
        group = FilterGroup.number,
        filterable = true,
        type = ColumnType.NUMBER,
        exposedExpression = OperationalReport.rgShare,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    comeOffCount(
        title = "Кол-во сходов в день",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.comeOffCount,
        isAggregated = true,
    ),

    comeOff(
        title = "Сходы, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.comeOffPercent,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    carInTime(
        title = "Car in, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.carInTime,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    carInTimeOwn(
        title = "Car in СТ, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.carInTimeOwn,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    carInGPSOwn(
        title = "Car in по GPS СТ, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.carInGpsOwn,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    carInTimeHired(
        title = "Car in НТ, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.carInTimeHired,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    carInGPSHired(
        title = "Car in по GPS НТ, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.carInGpsHired,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    deliveryInPlan(
        title = "Доставка в часовое окно, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.deliveryInPlan,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),

    temperature(
        title = "Соблюдение темп. режима, %",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        filterable = true,
        exposedExpression = OperationalReport.temperature,
        isAggregated = true,
        precision = WEAK_PRECISION_THRESHOLD
    ),
}
