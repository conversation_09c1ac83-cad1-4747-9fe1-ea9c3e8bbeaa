package com.x5.logistics.rest.dto.fuel

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

@Schema(description = "Данные по ТС для модального виджета.")
data class FuelVehicleData(
    @Schema(description = "Гос. номер ТС.")
    val licenseNum: String,
    @Schema(description = "Единица оборудования.")
    val eqUnit: String,
    @Schema(description = "Марка ТС.")
    val brand: String,
    @Schema(description = "Значение топлива.")
    val value: BigDecimal
)
