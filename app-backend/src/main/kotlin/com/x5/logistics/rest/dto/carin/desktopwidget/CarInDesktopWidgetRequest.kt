package com.x5.logistics.rest.dto.carin.desktopwidget

import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.common.CharacterDeliveryForRetailNetwork
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

data class CarInDesktopWidgetRequest(
    val from: LocalDate,
    val to: LocalDate,
    val geoFilter: GeoFilter,
    val flagGPS: <PERSON><PERSON><PERSON>,
    @Schema(description = "Цепочки поставок для торговых сетей")
    val characterDeliveryForRetailNetwork: List<CharacterDeliveryForRetailNetwork>
)