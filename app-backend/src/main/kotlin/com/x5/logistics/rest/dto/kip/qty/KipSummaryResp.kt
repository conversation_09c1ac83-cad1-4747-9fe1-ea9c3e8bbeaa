package com.x5.logistics.rest.dto.kip.qty

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

@Schema(description = "Суммарные КИП данные.")
data class KipSummaryResp(
    @Schema(description = "КИП факт (методология).")
    val kipFactByMethod: Double,
    @Schema(description = "КИП факт (реальный).")
    val kipFactReal: Double,
    @Schema(description = "Без рейсов.")
    val noTrip: Double,
    @Schema(description = "Простой более 12 часов.")
    val downtime: Double,
    @Schema(description = "Ремонтные точки.")
    val repairPoints: Double,
    @Schema(description = "КИП тариф.")
    val kipTariff: Int,
    @Schema(description = "Всего ТС.")
    val vehicleTotal: Long,
    @Schema(description = "В ремонте.")
    val vehicleRepair: Double,
    @Schema(description = "КТГ.")
    val ktg: Double,
    @Schema(description = "Не подано в ТГ / Не готово.")
    val notReady: Double,
    @Schema(description = "РГ.")
    val rg: Double,
    @Schema(description = "Не подано в ТГ / Не готово (есть ПЛ).")
    val notReadyWithWB: Double,
    @Schema(description = "Нет ПЛ.")
    val noWB: Double,
    @Schema(description = "КТГ тариф.")
    val ktgTariff: Int
)
