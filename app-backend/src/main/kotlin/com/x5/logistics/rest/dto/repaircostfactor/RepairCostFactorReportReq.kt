package com.x5.logistics.rest.dto.repaircostfactor

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.repository.Granularity
import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос на детальный отчет по амортизации")
data class RepairCostFactorReportReq(
    @Schema(description = "Номер страницы")
    val pageNumber: Int?,

    @Schema(description = "Размер страницы")
    val pageSize: Int?,

    @Schema(description = "Дата начала периода")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val from: LocalDate,

    @Schema(description = "Дата конца периода")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val to: LocalDate,

    @Schema(description = "Глобальные фильтры")
    val geoFilter: GeoFilter,

    @Schema(description = "Список колонок")
    val columns: List<RepairCostFactorReportColumn>,

    @Schema(description = "Список сортировок")
    val sort: List<RepairCostFactorReportSortOrder>,

    @Schema(description = "Список фильтров")
    val filters: List<RepairCostFactorReportColumnFilter>,

    @Schema(description = "Гранулярность отчета")
    val granularity: Granularity?
)