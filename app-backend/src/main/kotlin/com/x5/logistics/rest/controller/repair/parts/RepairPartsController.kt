package com.x5.logistics.rest.controller.repair.parts

import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.repair.parts.RepairPartsColumn
import com.x5.logistics.rest.dto.repair.parts.RepairPartsColumnDto
import com.x5.logistics.rest.dto.repair.parts.RepairPartsDto
import com.x5.logistics.rest.dto.repair.parts.RepairPartsPagedResponse
import com.x5.logistics.rest.dto.repair.parts.RepairsPartsMassFilterReq
import com.x5.logistics.rest.dto.repair.parts.RepairsPartsReq
import com.x5.logistics.service.RepairPartsService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.core.io.Resource
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
@RequestMapping("/api/repair/parts")
class RepairPartsController(
    val service: RepairPartsService
) {
    private val log = getLogger()

    @Tag(name = "Ремонт - запчасти")
    @Operation(summary = "Запрос данных по запчастям - детальный отчет.")
    @SwaggerReqBody(
        description = "Запрос данных по запчастям - детальный отчет",
        content = [Content(schema = Schema(implementation = RepairsPartsReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = RepairPartsDto::class))]
            )
        ]
    )
    @PostMapping
    fun getDetailedReport(@RequestBody req: RepairsPartsReq): RepairPartsPagedResponse {
        return service.getReport(req)
    }

    @Tag(name = "Ремонт - запчасти")
    @Operation(summary = "Экспорт данных по запчастям - детальный отчет в Excel.")
    @SwaggerReqBody(
        description = "Экспорт данных по запчастям - детальный отчет в Excel",
        content = [Content(schema = Schema(implementation = RepairsPartsReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(type = "string", format = "binary")
                )]
            )
        ]
    )
    @PostMapping("/export")
    fun exportToXlsx(@RequestBody req: RepairsPartsReq, token: JwtToken?): ResponseEntity<Resource> {
        return service.exportToXlsx(req, token.username)
    }

    @Tag(name = "Ремонт - запчасти")
    @Operation(summary = "Получение названия столбцов на данные по запчастям - детальный отчет.")
    @GetMapping
    fun getDetailedReportColumns(): List<RepairPartsColumnDto> {
        return RepairPartsColumn.entries.asSequence()
            .filterNot { it == RepairPartsColumn.granularityPeriodCount }
            .map {
            RepairPartsColumnDto(
                label = when (it) {
                    RepairPartsColumn.vehicleYear -> "year"
                    else -> it.name
                },
                name = it.columnTitle,
                group = it.group.name,
                filterable = it.filterable
            )
        }.toList()
    }

    @Tag(name = "Ремонт - запчасти")
    @Operation(summary = "Запрос на получение доступных значений фильтра с учетом уже выбранных фильтров.")
    @SwaggerReqBody(
        description = "Запрос на получение доступных значений фильтров.",
        content = [Content(schema = Schema(implementation = RepairsPartsMassFilterReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = LabeledValue::class))]
            )
        ]
    )
    @PostMapping("/filter")
    fun getFilterValues(@RequestBody req: RepairsPartsMassFilterReq): List<LabeledValue> {
        log.debug("Repair parts filter [req={}]", req)
        return service.getFilterValues(req)
    }

    @Tag(name = "Ремонт - запчасти")
    @Operation(summary = "Запрос проверки данных для поиска по списку значений.")
    @SwaggerReqBody(
        description = "Запрос на получение доступных значений фильтров.",
        content = [Content(schema = Schema(implementation = RepairsPartsMassFilterReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = MassFilterResp::class))]
            )
        ]
    )
    @PostMapping("/massfilter")
    fun getMassFilter(@RequestBody req: RepairsPartsMassFilterReq): MassFilterResp {
        log.debug("Mass filter [req={}]", req)
        return service.getMassFilterValues(req)
    }
}
