package com.x5.logistics.rest.controller

import com.x5.logistics.rest.dto.constructor.ConstructorBiMeta
import com.x5.logistics.service.ConstructorBiService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/bi/")
public class ConstructorBiController(private val service: ConstructorBiService) {
    @GetMapping("/meta")
    fun readMeta(): ConstructorBiMeta =
        service.readBiMeta()
}
