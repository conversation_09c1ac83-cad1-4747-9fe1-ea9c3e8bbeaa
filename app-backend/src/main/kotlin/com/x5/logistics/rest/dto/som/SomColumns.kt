package com.x5.logistics.rest.dto.som

import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.som.SomTripsAndPointsFullTable
import com.x5.logistics.data.som.SomTripsTable
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.repository.castToString
import com.x5.logistics.rest.util.FilterGroup
import org.jetbrains.exposed.sql.ExpressionWithColumnType

const val WEAK_PRECISION_THRESHOLD = 0.05

@Suppress("EnumEntryName")
enum class SomColumns(
    val columnName: String,
    val type: ColumnType,
    val filterType: FilterGroup,
    val filterable: Boolean,
    val grouping: Boolean,
    val somTripsExp: ExpressionWithColumnType<out Any?>?,
    val somTripsAndPointsFullExp: ExpressionWithColumnType<out Any?>?,
    val percentType: Boolean = false,
    val precision: Double = WEAK_PRECISION_THRESHOLD
) {
    vehicleLicense(
        columnName = "Гос. номер ТС",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.gosNumTs,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.gosNumTs
    ),
    vehicleGroup(
        columnName = "Вид ТС",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.tdGroup,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.vehicleGroup
    ),
    vehicleType(
        columnName = "Тип ТС",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.tdType,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.vehicleType
    ),
    vehicleBrand(
        columnName = "Марка",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.tdBrand,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.vehicleBrand
    ),
    vehicleModel(
        columnName = "Модель",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.tdModel,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.vehicleModel
    ),
    vehicleTonnage(
        columnName = "Тоннаж",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.tdTonnage,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.vehicleTonnage
    ),
    vehicleCreateYear(
        columnName = "Год ввода в эксплуатацию",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.tdCreateYear,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.vehicleCreateYear
    ),
    vehicleVin(
        columnName = "VIN номер",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.tdFleetNum,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.fleetNum
    ),
    vehicleId(
        columnName = "Единица оборудования",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.equnr.castToString(),
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.equnr.castToString()
    ),
    trailerLicenseNum(
        columnName = "Гос. номер прицепа",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.gosNumTrailer,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.gosNumTrailer
    ),
    ter(
        columnName = "Территория",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = false,
        grouping = true,
        somTripsExp = OrganizationalUnitsTimelineTable.territoryName,
        somTripsAndPointsFullExp = OrganizationalUnitsTimelineTable.territoryName
    ),
    mr(
        columnName = "Макрорегион",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = false,
        grouping = true,
        somTripsExp = OrganizationalUnitsTimelineTable.mrName,
        somTripsAndPointsFullExp = OrganizationalUnitsTimelineTable.mrName
    ),
    atp(
        columnName = "АТП",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = false,
        grouping = true,
        somTripsExp = OrganizationalUnitsTimelineTable.atpName,
        somTripsAndPointsFullExp = OrganizationalUnitsTimelineTable.atpName
    ),
    mvz(
        columnName = "МВЗ ТС",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = false,
        grouping = true,
        somTripsExp = SomTripsTable.mvzId,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.mvzId
    ),
    mvzName(
        columnName = "Название МВЗ ТС",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = false,
        grouping = true,
        somTripsExp = OrganizationalUnitsTimelineTable.mvzName,
        somTripsAndPointsFullExp = OrganizationalUnitsTimelineTable.mvzName
    ),
    retailNetwork(
        columnName = "Торговая сеть АТП",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = false,
        grouping = true,
        somTripsExp = OrganizationalUnitsTimelineTable.retailNetwork,
        somTripsAndPointsFullExp = OrganizationalUnitsTimelineTable.retailNetwork
    ),
    atpType(
        columnName = "Вид деятельности АТП",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = false,
        grouping = true,
        somTripsExp = OrganizationalUnitsTimelineTable.atpType,
        somTripsAndPointsFullExp = OrganizationalUnitsTimelineTable.atpType
    ),
    mvzType(
        columnName = "Тип МВЗ",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = false,
        grouping = true,
        somTripsExp = OrganizationalUnitsTimelineTable.mvzType,
        somTripsAndPointsFullExp = OrganizationalUnitsTimelineTable.mvzType
    ),
    TMSRouteNum(
        columnName = "Номер рейса TMS",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.numTripTms.castToString(),
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.numTripTms.castToString()
    ),
    NQRouteNum(
        columnName = "Номер рейса NQ",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.numTripNq.castToString(),
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.numTripNq.castToString()
    ),
    retailNetworkOrder(
        columnName = "Торг. сеть РЦ отгрузки",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.rcRetailNetwork,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.rcRetailNetwork
    ),
    rcName(
        columnName = "РЦ отгрузки",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.rcName,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.rcName
    ),
    idRcSAP(
        columnName = "Код РЦ отгрузки ТОРГ",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.idRcSap,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.idRcSap
    ),
    logisticsRcName(
        columnName = "Логистический РЦ",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.rcNameSapLogistics,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.rcNameSapLogistics
    ),
    idLogisticsRcSAP(
        columnName = "Код логист. РЦ ТОРГ",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.idRcSapLogistics,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.idRcSapLogistics
    ),
    vehicleArriveDate(
        columnName = "План. дата прибытия на РЦ ТС",
        type = ColumnType.DATE,
        filterType = FilterGroup.date,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.dtPlanRcDate,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.dtPlanRcDate
    ),
    vehicleArriveTime(
        columnName = "План. время прибытия на РЦ ТС",
        type = ColumnType.TIME,
        filterType = FilterGroup.time,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.timePlanRc,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.timePlanRc
    ),
    vehicleRcRegDate(
        columnName = "Дата регистрации ТС на РЦ в NQ",
        type = ColumnType.DATE,
        filterType = FilterGroup.date,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.dtRegRcDate,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.dtRegRcDate
    ),
    vehicleRcRegTime(
        columnName = "Время регистрации ТС на РЦ в NQ",
        type = ColumnType.TIME,
        filterType = FilterGroup.time,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.timeRegRc,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.timeRegRc
    ),
    ourTripFlag(
        columnName = "СТ/НТ",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.ourTripText,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.ourTripText
    ),
    ttCodeSAP(
        columnName = "Код ТТ ТОРГ",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.codeSap
    ),
    ttCodeNQ(
        columnName = "Код ТТ NQ",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.codeNq.castToString()
    ),
    ttSending(
        columnName = "ТТ отправки",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.nameTtSending
    ),
    ttArrival(
        columnName = "ТТ прибытия",
        type = ColumnType.STRING,
        filterType = FilterGroup.stringSearch,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.nameTt
    ),
    ttVisitingWayPlan(
        columnName = "План. порядок посещения точки",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.orderPointPlan,
        precision = 0.0
    ),
    ttVisitingWayFact(
        columnName = "Факт. порядок посещения точки",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.orderPointFact,
        precision = 0.0
    ),
    ttArriveDatePlan(
        columnName = "План. дата прибытия на ТТ",
        type = ColumnType.DATE,
        filterType = FilterGroup.date,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.dtPlanArriveDate
    ),
    ttArriveTimePlan(
        columnName = "План. время прибытия на ТТ",
        type = ColumnType.TIME,
        filterType = FilterGroup.time,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.timePlanArrive
    ),
    ttArriveDateFact(
        columnName = "Факт. дата прибытия на ТТ",
        type = ColumnType.DATE,
        filterType = FilterGroup.date,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.dtFactArriveDate
    ),
    ttArriveTimeFact(
        columnName = "Факт. время прибытия на ТТ",
        type = ColumnType.TIME,
        filterType = FilterGroup.time,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.timeFactArrive
    ),
    ttScanPalletDate(
        columnName = "Дата скан. 1-й паллеты на ТТ",
        type = ColumnType.DATE,
        filterType = FilterGroup.date,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.dtScanFirstPalletDate
    ),
    ttScanPalletTime(
        columnName = "Время скан. 1-й паллеты на ТТ",
        type = ColumnType.TIME,
        filterType = FilterGroup.time,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.timeScanFirstPallet
    ),
    charaterDelivery(
        columnName = "Цепочка поставки",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.charaterDelvr,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.charaterDelvr
    ),
    rnAndCharaterDelivery(
        columnName = "ТС отгрузки, цепочка поставки",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.rnAndCharaterDelivery,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.rnAndCharaterDelivery
    ),
    deliveryInHourGPSStatus(
        columnName = "Статус доставки в часовое окно по GPS",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.statusDelvrIntimeGps
    ),
    deliveryInHourPalletStatus(
        columnName = "Статус доставки в часовое окно по паллетам",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = null,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.statusDelvrIntimePallet
    ),
    carInTime(
        columnName = "Car in, %",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = false,
        somTripsExp = null,
        somTripsAndPointsFullExp = null,
        percentType = true,
    ),
    carInGPS(
        columnName = "Car in по GPS, %",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = false,
        somTripsExp = null,
        somTripsAndPointsFullExp = null,
        percentType = true,
    ),
    deliveryInHourPlan(
        columnName = "Цель доставка в часовое окно, %",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = false,
        somTripsExp = null,
        somTripsAndPointsFullExp = null,
        percentType = true,
    ),
    temperaturePlan(
        columnName = "Цель соблюдение температурного режима, %",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = false,
        somTripsExp = null,
        somTripsAndPointsFullExp = null,
        percentType = true,
    ),
    deliveryInPlan(
        columnName = "Доставка в часовое окно, %",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = false,
        somTripsExp = null,
        somTripsAndPointsFullExp = null,
        percentType = true,
    ),
    temperature(
        columnName = "Соблюдение темп. режима, %",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = false,
        somTripsExp = null,
        somTripsAndPointsFullExp = null,
        percentType = true,
    ),
    tempStatus(
        columnName = "Статус по температуре",
        type = ColumnType.STRING,
        filterType = FilterGroup.string,
        filterable = true,
        grouping = true,
        somTripsExp = SomTripsTable.statusTDescription,
        somTripsAndPointsFullExp = SomTripsAndPointsFullTable.statusTDescription
    ),
    tripsCount(
        columnName = "Кол-во рейсов",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = false,
        somTripsExp = null,
        somTripsAndPointsFullExp = null
    ),
    pointsCount(
        columnName = "Кол-во доставок",
        type = ColumnType.NUMBER,
        filterType = FilterGroup.number,
        filterable = true,
        grouping = false,
        somTripsExp = null,
        somTripsAndPointsFullExp = null
    ),
}