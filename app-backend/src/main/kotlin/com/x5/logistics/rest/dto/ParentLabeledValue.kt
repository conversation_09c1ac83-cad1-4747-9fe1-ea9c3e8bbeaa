package com.x5.logistics.rest.dto

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Пара название-значение с дочерними элементами.")
data class ParentLabeledValue(
    @Schema(description = "Название.")
    val label: String,
    @Schema(description = "Значение.")
    val value: Any?,
    @Schema(description = "Список пар название-значение.")
    val children: MutableList<LabeledValue>,
)
