package com.x5.logistics.rest.dto.dictionary.rr

import com.fasterxml.jackson.annotation.JsonProperty
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.SortItem
import com.x5.logistics.rest.dto.dictionary.kip.maxLimit

data class RrDictionaryGroupsListReq(
    val pageSize: Int = maxLimit,
    val pageNumber: Int = 0,
    val sort: List<SortItem<RrDictionaryGroupField>> = emptyList(),
    val filters: List<RrDictionaryGroupFilter> = emptyList(),
    val year: Int
)

enum class RrDictionaryGroupField {
    @JsonProperty("year") YEAR,
    @JsonProperty("atpName") ATP_NAME,
    @JsonProperty("tonnage") TONNAGE,
    @JsonProperty("createdBy") CREATED_BY,
    @JsonProperty("createdAt") CREATED_AT
}

data class RrDictionaryGroupFilter(
    val name: RrDictionaryGroupFilterField,
    val condition: FilterCondition? = null,
    val value: List<Any>
)

enum class RrDictionaryGroupFilterField {
    @JsonProperty("atpId") ATP_ID,
    @JsonProperty("atpName") ATP_NAME,
    @JsonProperty("tonnage") TONNAGE,
    @JsonProperty("vrt_subtype_id") VRT_SUBTYPE_ID,
}
