package com.x5.logistics.rest.controller.dictionary.repair

import com.x5.logistics.rest.dto.PagedResponse
import com.x5.logistics.rest.dto.dictionary.repair.NhDictionaryCheckNewDto
import com.x5.logistics.rest.dto.dictionary.repair.NhDictionaryImportStatus
import com.x5.logistics.rest.dto.dictionary.repair.NhDictionaryInsertRequest
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanChildrenDictionaryDto
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDeleteRequest
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryDto
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryListRequest
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryUpdateRequest
import com.x5.logistics.rest.dto.dictionary.repair.NhPlanDictionaryValuesDto
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.service.dictionary.repair.NhPlanDictionaryService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.email
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.name
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.ArraySchema
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.net.URLEncoder
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
@RequestMapping("/api/dictionary/plannh")
class NhPlanDictionaryController(
    private val service: NhPlanDictionaryService
) {
    private val log = getLogger()
    val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss")

    @Tag(name = "Справочник плановых НЧ")
    @Operation(summary = "Получение справочника плановых нормочасов для марок/моделей")
    @SwaggerReqBody(
        description = "Запрос справочника плановых нормочасов для марок/моделей",
        content = [Content(schema = Schema(implementation = NhPlanDictionaryListRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = PagedResponse::class))
                ]
            )
        ]
    )
    @PostMapping("/list")
    fun getData(
        @RequestBody req: NhPlanDictionaryListRequest
    ): PagedResponse<NhPlanDictionaryDto> =
        service.getData(req)

    @Tag(name = "Справочник плановых НЧ")
    @Operation(summary = "Получение словаря")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = NhPlanDictionaryValuesDto::class))
                ]
            )
        ]
    )
    @GetMapping("/dict")
    fun getDictionary(): NhPlanDictionaryValuesDto =
        service.getDictionaryValues()

    @Tag(name = "Справочник плановых НЧ")
    @Operation(summary = "Получение списка ТС с незаполненным планом НЧ")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = NhDictionaryCheckNewDto::class))
                ]
            )
        ]
    )
    @GetMapping("/checkNew")
    fun checkNew(): NhDictionaryCheckNewDto =
        service.checkNew()

    @Tag(name = "Справочник плановых НЧ")
    @Operation(summary = "Изменение записи справочника плановых нормочасов для марок/моделей")
    @SwaggerReqBody(
        description = "Запрос справочника плановых нормочасов для марок/моделей",
        content = [Content(schema = Schema(implementation = NhPlanDictionaryUpdateRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/json",
                    array = ArraySchema(schema = Schema(implementation = NhPlanChildrenDictionaryDto::class))
                )
                ]
            )
        ]
    )
    @PutMapping("/edit")
    fun update(
        token: JwtToken?,
        @RequestBody req: NhPlanDictionaryUpdateRequest
    ): List<NhPlanChildrenDictionaryDto> =
        service.update(req, token.username)
            .also { log.debug("Update plan '${req.id}' Token: ${token.email} : ${token?.username}") }

    @Tag(name = "Справочник плановых НЧ")
    @Operation(summary = "Создание новой записи справочника плановых нормочасов для марок/моделей")
    @SwaggerReqBody(
        description = "Запрос на создание новой записи справочника плановых нормочасов для марок/моделей",
        content = [Content(schema = Schema(implementation = NhDictionaryInsertRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/json",
                    array = ArraySchema(schema = Schema(implementation = NhPlanChildrenDictionaryDto::class))
                )
                ]
            )
        ]
    )
    @PostMapping("/new")
    fun new(
        token: JwtToken?,
        @RequestBody req: NhDictionaryInsertRequest
    ): List<NhPlanChildrenDictionaryDto> =
        service.new(req, token.username)
            .also { log.debug("Create plan for vehicle '${req.id}' Token: ${token.email} : ${token?.username}") }

    @Tag(name = "Справочник плановых НЧ")
    @Operation(summary = "Создание новой записи справочника плановых нормочасов для марок/моделей")
    @SwaggerReqBody(
        description = "Запрос на создание новой записи справочника плановых нормочасов для марок/моделей",
        content = [Content(schema = Schema(implementation = NhPlanDeleteRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/json",
                    array = ArraySchema(schema = Schema(implementation = NhPlanChildrenDictionaryDto::class))
                )
                ]
            )
        ]
    )
    @DeleteMapping("/del")
    fun delete(
        token: JwtToken?,
        @RequestBody req: NhPlanDeleteRequest
    ): List<NhPlanChildrenDictionaryDto> =
        service.delete(req.id, token.username)
            .also { log.debug("Delete plan '${req.id}' Token: ${token.email} : ${token?.name}") }

    @Tag(name = "Справочник плановых НЧ")
    @Operation(summary = "Запрос на экспорт в XLSX справочника плановых нормочасов для марок/моделей")
    @SwaggerReqBody(
        description = "Запрос справочника плановых нормочасов для марок/моделей",
        content = [Content(schema = Schema(implementation = NhPlanDictionaryListRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(type = "string", format = "binary")
                )]
            )
        ]
    )
    @PostMapping("/export")
    fun export(@RequestBody req: NhPlanDictionaryListRequest): ResponseEntity<ByteArray> {
        val fileName = "Справочник План НЧ ${dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))}.xlsx"

        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(service.export(req))
    }

    @Tag(name = "Справочник плановых НЧ")
    @Operation(summary = "Запрос на импорт в XLSX справочника плановых нормочасов для марок/моделей")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = NhDictionaryImportStatus::class))
                ]
            )
        ]
    )
    @PostMapping(
        "/import",
        consumes = [MediaType.MULTIPART_FORM_DATA_VALUE]
    )
    fun import(
        token: JwtToken?,
        body: MultipartFile
    ): ResponseEntity<*> {
        val response = try {
            val result = service.import(body.inputStream, token.username)
            ResponseEntity.ok()
                .body(result)
        } catch (e: WrongRequestDataException) {
            ResponseEntity.badRequest()
                .body(e.message)
        }
        return response
    }
}
