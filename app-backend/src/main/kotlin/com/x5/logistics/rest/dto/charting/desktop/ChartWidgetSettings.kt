package com.x5.logistics.rest.dto.charting.desktop

import com.x5.logistics.rest.dto.charting.ChartColumnFilter
import com.x5.logistics.rest.dto.charting.ChartColumnRequest
import com.x5.logistics.rest.dto.charting.ChartDataWidgetValueRequest
import com.x5.logistics.rest.dto.charting.ChartSortOrder
import com.x5.logistics.rest.dto.charting.ChartTotals
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Настройки виджета.")
data class ChartWidgetSettings(
    @Schema(description = "Список строк.")
    val rows: List<ChartColumnRequest>,
    @Schema(description = "Список колонок.")
    val columns: List<ChartColumnRequest>,
    @Schema(description = "Общие итоги")
    val total: ChartTotals,
    @Schema(description = "Список настроек колонок.")
    val columnsSettings: Any?,
    @Schema(description = "Список значений.")
    val values: List<ChartDataWidgetValueRequest>,
    @Schema(description = "Список сборных показателей")
    val valuesCombine: List<Long>,
    @Schema(description = "Список сортировок.")
    val sort: List<ChartSortOrder>,
    @Schema(description = "Список фильтров.")
    val filters: List<ChartColumnFilter>,
    @Schema(description = "Гранулярность.")
    val granularity: String?,
    @Schema(description = "Автоматическая ширина.")
    val compactView: Boolean
)
