package com.x5.logistics.rest.dto.fuel

import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос модального виджета по топливу.")
data class FuelModalWidgetReq(
    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,
    @Schema(description = "Дата начала периода.")
    val from: LocalDate,
    @Schema(description = "Дата конца периода.")
    val to: LocalDate,
    @Schema(description = "Тип топлива.")
    val fuelType: FuelType
)
