package com.x5.logistics.rest.dto.repair.workspace

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Ответ данные по ремонтам - виджет рабочего стола по ремонтам на ремзоне.")
data class RepairWidgetPlacesResp(
    @Schema(description = "% работ на РЗ.")
    val ownWorkshopNHShare: Double?,

    @Schema(description = "Плановый % работ на РЗ.")
    val ownWorkshopNHSharePlan: Double,

    @Schema(description = "Средняя стоимость НЧ на РЗ.")
    val ownWorkshopNHAvgPrice: Double?,

    @Schema(description = "Средняя стоимость НЧ на СТО.")
    val extWorkshopNHAvgPrice: Double?,

    val isEmpty: <PERSON><PERSON><PERSON>,
)
