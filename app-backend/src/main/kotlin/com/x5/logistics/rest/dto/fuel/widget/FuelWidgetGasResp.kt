package com.x5.logistics.rest.dto.fuel.widget

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

@Schema(description = "Ответ по данным виджета ГАЗ.")
data class FuelWidgetGasResp(
    @Schema(description = "Факт.расх./100 км.Газ, л.")
    val factConsumptionOn100GasLiter: BigDecimal?,
    @Schema(description = "Пл.расх./100 км. Газ, л.")
    val planConsumptionOn100GasLiter: BigDecimal?,
    @Schema(description = "Факт.расх. Газ, руб/км.")
    val factConsumptionGasRubKm: BigDecimal?,
    @Schema(description = "Перерасход (+) /Экономия (-) Газ, руб/км.")
    val overspendingEconomyGasRubKm: BigDecimal?,
    @Schema(description = "Перерасход (+) /Экономия (-) Газ, руб.")
    val overspendingEconomyGasRub: BigDecimal?,
    @Schema(description = "Ответ пуст.")
    val isEmpty: Boolean
)
