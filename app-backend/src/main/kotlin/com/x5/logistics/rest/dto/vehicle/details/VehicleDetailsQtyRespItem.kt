package com.x5.logistics.rest.dto.vehicle.details

import com.x5.logistics.rest.dto.GranularityItem
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDate

@Schema(description = "Ответ на детальный отчет по Количеству ТС.")
data class VehicleDetailsQtyRespItem(
    @Schema(description = "Вид ТС.")
    val vehicleGroup: String?,
    @Schema(description = "Тип ТС.")
    val vehicleType: String?,
    @Schema(description = "Территория.")
    val terName: String?,
    @Schema(description = "Макрорегион.")
    val mrName: String?,
    @Schema(description = "АТП.")
    val atp: String?,
    @Schema(description = "МВЗ.")
    val mvz: String?,
    @Schema(description = "Наименование МВЗ.")
    val mvzName: String?,
    @Schema(description = "Тип МВЗ.")
    val mvzType: String?,
    @Schema(description = "Марка.")
    val brand: String?,
    @Schema(description = "Модель.")
    val model: String?,
    @Schema(description = "Год выпуска.")
    val year: String?,
    @Schema(description = "Тоннаж.")
    val tonnage: Float?,
    @Schema(description = "Паллетовместимость.")
    val compartAmount: Int?,
    @Schema(description = "Гос. номер ТС.")
    val licenseNum: String?,
    @Schema(description = "Единица оборудования.")
    val eqUnit: String?,
    @Schema(description = "VIN номер.")
    val vin: String?,
    @Schema(description = "Признак установленного ГБО.")
    val gbo: Boolean?,
    @Schema(description = "Дата ввода в эксплуатацию.")
    val commissioningRealDate: LocalDate?,
    @Schema(description = "Год ввода в эксплуатацию.")
    val commissioningDate: Int?,
    @Schema(description = "Пробег.")
    val mileage: BigDecimal?,
    @Schema(description = "ТС на конец периода.")
    var qtyForLastDate: Int? = null,
    @Schema(description = "Среднее за период.")
    var avgQty: Double? = null,
    @Schema(description = "ТС на конец периода по гранулярным периодам.")
    val granularityLastDayQty: MutableList<GranularityItem>? = null,
    @Schema(description = "Среднее за период по гранулярным периодам.")
    val granularityAvgQty: MutableList<GranularityItem>? = null
)


@Schema(description = "Ответ на детальный отчет по Количеству ТС")
data class VehicleDetailsQtyResponse(
    @Schema(description = "Общее количество записей.")
    val count: Int,
    @Schema(description = "Номер страницы.")
    val pageNumber: Int,
    @Schema(description = "Размер страницы.")
    val pageSize: Int,
    @Schema(description = "Реальное начало периода.")
    val actualStartDate: LocalDate,
    @Schema(description = "Реальный конец периода.")
    val actualEndDate: LocalDate,
    @Schema(description = "Итого в среднем.")
    val totalAvg: BigDecimal?,
    @Schema(description = "Значения.")
    val items: Collection<VehicleDetailsQtyRespItem>,

    @Schema(description = "Итого на последний день.") //to delete
    val totalLastDay: Int? = 0,
) {
    @Schema(description = "Всего страниц.")
    val pageTotal: Int = (count + pageSize - 1) / pageSize
}