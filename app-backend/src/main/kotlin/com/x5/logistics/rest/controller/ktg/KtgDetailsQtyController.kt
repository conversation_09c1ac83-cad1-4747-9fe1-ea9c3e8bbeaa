package com.x5.logistics.rest.controller.ktg

import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.PagedDetailsResponse
import com.x5.logistics.rest.dto.kip.details.KipDetailsQtyReq
import com.x5.logistics.rest.dto.ktg.KtgDetailedFilterRest
import com.x5.logistics.rest.dto.ktg.KtgDetailedFiltersFront
import com.x5.logistics.rest.dto.ktg.KtgDetailsFilterReq
import com.x5.logistics.rest.dto.ktg.KtgDetailsMassFilterReq
import com.x5.logistics.rest.dto.ktg.KtgDetailsPagedResponse
import com.x5.logistics.rest.dto.ktg.KtgDetailsQtyReq
import com.x5.logistics.rest.dto.ktg.KtgDetailsQtyResp
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailsColumnFilter
import com.x5.logistics.service.ktg.KtgReportService
import com.x5.logistics.util.checkFilterValues
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
@RequestMapping("/api/ktg/details")
class KtgDetailsQtyController(
    val ktgService: KtgReportService,
) {
    private val log = getLogger()

    @Tag(name = "КТГ данные.")
    @Operation(summary = "Запрос на детальный отчет КТГ по количеству транспортных средств для заданного периода дат и МВЗ.")
    @SwaggerReqBody(
        description = "Запрос на детальный отчет по КТГ.",
        content = [Content(schema = Schema(implementation = KipDetailsQtyReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = PagedDetailsResponse::class))]
            )
        ]
    )
    @PostMapping
    suspend fun getDetailedReport(@RequestBody req: KtgDetailsQtyReq): KtgDetailsPagedResponse<KtgDetailsQtyResp> {
        log.debug("Kip details qty. [req={}]", req)
        return ktgService.getReport(req)
    }

    @Tag(name = "КТГ данные.")
    @Operation(summary = "Получение названия столбцов на детальный отчет КТГ по количеству транспортных средств для заданного периода дат и МВЗ.")
    @GetMapping
    fun getDetailedReportColumns(): List<KtgDetailedFilterRest> {
        return KtgDetailedFiltersFront.entries.map {
            KtgDetailedFilterRest(
                label = it.name,
                name = it.label,
                group = it.group.name,
                filterable = it.filterable
            )
        }
    }

    @Tag(name = "КТГ данные.")
    @Operation(summary = "Запрос на получение доступных значений фильтра с учетом уже выбранных фильтров.")
    @SwaggerReqBody(
        description = "Запрос на детальный отчет по КТГ.",
        content = [Content(schema = Schema(implementation = KtgDetailsFilterReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = LabeledValue::class))]
            )
        ]
    )
    @PostMapping("/filter")
    suspend fun getFilterValues(@RequestBody req: KtgDetailsFilterReq): List<LabeledValue> {
        log.debug("Kip details qty. [req={}]", req)
        return ktgService.getFilterValues(req)
    }

    @Tag(name = "КТГ данные.")
    @Operation(summary = "Запрос на массовую проверку значений фильтра: детальный отчет по КТГ")
    @SwaggerReqBody(
        description = "Запрос на детальный отчет по КТГ.",
        content = [Content(schema = Schema(implementation = KtgDetailsFilterReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = MassFilterResp::class))]
            )
        ]
    )
    @PostMapping("/massfilter")
    suspend fun getMassFilterValues(@RequestBody req: KtgDetailsMassFilterReq): MassFilterResp = checkFilterValues(
        valuesForCheck = req.request.value,
        actualValues = ktgService.getFilterValues(
            KtgDetailsFilterReq(
                from = req.from,
                to = req.to,
                geoFilter = req.geoFilter,
                columns = req.columns,
                filters = req.filters,
                request = VehicleDetailsColumnFilter(
                    name = req.request.name,
                    condition = FilterCondition.contain,
                    value = req.request.value,
                )
            )
        )
    )
}
