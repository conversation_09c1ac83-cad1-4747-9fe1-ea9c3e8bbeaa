package com.x5.logistics.rest.dto.dictionary.rr

import com.fasterxml.jackson.annotation.JsonProperty
import com.x5.logistics.data.dictionary.rr.Month
import java.math.BigDecimal

data class RrDictionaryGroupDto(
    val year: Int,
    val atpId: Long,
    val atpName: String,
    @JsonProperty("updated_by")
    val updatedBy: String,
    val tonnage: BigDecimal,
    val deletable: Boolean,
    val rates: List<RrDictionaryGroupRateDto>
)

data class RrDictionaryGroupRateDto(
    val month: Month,
    val rate: Double
)
