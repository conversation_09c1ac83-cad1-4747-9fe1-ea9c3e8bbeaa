package com.x5.logistics.rest.dto.hr.dictionaries.drivers

import com.x5.logistics.rest.dto.SortItem
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Запрос справочника плановой численности водителей.")
data class HrDriversPerVehicleDictionaryRequest(
    @Schema(description = "Размер страницы.")
    val pageSize: Int,
    @Schema(description = "Номер страницы.")
    val pageNumber: Int,
    @Schema(description = "Список сортировок.")
    val sort: List<SortItem<DriversPerVehicleColumn>>,
    @Schema(description = "Список фильтров.")
    val filters: List<HrDriversPerVehicleDictionaryFilter>,
    @Schema(description = "Год.")
    val year: Int
)