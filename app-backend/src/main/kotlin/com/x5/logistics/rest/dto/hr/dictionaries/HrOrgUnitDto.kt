package com.x5.logistics.rest.dto.hr.dictionaries

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Данные об автоколонне HR")
data class HrOrgUnitDto(

    @Schema(description = "Код автоколонны HR")
    val orgUnitId: String,

    @Schema(description = "Наименование автоколонны HR")
    val orgUnitName: String,

    @Schema(description = "Full path")
    val orgUnitFullPath: String
)
