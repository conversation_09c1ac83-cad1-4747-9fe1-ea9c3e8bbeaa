package com.x5.logistics.rest.dto.macroregion

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Запрос на список макрорегионов.")
data class MacroRegionListReq(
    @Schema(description = "Номер страницы.")
    val pageNumber: Int,

    @Schema(description = "Размер страницы.")
    val pageSize: Int,

    @Schema(description = "Список сортировок.")
    val sort: List<MacroRegionSortOrder>,

    @Schema(description = "Список фильтров.")
    val filters: List<MacroRegionColumnFilter>
)
