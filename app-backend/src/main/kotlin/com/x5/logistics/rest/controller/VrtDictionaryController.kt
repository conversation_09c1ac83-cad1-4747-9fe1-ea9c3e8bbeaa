package com.x5.logistics.rest.controller

import com.x5.logistics.rest.dto.OffsetLimitPagedResponse
import com.x5.logistics.rest.dto.dictionary.vrt.AddVrtSubtypeDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.AddVrtTypeDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.DeleteItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.EditVrtDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.EditVrtSubtypeDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.EditVrtTypeDictionaryItemReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryCheckNewResponse
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryExportReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryFilterValues
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryFiltersRes
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionarySubtypeItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtLevel
import com.x5.logistics.rest.dto.dictionary.vrt.VrtSubtypeDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtSubtypeItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeItem
import com.x5.logistics.service.dictionary.vrt.VrtDictionaryExportService
import com.x5.logistics.service.dictionary.vrt.VrtDictionaryService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URLEncoder
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("api/dictionary/vrt")
class VrtDictionaryController(
    private val vrtDictionaryService: VrtDictionaryService,
    private val exportService: VrtDictionaryExportService
) {
    val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss")

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Получение записей словаря ВРТ.")
    @PostMapping("vrt-subtypes")
    fun getVrtDictionaryList(
        @RequestBody req: VrtDictionaryListReq
    ): OffsetLimitPagedResponse<VrtDictionarySubtypeItem> {
        req.setDefaultSort(isSubtypeDictionary = true)
        val items = vrtDictionaryService.getVrtDictionaryGroupList(req).map { it.toSubtypeItem() }
        val count = vrtDictionaryService.countVrtDictionaryGroupList(req)
        return OffsetLimitPagedResponse(
            count = count,
            offset = req.pageNumber * req.pageSize,
            items = items
        )
    }

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Выгрузка записей словаря ВРТ в Excel.")
    @PostMapping("export")
    fun exportVrtDictionary(
        @RequestBody req: VrtDictionaryExportReq
    ): ResponseEntity<ByteArray> {
        val namePart = when (req.vrtLevel) {
            VrtLevel.TYPES -> "Справочник Распределение видов ВРТ"
            VrtLevel.SUBTYPES -> "Справочник Распределение подвидов ВРТ"
        }
        val reportDate = dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))
        val fileName = "${namePart}_$reportDate.xlsx".urlEncoded
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=$fileName"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(exportService.exportToroWorks(req))
    }


    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Получение значений фильтров.")
    @GetMapping("vrt-dict")
    fun getVrtDictionaryValues(): VrtDictionaryFiltersRes {
        val typeFilterItems = vrtDictionaryService.getVrtDictionaryTypeFilterItems()
        val subtypeFilterItems = vrtDictionaryService.getVrtDictionarySubtypeFilterItems()
        return VrtDictionaryFiltersRes(
            type = typeFilterItems,
            subtype = subtypeFilterItems
        )
    }

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Получение значений фильтров.")
    @GetMapping("vrt-filter")
    fun getVrtDictionaryFilters(): VrtDictionaryFilterValues =
        vrtDictionaryService.getVrtDictionaryFilterValues()

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Выгрузка записей словаря типов ВРТ в Excel.")
    @PostMapping("type/export")
    fun exportTypes(
        @RequestBody req: VrtTypeDictionaryListReq
    ): ResponseEntity<ByteArray> {
        val namePart = "Справочник Виды ВРТ"
        val reportDate = dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))
        val fileName = "${namePart}_$reportDate.xlsx".urlEncoded
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=$fileName"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(exportService.exportTypes(req))
    }

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Получение записей словаря подтипов ВРТ.")
    @PostMapping("subtypes")
    fun getVrtSubtypeDictionaryList(
        @RequestBody req: VrtSubtypeDictionaryListReq
    ): OffsetLimitPagedResponse<VrtSubtypeItem> {
        val items = vrtDictionaryService.getVrtSubtypeDictionaryItems(req)
        val count = vrtDictionaryService.countVrtSubtypeDictionaryItems(req)
        return OffsetLimitPagedResponse(
            count = count,
            offset = req.pageNumber * req.pageSize,
            items = items
        )
    }

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Выгрузка записей словаря подтипов ВРТ в Excel.")
    @PostMapping("subtype/export")
    fun exportSubtypes(
        @RequestBody req: VrtSubtypeDictionaryListReq
    ): ResponseEntity<ByteArray> {
        val namePart = "Справочник Подвиды ВРТ"
        val reportDate = dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))
        val fileName = "${namePart}_$reportDate.xlsx".urlEncoded
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=$fileName"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(exportService.exportSubtypes(req))
    }

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Изменение записи словаря ВРТ.")
    @PutMapping
    fun editVrtDictionaryItem(
        @RequestBody req: EditVrtDictionaryItemReq,
        token: JwtToken?
    ): VrtDictionaryItem =
        vrtDictionaryService.editVrtDictionaryItem(req, token.username)

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Изменение записи словаря типов ВРТ.")
    @PutMapping("type")
    fun editVrtTypeDictionaryItem(
        @RequestBody req: EditVrtTypeDictionaryItemReq,
        token: JwtToken?
    ): VrtTypeItem =
        vrtDictionaryService.editVrtTypeDictionaryItem(req, token.username)

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Добавление записи словаря типов ВРТ.")
    @PostMapping("type")
    fun addVrtTypeDictionaryItem(
        @RequestBody req: AddVrtTypeDictionaryItemReq,
        token: JwtToken?
    ): VrtTypeItem =
        vrtDictionaryService.addVrtTypeDictionaryItem(req, token.username)

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Удаление записи словаря типов ВРТ.")
    @DeleteMapping("type")
    fun deleteVrtTypeDictionaryItem(
        @RequestBody req: DeleteItemReq,
        token: JwtToken?
    ) {
        vrtDictionaryService.deleteVrtTypeDictionaryItem(req.id, token.username)
    }

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Изменение записи словаря подтипов ВРТ.")
    @PutMapping("subtype")
    fun editVrtSubtypeDictionaryItem(
        @RequestBody req: EditVrtSubtypeDictionaryItemReq,
        token: JwtToken?
    ): VrtSubtypeItem =
        vrtDictionaryService.editVrtSubtypeDictionaryItem(req, token.username)

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Добавление записи словаря подтипов ВРТ.")
    @PostMapping("subtype")
    fun addVrtSubtypeDictionaryItem(
        @RequestBody req: AddVrtSubtypeDictionaryItemReq,
        token: JwtToken?
    ): VrtSubtypeItem =
        vrtDictionaryService.addVrtSubtypeDictionaryItem(req, token.username)

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Удаление записи словаря подтипов ВРТ.")
    @DeleteMapping("subtype")
    fun deleteVrtSubtypeDictionaryItem(
        @RequestBody req: DeleteItemReq,
        token: JwtToken?
    ) {
        vrtDictionaryService.deleteVrtSubtypeDictionaryItem(req.id, token.username)
    }

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Уведомление о незаполненных ВРТ.")
    @GetMapping("checkNew")
    fun checkNew(): VrtDictionaryCheckNewResponse {
        return vrtDictionaryService.getNewVrts()
    }

    private val String.urlEncoded: String
        get() = URLEncoder.encode(this, "UTF-8").replace("+", "%20")
}
