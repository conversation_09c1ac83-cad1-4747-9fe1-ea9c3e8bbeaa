package com.x5.logistics.rest.dto.operreport

data class OperReportResponse(
    val count: Long,
    val pageNumber: Int,
    val pageSize: Int,
    val items: List<OperReportResponseItem>,
    val pageTotal: Long = if (pageSize != 0) (count + pageSize.toLong() - 1) / pageSize else 1
)

data class OperReportResponseItem(
    val periodDay: String?,
    val periodReportWeek: String?,
    val periodCalendarWeek: String?,
    val periodMonth: String?,
    val periodQuarter: String?,
    val periodYear: String?,
    val vehicleLicense: String?,
    val vehicleGroup: String?,
    val vehicleType: String?,
    val vehicleBrand: String?,
    val vehicleModel: String?,
    val vehicleTonnage: Float?,
    val vehicleCreateYear: Int?,
    val mvz: String?,
    val mvzName: String?,
    val retailNetwork: String?,
    val ter: String?,
    val mr: String?,
    val atp: String?,
    val vehicleCount: Double?,
    val rcRetailNetwork: String?,
    val rcName: String?,
    val rcCode: String?,
    val logisticsRcName: String?,
    val idLogisticsRcSAP: String?,
    val tripsCount: Long?,
    val pointsCount: Long?,
    val charaterDelivery: String?,
    val rnAndCharaterDelivery: String?,
    val ourTripText: String?,
    val kipHours: Double?,
    val kipNoReserveHours: Double?,
    val kipShare: Double?,
    val kipNoReserveShare: Double?,
    val ktgShare: Double?,
    val rgShare: Double?,
    val comeOffCount: Double?,
    val comeOff: Double?,
    val carInTime: Double?,
    val carInTimeOwn: Double?,
    val carInGPSOwn: Double?,
    val carInTimeHired: Double?,
    val carInGPSHired: Double?,
    val deliveryInPlan: Double?,
    val temperature: Double?,
)