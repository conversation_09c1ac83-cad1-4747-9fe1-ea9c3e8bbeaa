package com.x5.logistics.rest.dto.repaircostfactor

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос проверки данных для поиска по списку значений")
data class RepairCostFactorReportMassFilterReq(
    @Schema(description = "Дата начала периода")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val from: LocalDate,

    @Schema(description = "Дата конца периода")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val to: LocalDate,

    @Schema(description = "Список колонок")
    val columns: List<RepairCostFactorReportColumn>,

    @Schema(description = "Фильтры")
    val filters: List<RepairCostFactorReportColumnFilter>,

    @Schema(description = "Геофильтр")
    val geoFilter: GeoFilter,

    @Schema(description = "Запрос")
    val request: Request
) {
    data class Request(
        @Schema(description = "Название фильтра")
        val name: RepairCostFactorReportColumn,

        @Schema(description = "Значения для фильтрации")
        val value: List<String>
    )
}
