package com.x5.logistics.rest.dto.atplog

import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate
import java.time.LocalDateTime

@Schema(description = "Данные по АТП")
data class AtpLog(
    @Schema(description = "Уникальный идентификатор для выдачи на фронтенд")
    val id: Long,

    @Schema(description = "Уникальный идентефикатор АТП в БД")
    val atpId: Long,

    @Schema(description = "Название АТП")
    val atpName: String,

    @Schema(description = "Идентификатор макрорегиона")
    val mrId: Long,

    @Schema(description = "Название макрорегиона")
    val mrName: String?,

    @Schema(description = "Тип АТП")
    val type: String?,

    @Schema(description = "Торговая сеть АТП")
    val retailNetwork: String,

    @Schema(description = "Дата начала")
    val startDate: LocalDate,

    @Schema(description = "Дата окончания")
    val endDate: LocalDate?,

    @Schema(description = "Автор редактирования")
    val author: String?,

    @Schema(description = "Дата обновления")
    val updatedAt: LocalDateTime?,

    @Schema(description = "Количество записей")
    val count: Int?,

    @Schema(description = "Исторические данные")
    val children: List<AtpLog>?
)
