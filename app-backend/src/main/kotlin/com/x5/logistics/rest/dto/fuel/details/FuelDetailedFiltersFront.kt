package com.x5.logistics.rest.dto.fuel.details

import com.x5.logistics.rest.util.FilterGroup
import com.x5.logistics.rest.util.FilterGroup.*
import com.x5.logistics.rest.util.FilterRespEnum

@Suppress("EnumEntryName")
enum class FuelDetailedFiltersFront(
    override val label: String,
    override val group: FilterGroup,
    override val filterable: Boolean = true
) : FilterRespEnum {
    // ПЛ:
    numberWaybillStart(
        group = date,
        label = "Дата и время открытия ПЛ"
    ),

    numberWaybillEnd(
        group = date,
        label = "Дата и время закрытия ПЛ"
    ),

    waybillNumber(
        group = stringSearch,
        label = "Номер ПЛ"
    ),

    typeWB(
        group = stringSearch,
        label = "Тип ПЛ"
    ),

    maintenance(
        group = boolean,
        label = "Ремонтный ПЛ"
    ),

    runWaybill(
        group = number,
        label = "Пробег по ПЛ"
    ),

    probegWithGas(
        group = number,
        label = "Пробег с газом"
    ),

    mountainMileage(
        group = number,
        label = "Пробег горный"
    ),

    engineHoursFridgeHead(
        group = number,
        label = "Моточасы ХОУ (г), ч."
    ),

    engineHoursAcTrailer(
        group = number,
        label = "Моточасы ХОУ (п), ч."
    ),

    normaDieselOn100Km(
        group = number,
        label = "Норма ДТ/ 100 км (К1_П), л."
    ),

    normaFridgeHeadDieselPerHour(
        group = number,
        label = "Норма ХОУ (г) ДТ/ час, л."
    ),

    normaFridgeTrailerDieselPerHour(
        group = number,
        label = "Норма ХОУ (п) ДТ / час, л."
    ),

    normaGasOn100Km(
        group = number,
        label = "Норма Газ/ 100 км, л."
    ),

    winterCoefficient(
        group = number,
        label = "Зимний коэф. (К1)"
    ),

    mountainCoefficient(
        group = number,
        label = "Горный коэф. (S)"
    ),

    coefficientReductionDiesel(
        group = number,
        label = "Коэф. снижения ДТ (К8)"
    ),

    costDieselOnLiter(
        group = number,
        label = "Стоимость ДТ / л, руб."
    ),

    costGasOnLiter(
        group = number,
        label = "Стоимость Газ / л, руб."
    ),

    // TC:
    terName(
        group = string,
        label = "Территория",
        filterable = false
    ),

    macroRegion(
        group = string,
        label = "Макрорегион",
        filterable = false
    ),

    atp(
        group = string,
        label = "АТП",
        filterable = false
    ),

    mvz(
        group = string,
        label = "Номер МВЗ",
        filterable = false
    ),

    mvzName(
        group = string,
        label = "Наименование МВЗ",
        filterable = false
    ),

    vehicleType(
        group = string,
        label = "Тип ТС"
    ),

    vehicleGroup(
        group = string,
        label = "Вид ТС"
    ),

    brand(
        group = string,
        label = "Марка ТС"
    ),

    model(
        group = string,
        label = "Модель ТС"
    ),

    tonnage(
        group = number,
        label = "Тоннаж ТС"
    ),

    fuelTypeFirst(
        group = string,
        label = "Первичное топливо ТС"
    ),

    year(
        group = number,
        label = "Год выпуска ТС"
    ),

    startDate(
        group = number,
        label = "Год начала эксплуатации ТС"
    ),

    tabNum(
        group = stringSearch,
        label = "Табельный номер водителя"
    ),

    licenseNum(
        group = stringSearch,
        label = "Гос. номер ТС"
    ),

    vin(
        group = stringSearch,
        label = "VIN номер ТС"
    ),

    eqUnit(
        group = stringSearch,
        label = "Единица оборудования ТС"
    ),

    licenseNumTrailer(
        group = stringSearch,
        label = "Гос. номер прицепа"
    ),

    gbo(
        group = boolean,
        label = "ГБО"
    ),

    // ДТ
    planConsumptionDieselLiter(
        group = number,
        label = "Пл.расх. ДТ, л."
    ),

    planConsumptionHeadDieselLiter(
        group = number,
        label = "Пл.расх (г) ДТ, л."
    ),

    planConsumptionFridgeHeadDieselLiter(
        group = number,
        label = "Пл.расх ХОУ (г) ДТ, л."
    ),

    planConsumptionNoFridgeHeadDieselLiter(
        group = number,
        label = "Пл.расх. без ХОУ (г) ДТ, л."
    ),

    planConsumptionFridgeTrailerDieselLiter(
        group = number,
        label = "Пл.расх. ХОУ (п) ДТ, л."
    ),

    factConsumptionDieselLiter(
        group = number,
        label = "Факт. расх. ДТ, л."
    ),

    factConsumptionHeadDieselLiter(
        group = number,
        label = "Факт. расх. (г) ДТ, л."
    ),

    factConsumptionFridgeHeadDieselLiter(
        group = number,
        label = "Факт. расх. ХОУ (г) ДТ, л."
    ),

    factConsumptionNoFridgeHeadDieselLiter(
        group = number,
        label = "Факт. расх. без ХОУ (г) ДТ, л."
    ),

    factConsumptionFridgeTrailerDieselLiter(
        group = number,
        label = "Факт. расх. ХОУ (п) ДТ, л."
    ),

    burnoutEconomyDieselLiter(
        group = number,
        label = "Пережог (+) /Экономия (-) ДТ, л."
    ),

    burnoutEconomyNoFridgeHeadDieselLiter(
        group = number,
        label = "Пережог (+) /Экономия (-) без ХОУ (г) ДТ, л."
    ),

    burnoutEconomyFridgeTrailerDieselLiter(
        group = number,
        label = "Пережог (+) /Экономия (-) ХОУ (п) ДТ, л."
    ),

    planConsumptionOn100DieselLiter(
        group = number,
        label = "Пл.расх./100 км. ДТ, л."
    ),

    planConsumptionOn100NoFridgeHeadDieselLiter(
        group = number,
        label = "Пл.расх./ 100 км.без ХОУ (г) ДТ, л."
    ),

    factConsumptionOn100DieselLiter(
        group = number,
        label = "Факт.расх./100 км.ДТ, л."
    ),

    factConsumptionOn100NoFridgeHeadDieselLiter(
        group = number,
        label = "Факт.расх./ 100 км.без ХОУ (г) ДТ, л."
    ),

    planConsumptionDieselRub(
        group = number,
        label = "Пл.расх. ДТ, руб."
    ),

    planConsumptionHeadDieselRub(
        group = number,
        label = "Пл.расх. (г) ДТ, руб."
    ),

    planConsumptionFridgeHeadDieselRub(
        group = number,
        label = "Пл.расх. ХОУ (г) ДТ, руб."
    ),

    planConsumptionNoFridgeHeadDieselRub(
        group = number,
        label = "Пл.расх.без ХОУ (г) ДТ, руб."
    ),

    planConsumptionFridgeTrailerDieselRub(
        group = number,
        label = "Пл.расх. ХОУ (п) ДТ, руб."
    ),

    factConsumptionDieselRub(
        group = number,
        label = "Факт.расх. ДТ, руб."
    ),

    factConsumptionHeadDieselRub(
        group = number,
        label = "Факт.расх. (г) ДТ, руб."
    ),

    factConsumptionFridgeHeadDieselRub(
        group = number,
        label = "Факт.расх. ХОУ (г) ДТ, руб."
    ),

    factConsumptionNoFridgeHeadDieselRub(
        group = number,
        label = "Факт.расх.без ХОУ (г) ДТ, руб."
    ),

    factConsumptionFridgeTrailerDieselRub(
        group = number,
        label = "Факт.расх. ХОУ (п) ДТ, руб."
    ),

    overspendingEconomyDieselRub(
        group = number,
        label = "Перерасход (+) /Экономия (-) ДТ, руб."
    ),

    overspendingEconomyNoFridgeHeadDieselRub(
        group = number,
        label = "Перерасход (+) /Экономия (-) без ХОУ (г) ДТ, руб."
    ),

    overspendingEconomyFridgeTrailerDieselRub(
        group = number,
        label = "Перерасход (+) /Экономия (-) ХОУ (п) ДТ, руб."
    ),

    planConsumptionDieselRubKm(
        group = number,
        label = "Пл.расх. ДТ, руб/км."
    ),

    factConsumptionDieselRubKm(
        group = number,
        label = "Факт.расх. ДТ, руб/км."
    ),

    overspendingEconomyDieselRubKm(
        group = number,
        label = "Перерасход (+) /Экономия (-) ДТ, руб/км."
    ),

    // Газ
    planConsumptionGasLiter(
        group = number,
        label = "Пл.расх. Газ, л."
    ),

    factConsumptionGasLiter(
        group = number,
        label = "Факт. расход Газ, л."
    ),

    planConsumptionOn100GasLiter(
        group = number,
        label = "Пл.расх./100 км. Газ, л."
    ),

    factConsumptionOn100GasLiter(
        group = number,
        label = "Факт.расх./100 км.Газ, л."
    ),

    burnoutEconomyGasLiter(
        group = number,
        label = "Пережог (+) /Экономия (-) Газ, л."
    ),

    planConsumptionGasRub(
        group = number,
        label = "Пл.расх. Газ, руб."
    ),

    factConsumptionGasRub(
        group = number,
        label = "Факт.расх. Газ, руб."
    ),

    overspendingEconomyGasRub(
        group = number,
        label = "Перерасход (+) /Экономия (-) Газ, руб."
    ),

    // Общее
    planConsumptionGasRubKm(
        group = number,
        label = "Пл.расх. Газ, руб/км."
    ),

    factConsumptionGasRubKm(
        group = number,
        label = "Факт.расх. Газ, руб/км."
    ),

    overspendingEconomyGasRubKm(
        group = number,
        label = "Перерасход (+) /Экономия (-) Газ, руб/км."
    ),

    planConsumptionLiter(
        group = number,
        label = "Пл.расх., л."
    ),

    factConsumptionLiter(
        group = number,
        label = "Факт.расх., л."
    ),

    burnoutEconomyLiter(
        group = number,
        label = "Пережог (+) /Экономия (-), л."
    ),

    planConsumptionOn100Liter(
        group = number,
        label = "Пл.расх./ 100 км., л."
    ),

    factConsumptionOn100Liter(
        group = number,
        label = "Факт.расх./ 100 км., л."
    ),

    planConsumptionRub(
        group = number,
        label = "Пл.расх., руб."
    ),

    factConsumptionRub(
        group = number,
        label = "Факт.расх., руб."
    ),

    overspendingEconomyRub(
        group = number,
        label = "Перерасход (+) /Экономия (-), руб."
    ),

    planConsumptionRubKm(
        group = number,
        label = "Пл.расх., руб/км."
    ),

    factConsumptionRubKm(
        group = number,
        label = "Факт.расх., руб/км."
    ),

    overspendingEconomyRubKm(
        group = number,
        label = "Перерасход (+) /Экономия (-), руб/км."
    ),

    // ГБО
    presumptiveConsumptionDieselOutGbo(
        group = number,
        label = "Предпол.расход ДТ без ГБО, л."
    ),

    planEffectDieselWithGbo(
        group = number,
        label = "Пл. эффект на ДТ от ГБО, л."
    ),

    factEffectDieselWithGbo(
        group = number,
        label = "Факт. эффект на ДТ с ГБО, л."
    ),

    planEffectDieselWithGboOn100(
        group = number,
        label = "Пл. эффект на ДТ от ГБО /100 км, л."
    ),

    factEffectDieselWithGboOn100(
        group = number,
        label = "Факт. эффект на ДТ с ГБО /100 км, л."
    ),

    planEffectWithGboRub(
        group = number,
        label = "Пл. эффект от ГБО, руб."
    ),

    factEffectWithGboRub(
        group = number,
        label = "Факт. эффект от ГБО , руб"
    ),

    overspendingEconomyPlanEffectWithGbo(
        group = number,
        label = "Перерасход (+) /Экономия (-) от план.эффекта ГБО, руб."
    ),

    planEffectWithGboRubKm(
        group = number,
        label = "Пл. эффект от ГБО, руб/км."
    ),

    factEffectWithGboRubKm(
        group = number,
        label = "Факт. эффект от ГБО, руб/км."
    ),

    planPercentDeclineDiesel(
        group = number,
        label = "Пл.% сниж.ДТ, %"
    ),

    factPercentDeclineDiesel(
        group = number,
        label = "Факт.% сниж.ДТ, %"
    ),

    planCoefficientSubstitutionDieselByGas(
        group = number,
        label = "Пл. Коэф. замещения ДТ Газом"
    ),

    factCoefficientSubstitutionDieselByGas(
        group = number,
        label = "Факт. Коэф. замещения ДТ Газом"
    )
}