package com.x5.logistics.rest.dto.vehicle.details

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос проверки данных для поиска по списку значений.")
data class VehicleDetailsMassFilterReq(

    @Schema(description = "Дата начала периода.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val from: LocalDate,

    @Schema(description = "Дата конца периода.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val to: LocalDate,

    @Schema(description = "Фильтры.")
    val filters: List<VehicleDetailsColumnFilter>,

    @Schema(description = "Геофильтр.")
    val geoFilter: GeoFilter,

    @Schema(description = "Запрос.")
    val request: Request

    ) {
    data class Request(

        @Schema(description = "Название фильтра.")
        val name: VehicleDetailsColumn,

        @Schema(description = "Значения для фильтрации.")
        val value: List<String>
    )
}

