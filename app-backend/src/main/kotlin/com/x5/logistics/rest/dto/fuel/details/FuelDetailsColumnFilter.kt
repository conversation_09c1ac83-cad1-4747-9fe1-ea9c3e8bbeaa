package com.x5.logistics.rest.dto.fuel.details

import com.x5.logistics.rest.dto.FilterCondition
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Описание фильтра для детального отчета по топливу.")
data class FuelDetailsColumnFilter(
    @Schema(description = "Колонка для фильтра.")
    val name: FuelDetailsColumn,
    @Schema(description = "Тип фильтра.")
    val condition: FilterCondition,
    @Schema(description = "Список значений.")
    val value: List<Any>
)
