package com.x5.logistics.rest.dto.repair.parts

import java.math.BigInteger
import java.time.LocalDate

data class RepairPartsItem(
    val terName: String?,
    val mr: String?,
    val atp: String?,
    val mvzId: String?,
    val mvzName: String?,
    val tonnage: Float?,
    val brand: String?,
    val model: String?,
    val year: Int?,
    val licenseNum: String?,
    val materialTypeId: String?,
    val materialTypeName: String?,
    val materialGroupId: String?,
    val materialGroupName: String?,
    val materialParentId: String?,
    val materialParentName: String?,
    val materialArticle: String?,
    val partCode: Long?,
    val partName: String?,
    val materialProduce: String?,
    val orderId: Long?,
    val orderCreateDate: LocalDate?,
    val vrt: String?,
    val vrtName: String?,
    val vrtSubtype: String?,
    val vrtType: String?,
    val partsAmount: Double?,
    val partsCosts: Double?,
    val partsAvgPrice: Double?,
    val granularityPartsAmount: String?,
    val granularityPeriodCount: BigInteger?,
    val totalAmount: Double?,
    val totalCosts: Double?,
    val maxPartsAmount: Double?,
    val maxPartsCosts: Double?,
    val maxPartsAvgPrice: Double?,
    val maxGranularityPeriodCount: Long?,
    val rows: Int?,
    val orderKindId: String?
)
