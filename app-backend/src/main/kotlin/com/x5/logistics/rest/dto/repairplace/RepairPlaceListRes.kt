package com.x5.logistics.rest.dto.repairplace

data class RepairPlaceListRes(
    val count: Long,
    val items: List<RepairPlaceListItem>,
    val ourRepairShare: Double,
    val planFulfillment: Double
)

data class RepairPlaceListItem(
    val terName: String? = null,
    val mrName: String? = null,
    val repairPlace: String? = null,
    val serviceTer: String? = null,
    val serviceMr: String? = null,
    val serviceAtp: String? = null,
    val serviceMvz: String? = null,
    val tsNumber: Double? = null,
    val factTsNumber: Long? = null,
    val atpRequirement: Double? = null,
    val goalPostProduction: Double? = null,
    val postsNumberPerDay: Double? = null,
    val postsNumberPerNight: Double? = null,
    val totalPostsNumber: Double? = null,
    val repairPlaceWorkingPower: Double? = null,
    val repairPlaceTotalPower: Double? = null,
    val goalRepairPlaceProduction: Double? = null,
    val repairPlaceProvision: Double? = null,
    val workPercent: Double? = null,
    val factRepairPlaceProduction: Double? = null,
    val repairPlaceProductionPlanPerformance: Double? = null,
    val factPostProduction: Double? = null,
    val goalMechanicProduction: Double? = null,
    val goalMechanicRequirement: Double? = null,
    val factMechanicSsch: Double? = null,
    val factMechanicNumber: Long? = null,
    val factMechanicProduction: Double? = null,
    val mechanicProductionPlanPerformance: Double? = null,
    val mechanicProvision: Double? = null
)
