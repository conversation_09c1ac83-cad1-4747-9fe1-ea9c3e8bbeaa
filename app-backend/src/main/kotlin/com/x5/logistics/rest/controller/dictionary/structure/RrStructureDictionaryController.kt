package com.x5.logistics.rest.controller.dictionary.structure

import com.x5.logistics.repository.dictionary.rr.structure.RrStructureDictionaryExposedRepo
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.dictionary.rr.RrImportResponse
import com.x5.logistics.rest.dto.dictionary.rr.structure.DetailedRowForImportReq
import com.x5.logistics.rest.dto.dictionary.rr.structure.GetAtpResp
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryField
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryFilter
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryListReqWithYearList
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryResp
import com.x5.logistics.rest.dto.dictionary.rr.structure.RrStructureDictionaryUpdateReq
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("/api/dictionary/rr-structure")
class RrStructureDictionaryController(
    val repo: RrStructureDictionaryExposedRepo
) {
    @Tag(name = "Справочник \"Структура ремонтов (доля)\"")
    @Operation(summary = "Получение данных справочника")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на получение данных справочника",
        content = [Content(schema = Schema(implementation = RrStructureDictionaryListReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ",
                content = [Content(schema = Schema(implementation = RrStructureDictionaryResp::class))]
            )
        ]
    )
    @PostMapping("/dict")
    fun getDictionary(@RequestBody req: RrStructureDictionaryListReq) = repo.getDictionary(req)

    @Tag(name = "Справочник \"Структура ремонтов (доля)\"")
    @Operation(summary = "Редактирование записи")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на редактирование записи",
        content = [Content(schema = Schema(implementation = RrStructureDictionaryUpdateReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ"
            )
        ]
    )
    @PostMapping("/updateRates")
    fun updateRates(
        @RequestBody req: RrStructureDictionaryUpdateReq,
        token: JwtToken?
    ) = repo.updateRates(req, token.username)

    @Tag(name = "Справочник \"Структура ремонтов (доля)\"")
    @Operation(summary = "Словарь для фильтров")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ",
                content = [Content(schema = Schema(implementation = GetAtpResp::class))]
            )
        ]
    )
    @GetMapping("/filters")
    fun getAtp() = repo.getAtp()

    @Tag(name = "Справочник \"Структура ремонтов (доля)\"")
    @Operation(summary = "Получения счётчика для справочников")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ",
                content = [Content(schema = Schema(implementation = List::class))]
            )
        ]
    )
    @GetMapping("/checkNew")
    fun checkNew() = repo.checkNew()

    @Tag(name = "Справочник \"Структура ремонтов (доля)\"")
    @Operation(summary = "Экспорт")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на экспорт справочника",
        content = [Content(schema = Schema(implementation = RrStructureDictionaryListReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(
                        type = "string",
                        format = "binary"
                    )
                )]
            )
        ]
    )
    @PostMapping("/export")
    fun export(@RequestBody req: RrStructureDictionaryListReq) = repo.export(
        RrStructureDictionaryListReqWithYearList(
            pageSize = req.pageSize,
            pageNumber = req.pageNumber,
            sort = req.sort,
            filters = req.filters,
            year = listOf(req.year)
        )
    )

    @Tag(name = "Справочник \"Структура ремонтов (доля)\"")
    @Operation(summary = "Экспорт для импорта")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на экспорт для импорта справочника",
        content = [Content(schema = Schema(implementation = DetailedRowForImportReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(
                        type = "string",
                        format = "binary"
                    )
                )]
            )
        ]
    )
    @PostMapping("/detailedRowForImport/export")
    fun exportForImport(@RequestBody req: DetailedRowForImportReq) = repo.export(
        RrStructureDictionaryListReqWithYearList(
            pageSize = 1,
            filters = listOf(
                RrStructureDictionaryFilter(
                    name = RrStructureDictionaryField.atpId,
                    condition = FilterCondition.contain,
                    value = req.atpId)
            ),
            year = req.year
        ),
        forImport = true
    )

    @Tag(name = "Справочник \"Структура ремонтов (доля)\"")
    @Operation(summary = "Импорт")
    @PostMapping("/import", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun import(token: JwtToken?, body: MultipartFile): RrImportResponse {
        return repo.import(body.inputStream, token.username)
    }
}