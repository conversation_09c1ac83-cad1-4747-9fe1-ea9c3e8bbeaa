package com.x5.logistics.rest.dto.som

import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate
import java.time.LocalTime

@Schema(description = "Ответ на детальный отчет СОМ.")
data class SomReportResponse(
    val count: Long,
    val pageNumber: Int,
    val pageSize: Int,
    val items: List<SomReportItem>,
    val pageTotal: Int,
)

data class SomReportItem(
    val vehicleLicense: String?,
    val vehicleGroup: String?,
    val vehicleType: String?,
    val vehicleBrand: String?,
    val vehicleModel: String?,
    val vehicleTonnage: Float?,
    val vehicleCreateYear: Int?,
    val vehicleVin: String?,
    val vehicleId: String?,
    val trailerLicenseNum: String?,
    val ter: String?,
    val mr: String?,
    val atp: String?,
    val mvz: String?,
    val mvzName: String?,
    val retailNetwork: String?,
    val atpType: String?,
    val mvzType: String?,
    val TMSRouteNum: String?,
    val NQRouteNum: String?,
    val retailNetworkOrder: String?,
    val rcName: String?,
    val idRcSAP: String?,
    val logisticsRcName: String?,
    val idLogisticsRcSAP: String?,
    val vehicleArriveDate: LocalDate?,
    val vehicleArriveTime: LocalTime?,
    val vehicleRcRegDate: LocalDate?,
    val vehicleRcRegTime: LocalTime?,
    val ourTripFlag: String?,
    val charaterDelivery: String?,
    val rnAndCharaterDelivery: String?,
    val ttCodeSAP: String?,
    val ttCodeNQ: String?,
    val ttSending: String?,
    val ttArrival: String?,
    val ttVisitingWayPlan: Int?,
    val ttVisitingWayFact: Int?,
    val ttArriveDatePlan: LocalDate?,
    val ttArriveTimePlan: LocalTime?,
    val ttArriveDateFact: LocalDate?,
    val ttArriveTimeFact: LocalTime?,
    val ttScanPalletDate: LocalDate?,
    val ttScanPalletTime: LocalTime?,
    val deliveryInHourGPSStatus: String?,
    val deliveryInHourPalletStatus: String?,
    val carInTime: Double?,
    val carInGPS: Double?,
    val deliveryInHourPlan: Double?,
    val temperaturePlan: Double?,
    val deliveryInPlan: Double?,
    val temperature: Double?,
    val tempStatus: String?,
    val tripsCount: Long?,
    val pointsCount: Long?,
)
