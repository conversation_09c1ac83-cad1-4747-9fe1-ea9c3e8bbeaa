package com.x5.logistics.rest.controller

import com.x5.logistics.rest.dto.OffsetLimitPagedResponse
import com.x5.logistics.rest.dto.dictionary.rr.AtpTonnagesFilter
import com.x5.logistics.rest.dto.dictionary.rr.DeleteRrDictionaryGroupReq
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryDetailedGroupDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryDictDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryFiltersDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupIdDto
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupsListReq
import com.x5.logistics.rest.dto.dictionary.rr.RrExportForImportRequest
import com.x5.logistics.rest.dto.dictionary.rr.RrImportResponse
import com.x5.logistics.rest.dto.dictionary.rr.UpdateRrDictionaryGroupReq
import com.x5.logistics.service.dictionary.rr.RrDictionaryExportService
import com.x5.logistics.service.dictionary.rr.RrDictionaryImportService
import com.x5.logistics.service.dictionary.rr.RrDictionaryService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.net.URLEncoder
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("api/dictionary/rr")
class RrDictionaryController(
    private val service: RrDictionaryService,
    private val exportService: RrDictionaryExportService,
    private val importService: RrDictionaryImportService,
) {
    private val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss")

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Получение записей словаря.")
    @PostMapping("listRows")
    fun getList(
        @RequestBody req: RrDictionaryGroupsListReq
    ): OffsetLimitPagedResponse<RrDictionaryGroupDto> {
        val items = service.getRrDictionaryGroups(req)
        val count = service.countRrDictionaryGroups(req)
        return OffsetLimitPagedResponse(
            count = count,
            offset = req.pageNumber * req.pageSize,
            items = items
        )
    }

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Получение полной записи словаря.")
    @PostMapping("detailedRow")
    fun getDetailed(
        @RequestBody req: RrDictionaryGroupIdDto
    ): RrDictionaryDetailedGroupDto =
        service.getRrDictionaryDetailedGroup(req)

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Создание новой строки словаря.")
    @PostMapping("newRow")
    fun createNewRow(
        @RequestBody req: RrDictionaryGroupIdDto,
        token: JwtToken?
    ): RrDictionaryGroupDto =
        service.createRrDictionaryGroup(token.username, req)

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Удаление строки словаря.")
    @PostMapping("deleteRow")
    fun deleteRow(
        @RequestBody req: DeleteRrDictionaryGroupReq
    ) {
        service.deleteRrDictionaryGroup(req)
    }

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Изменение значений тарифов в строке.")
    @PostMapping("updateRates")
    fun updateRates(
        @RequestBody req: UpdateRrDictionaryGroupReq,
        token: JwtToken?
    ): RrDictionaryDetailedGroupDto =
        service.updateRrDictionaryGroup(token.username, req)

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Получение словарных значений.")
    @GetMapping("dict")
    fun dict(): RrDictionaryDictDto =
        service.getRrDictionaryDict()

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Получение значений для фильтров.")
    @GetMapping("filters")
    fun filters(
        @RequestParam(name = "atpId", required = false) atpId: Long?
    ): RrDictionaryFiltersDto =
        service.getRrDictionaryFilters(
            atpId = atpId
        )

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Получение справочника АТП + тоннаж")
    @GetMapping("filter-atp-tonnages")
    fun getFilterAtpTonnages(): List<AtpTonnagesFilter> =
        service.getFilterAtpTonnages()

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Получение Excel отчета словаря тарифов руб/км ремонтов.")
    @PostMapping("export")
    fun getRrDictionaryExcel(@RequestBody req: RrDictionaryGroupsListReq): ResponseEntity<ByteArray> {
        val reportDate = dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))
        val fileName = "Справочник Тариф рубкм ремонтов_$reportDate.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(exportService.export(req))
    }

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Получение Excel отчета словаря тарифов руб/км ремонтов.")
    @PostMapping("detailedRow/export")
    fun getDetailedRrDictionaryExcel(@RequestBody req: RrDictionaryGroupsListReq): ResponseEntity<ByteArray> {
        val reportDate = dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))
        val fileName = "Справочник Тариф рубкм ремонтов с детализацией по ВТР_$reportDate.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(exportService.detailedExport(req))
    }

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Получение Excel отчета словаря тарифов руб/км ремонтов для импорта.")
    @PostMapping("detailedRowForImport/export")
    fun exportForImport(@RequestBody req: RrExportForImportRequest): ResponseEntity<ByteArray> {
        val fileName = "Справочник Тарифы.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(importService.exportForImport(req))
    }

    @Tag(name = "Словарь тарифов руб/км ремонтов")
    @Operation(summary = "Импорт словаря тарифов руб/км ремонтов.")
    @PostMapping("detailedRowForVRT/import", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun getDetailedRrDictionaryImport(token: JwtToken?, body: MultipartFile): RrImportResponse {
        return importService.import(body.inputStream, token.username)
    }
}
