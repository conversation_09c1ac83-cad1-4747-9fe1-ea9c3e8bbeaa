package com.x5.logistics.rest.dto

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Постраничный результат.")
open class PagedResponse<D>(
    @Schema(description = "Общее количество записей.")
    val count: Int,
    @Schema(description = "Номер страницы.")
    val pageNumber: Int,
    @Schema(description = "Размер страницы.")
    val pageSize: Int,
    @Schema(description = "Значения.")
    val items: Collection<D>
) {
    @Schema(description = "Всего страниц.")
    val pageTotal: Int = if (pageSize != 0) (count + pageSize - 1) / pageSize else 1
}
