package com.x5.logistics.rest.dto.fuel.widget

import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос данных виджета ДТ.")
data class FuelWidgetDieselReq(
    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilt<PERSON>,
    @Schema(description = "Дата начала периода.")
    val from: LocalDate,
    @Schema(description = "Дата конца периода.")
    val to: LocalDate
)
