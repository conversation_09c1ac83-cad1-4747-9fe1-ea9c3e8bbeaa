package com.x5.logistics.rest.dto.dictionary.kip

import java.time.LocalDate

data class KipDictionaryGroupEntry(
    val id: String,
    val macroRegionId: Long,
    val macroRegionName: String,
    val atpId: Long,
    val atpName: String,
    val mvzId: String?,
    val mvzName: String?,
    val tonnage: Double,
    val percentage: Double,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val author: String,
    val children: List<KipDictionaryEntry>
)
