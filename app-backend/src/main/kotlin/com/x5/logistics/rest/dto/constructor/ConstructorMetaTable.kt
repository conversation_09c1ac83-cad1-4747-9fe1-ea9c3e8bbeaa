package com.x5.logistics.rest.dto.constructor

import com.fasterxml.jackson.annotation.JsonIgnore
import com.x5.logistics.data.constructor.ConstructorMetaBiAggregatedFunction
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.service.ConstructorService
import org.jetbrains.exposed.sql.Table

data class ConstructorMetaTable(
    val id: String,
    val schema: String,
    val name: String,
    val columns: List<ConstructorMetaColumn>,
    @JsonIgnore val exposed: Table,
    val biProperties: List<BiTable>
) {
    data class BiTable(
        val label: String,
        val icon: String,
        val globalFilterExpression: ConstructorService.Expression,
        val values: List<BiValue>
    )

    data class BiValue(
        val name: String,
        val expression: ConstructorService.Expression,
        val expressionType: ColumnType,
        val label: String,
        val shortLabel: String,
        val functions: List<ConstructorMetaBiAggregatedFunction>
    )
}


