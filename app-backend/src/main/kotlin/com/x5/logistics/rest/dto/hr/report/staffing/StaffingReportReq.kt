package com.x5.logistics.rest.dto.hr.report.staffing

import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос шаблонизированного отчета по укомплектованности")
data class StaffingReportReq(
    @Schema(description = "Период детализации")
    val granularity: Granularity,

    @Schema(description = "Список периодов")
    val periods: List<LocalDate>
)
