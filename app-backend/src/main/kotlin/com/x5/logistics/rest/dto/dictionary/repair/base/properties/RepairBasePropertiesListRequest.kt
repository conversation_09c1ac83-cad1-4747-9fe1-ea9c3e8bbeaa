package com.x5.logistics.rest.dto.dictionary.repair.base.properties

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Запрос справочника характеристик ремзон")
data class RepairBasePropertiesListRequest(
    @Schema(description = "Номер страницы.")
    val pageNumber: Int,

    @Schema(description = "Размер страницы.")
    val pageSize: Int,

    @Schema(description = "Список фильтров.")
    val filters: List<RepairBasePropertiesColumnFilter>,

    @Schema(description = "Список сортировок.")
    var sort: List<RepairBasePropertiesSortOrder> = listOf(
        RepairBasePropertiesSortOrder(RepairBasePropertiesColumn.rsName, true)
    ),

    @Schema(description = "Год.")
    val year: Int
) {
    init {
        if (this.sort.isEmpty()) {
            this.sort = listOf(
                RepairBasePropertiesSortOrder(
                    column = RepairBasePropertiesColumn.rsName,
                    asc = true
                )
            )
        }
    }
}
