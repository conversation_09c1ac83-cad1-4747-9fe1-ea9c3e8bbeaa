package com.x5.logistics.rest.controller

import com.x5.logistics.repository.DictionaryRepository
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailsColumn
import com.x5.logistics.rest.dto.waybill.WaybillItemDetailsColumn
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.Parameters
import io.swagger.v3.oas.annotations.enums.ParameterIn
import io.swagger.v3.oas.annotations.media.ArraySchema
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
class DictionaryFilterController(
    val dictionaryRepository: DictionaryRepository
) {
    private val log = getLogger()

    @Tag(name = "Словари")
    @Operation(summary = "Получение фильтрованных данных из словаря.")
    @Parameters(
        value = [
            Parameter(name = "column", description = "Колонка словаря.", `in` = ParameterIn.QUERY),
            Parameter(name = "filter", description = "Подстрока поиска.", `in` = ParameterIn.QUERY),
            Parameter(name = "limit", description = "Лимит результатов.", `in` = ParameterIn.QUERY)
        ]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [
                    Content(
                        array = ArraySchema(
                            schema = Schema(
                                implementation = LabeledValue::class
                            )
                        )
                    )
                ]
            )
        ]
    )
    @GetMapping("/api/dictionary/filter")
    fun process(
        @RequestParam("column") column: VehicleDetailsColumn,
        @RequestParam("filter") filter: String? = "",
        @RequestParam("limit") limit: Int,
    ): List<LabeledValue> {
        log.debug("Get filtered dictionary values. [column={}, filter={}, limit={}]", column, filter, limit)

        return dictionaryRepository.getFilteredValues(column, filter ?: "", limit)
    }


    @Tag(name = "Словари")
    @Operation(summary = "Получение фильтрованных данных по ПЛ из словаря.")
    @Parameters(
        value = [
            Parameter(name = "column", description = "Колонка словаря.", `in` = ParameterIn.QUERY),
            Parameter(name = "filter", description = "Подстрока поиска.", `in` = ParameterIn.QUERY),
            Parameter(name = "limit", description = "Лимит результатов.", `in` = ParameterIn.QUERY)
        ]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [
                    Content(
                        array = ArraySchema(
                            schema = Schema(
                                implementation = LabeledValue::class
                            )
                        )
                    )]
            )]
    )
    @GetMapping("/api/dictionary/filter/waybill")
    fun process(
        @RequestParam("column") column: WaybillItemDetailsColumn,
        @RequestParam("filter") filter: String,
        @RequestParam("limit") limit: Int,
    ): List<LabeledValue> {
        log.debug("Get filtered repair dictionary values. [column={}, filter={}, limit={}]", column, filter, limit)

        return dictionaryRepository.getWaybillFilteredValues(column, filter, limit)
    }
}
