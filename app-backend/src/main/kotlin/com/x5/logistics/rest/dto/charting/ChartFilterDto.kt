package com.x5.logistics.rest.dto.charting

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Доступный фильтр для конструктора")
data class ChartFilterDto(
    @Schema(description = "Доступный фильтр для конструктора")
    val name: String,
    @Schema(description = "Доступный фильтр для конструктора")
    val label: ChartColumn,
    @Schema(description = "Доступный фильтр для конструктора")
    val group: String,
    val restrictions: List<ChartColumn>
)
