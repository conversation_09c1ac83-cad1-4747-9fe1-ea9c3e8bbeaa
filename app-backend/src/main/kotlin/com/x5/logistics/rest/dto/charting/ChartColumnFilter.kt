package com.x5.logistics.rest.dto.charting

import com.x5.logistics.rest.dto.ColumnFilter
import com.x5.logistics.rest.dto.FilterCondition
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Описание фильтра.")
data class ChartColumnFilter(
    @Schema(description = "Колонка для фильтра.")
    override val name: ChartColumn,
    @Schema(description = "Условие для фильтра.")
    override val condition: FilterCondition,
    @Schema(description = "Список значений.")
    override val value: List<Any>
) : ColumnFilter(name, condition, value)
