package com.x5.logistics.rest.dto.airflowmonitoring

import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Schema(description = "Состояние обновления данных")
data class DataStatusDto(
    @Schema(description = "Текущая дата и время по МСК")
    val date: LocalDateTime,

    @Schema(description = "Текущий статус обновления данных")
    val currentStatus: String,

    @Schema(description = "Информация о следующем обновлении данных")
    val nextUpdateStatus: String
)