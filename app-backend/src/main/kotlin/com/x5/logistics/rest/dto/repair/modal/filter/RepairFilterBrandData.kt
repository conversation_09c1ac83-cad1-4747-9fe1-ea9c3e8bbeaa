package com.x5.logistics.rest.dto.repair.modal.filter

import io.swagger.v3.oas.annotations.media.Schema

/**
 * Implementation of JRAAVTO-40 https://wiki.x5.ru/pages/viewpage.action?pageId=280896401
 * <AUTHOR> ( <EMAIL> )
 */

@Schema(description = "Данные  по маркам ТС.")
data class RepairFilterBrandData(
    @Schema(description = "Марка ТС.")
    val label: String?,

    @Schema(description = "Марка ТС.")
    val value: String?,

    @Schema(description = "Список моделей ТС.")
    val children: List<RepairFilterBrandModelData>?
)