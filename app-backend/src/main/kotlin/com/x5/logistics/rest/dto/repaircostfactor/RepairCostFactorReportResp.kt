package com.x5.logistics.rest.dto.repaircostfactor

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.rest.dto.GranularityItem
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Ответ на детальный отчет Затраты на ремонты АТП (руб/км)")
data class RepairCostFactorReportResp(
    @Schema(description = "Общее количество записей")
    val count: Int,
    @Schema(description = "Номер страницы")
    val pageNumber: Int,
    @Schema(description = "Размер страницы")
    val pageSize: Int,
    @Schema(description = "Затраты, руб")
    val totalRepairExpensesFull: Double,
    @Schema(description = "Факт, руб/км")
    val totalRepairRubFact: Double,
    @Schema(description = "Значения")
    val items: Collection<RepairCostFactorReportItem>,
) {
    @Schema(description = "Всего страниц")
    val pageTotal: Int = (count + pageSize - 1) / pageSize
}

@Schema(description = "Элемент детального отчета Затраты на ремонты АТП (руб/км)")
data class RepairCostFactorReportItem(
    @Schema(description = "Территория")
    val ter: String?,
    @Schema(description = "Макрорегион")
    val mr: String?,
    @Schema(description = "АТП")
    val atp: String?,
    @Schema(description = "МВЗ ТС")
    val mvz: String?,
    @Schema(description = "Название МВЗ ТС")
    val mvzName: String?,
    @Schema(description = "Вид деятельности АТП")
    val atpType: String?,
    @Schema(description = "Тип МВЗ ТС")
    val mvzType: String?,
    @Schema(description = "Гос. номер ТС")
    val vehicleLicense: String?,
    @Schema(description = "Вид ТС")
    val vehicleGroup: String?,
    @Schema(description = "Тип ТС")
    val vehicleType: String?,
    @Schema(description = "Марка")
    val vehicleBrand: String?,
    @Schema(description = "Модель")
    val vehicleModel: String?,
    @Schema(description = "Тоннаж")
    val vehicleTonnage: Double?,
    @Schema(description = "Год выпуска")
    val vehicleYear: Int?,
    @Schema(description = "Дата ввода в эксплуатацию")
    @JsonFormat(pattern = "dd.MM.yyyy")
    val vehicleCreateDate: LocalDate?,
    @Schema(description = "VIN номер")
    val vehicleVin: String?,
    @Schema(description = "Единица оборудования")
    val equnr: String?,
    @Schema(description = "ГБО")
    val gbo: String?,
    @Schema(description = "Паллетовместимость")
    val compartAmount: Int?,
    @Schema(description = "Гос. номер прицепа")
    val trailerLicenseNum: String?,
    @Schema(description = "Затраты, руб")
    val repairExpensesFull: Double?,
    @Schema(description = "Пробег за период, км")
    val mileage: Double?,
    @Schema(description = "Факт, руб/км")
    val repairRub: Double?,
    @Schema(description = "Структура затрат")
    val structureName: String?,
    @Schema(description = "Место ремонта")
    val repairPlace: String?,
    @Schema(description = "Вид ремонта")
    val reqType: String?,
    @Schema(description = "Подвид ремонта")
    val reqSubtype: String?,
    @Schema(description = "Код ВРТ")
    val vrt: String?,
    @Schema(description = "ВРТ")
    val vrtName: String?,
    @Schema(description = "Код события")
    val eventId: String?,
    @Schema(description = "Событие")
    val eventText: String?,
    @Schema(description = "План. затраты, руб")
    val repairExpensesFullPlan: Double?,
    @Schema(description = "Отклонение затрат от плана, руб")
    val repairExpensesFullDeviation: Double?,
    @Schema(description = "Тариф, руб/км")
    val repairRubPlan: Double?,
    @Schema(description = "Отклонение факта от тарифа, руб/км")
    val repairRubDeviation: Double?,
    @Schema(description = "План. затраты по гранулярным периодам")
    val granularityRepairExpensesFullPlan: List<GranularityItem>?,
    @Schema(description = "Затраты по гранулярным периодам")
    val granularityRepairExpensesFull: List<GranularityItem>?,
    @Schema(description = "Отклонение затрат от плана по гранулярным периодам")
    val granularityRepairExpensesFullDeviation: List<GranularityItem>?,
    @Schema(description = "Тариф по гранулярным периодам")
    val granularityRepairRubPlan: List<GranularityItem>?,
    @Schema(description = "Факт по гранулярным периодам")
    val granularityRepairRub: List<GranularityItem>?,
    @Schema(description = "Отклонение факта от тарифа по гранулярным периодам")
    val granularityRepairRubDeviation: List<GranularityItem>?
)