package com.x5.logistics.rest.controller.repair

import com.x5.logistics.repository.repair.RepairModalWidgetRepo
import com.x5.logistics.rest.dto.repair.modal.RepairModalWidgetReq
import com.x5.logistics.rest.dto.repair.modal.RepairModalWidgetResp
import com.x5.logistics.rest.dto.repair.modal.filter.RepairFilterBrandReq
import com.x5.logistics.rest.dto.repair.modal.filter.RepairFilterBrandResp
import com.x5.logistics.rest.dto.repair.modal.rubkm.RepairModalRubKmEffectReq
import com.x5.logistics.rest.dto.repair.modal.rubkm.RepairModalRubKmEffectResp
import com.x5.logistics.rest.dto.repair.workspace.rubkm.RepairWidgetRubKmEffectReq
import com.x5.logistics.service.RepairEffectService
import com.x5.logistics.service.repair.RepairModalWidgetService
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

/**
 * Mock for JRAAVTO-40 https://wiki.x5.ru/pages/viewpage.action?pageId=280896401
 * <AUTHOR>
 * Implementation of JRAAVTO-40 https://wiki.x5.ru/pages/viewpage.action?pageId=280896401
 * <AUTHOR> Chayn ( <EMAIL> )
 */

@RestController
@RequestMapping("api/repair")
class RepairModalRubKmController(
    val service: RepairEffectService,
    val repo: RepairModalWidgetRepo,
    val rubKmService: RepairModalWidgetService,
) {
    private val log = getLogger()

    @Tag(name = "Ремонт")
    @Operation(summary = "Данные по ремонтам - модальный виджет руб/км по моделям ТС.")
    @SwaggerReqBody(
        description = "Запрос данных по ремонтам - модальный виджет руб/км по моделям ТС.",
        content = [Content(schema = Schema(implementation = RepairWidgetRubKmEffectReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = RepairModalRubKmEffectResp::class))]
            )
        ]
    )
    @PostMapping("/effect")
    fun getDetail(@RequestBody req: RepairModalRubKmEffectReq): RepairModalRubKmEffectResp {
        log.debug("Get repair data for modal widget. Req={}", req)

        return service.getRepairEffect(req)
    }

    @Tag(name = "Ремонт")
    @Operation(summary = "Запрос фильтра по маркам и моделям ТС.")
    @SwaggerReqBody(
        description = "Запрос фильтра по маркам и моделям ТС.",
        content = [Content(schema = Schema(implementation = RepairFilterBrandReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = RepairFilterBrandResp::class))]
            )
        ]
    )
    @PostMapping("/vehicles")
    fun getFuelEffectiveness(@RequestBody req: RepairFilterBrandReq): RepairFilterBrandResp {
        log.debug("Get fuel data of vehicles: {}", req)

        return service.getListBrand(req)
    }

    @Tag(name = "Ремонт")
    @Operation(summary = "Запрос модального виджета Затраты на ремонт.")
    @SwaggerReqBody(
        description = "Запрос модального виджета Затраты на ремонт.",
        content = [Content(schema = Schema(implementation = RepairModalWidgetReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = RepairModalWidgetResp::class))]
            )
        ]
    )
    @PostMapping("/km-cost")
    suspend fun getRubKmWidget(@RequestBody req: RepairModalWidgetReq): RepairModalWidgetResp {
        return rubKmService.getData(req)
    }
}