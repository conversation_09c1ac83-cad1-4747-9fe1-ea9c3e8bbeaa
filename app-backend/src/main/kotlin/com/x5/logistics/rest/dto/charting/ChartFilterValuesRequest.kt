package com.x5.logistics.rest.dto.charting

import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос доступных значений для фильтров")
data class ChartFilterValuesRequest(
    @Schema(description = "Дата начала периода")
    val from: LocalDate,
    @Schema(description = "Дата конца периода")
    val to: LocalDate,
    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,
    @Schema(description = "Список примененных фильтров.")
    val filters: List<ChartColumnFilter>,
    @Schema(description = "Фильтр для которого нужны нужны доступные значения")
    val request: ChartColumnFilterValueRequest
)
