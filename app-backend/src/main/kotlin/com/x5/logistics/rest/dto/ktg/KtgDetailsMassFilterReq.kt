package com.x5.logistics.rest.dto.ktg

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailsColumn
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailsColumnFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос значений фильтра на детальный отчет по КТГ.")
data class KtgDetailsMassFilterReq(
    @Schema(description = "Дата начала периода.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val from: LocalDate,

    @Schema(description = "Дата конца периода.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val to: LocalDate,

    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,

    @Schema(description = "Список колонок.")
    val columns: List<VehicleDetailsColumn>,

    @Schema(description = "Список фильтров.")
    val filters: List<VehicleDetailsColumnFilter>,

    @Schema(description = "Запрос значений фильтра.")
    val request: KtgDetailsMassFilterDataReq,
)

data class KtgDetailsMassFilterDataReq(
    val name: VehicleDetailsColumn,
    val value: List<String>,
)
