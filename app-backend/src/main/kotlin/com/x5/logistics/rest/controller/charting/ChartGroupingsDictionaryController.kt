package com.x5.logistics.rest.controller.charting

import com.x5.logistics.rest.dto.charting.ChartGroupingsDictionaryDto
import com.x5.logistics.service.charting.ChartGroupingsDictionaryService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.ArraySchema
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/chart/groupings")
class ChartGroupingsDictionaryController(
    val service: ChartGroupingsDictionaryService
) {

    @Tag(name = "Конструктор ремонтов")
    @Operation(summary = "Получение словаря разрезов данных.")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [
                    Content(array = ArraySchema(schema = Schema(implementation = ChartGroupingsDictionaryDto::class)))
                ]
            )
        ]
    )
    @GetMapping
    @ResponseBody
    fun getAll(): List<ChartGroupingsDictionaryDto> =
        service.chartGroupings
}
