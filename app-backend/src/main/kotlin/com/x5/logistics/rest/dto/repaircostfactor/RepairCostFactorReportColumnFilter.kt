package com.x5.logistics.rest.dto.repaircostfactor

import com.x5.logistics.rest.dto.FilterCondition
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Описание фильтра для детального отчета по амортизации")
data class RepairCostFactorReportColumnFilter(
    @Schema(description = "Колонка для фильтра")
    val name: RepairCostFactorReportColumn,
    @Schema(description = "Тип фильтра")
    val condition: FilterCondition,
    @Schema(description = "Список значений")
    val value: List<Any>
)
