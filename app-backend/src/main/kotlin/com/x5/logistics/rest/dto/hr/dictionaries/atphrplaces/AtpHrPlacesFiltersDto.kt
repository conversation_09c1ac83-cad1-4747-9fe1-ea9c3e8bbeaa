package com.x5.logistics.rest.dto.hr.dictionaries.atphrplaces

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Значения для фильтров справочника АТП - площадок")
data class AtpHrPlacesFiltersDto(
    @Schema(description = "Наименование АТП")
    val atpName: Collection<String?>,
    @Schema(description = "Наименование площадки")
    val placeName: Collection<String?>,
    @Schema(description = "Пользователь")
    val updatedBy: Collection<String?>,
)
