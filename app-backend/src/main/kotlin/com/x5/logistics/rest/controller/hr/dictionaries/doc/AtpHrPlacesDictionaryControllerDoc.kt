package com.x5.logistics.rest.controller.hr.dictionaries.doc

import com.x5.logistics.rest.dto.PagedResponse
import com.x5.logistics.rest.dto.hr.dictionaries.atphrplaces.AtpHrPlacesDictionaryCreateRequest
import com.x5.logistics.rest.dto.hr.dictionaries.atphrplaces.AtpHrPlacesDictionaryDeleteRequest
import com.x5.logistics.rest.dto.hr.dictionaries.atphrplaces.AtpHrPlacesDictionaryDto
import com.x5.logistics.rest.dto.hr.dictionaries.atphrplaces.AtpHrPlacesDictionaryListRequest
import com.x5.logistics.rest.dto.hr.dictionaries.atphrplaces.AtpHrPlacesDictionaryUpdateRequest
import com.x5.logistics.rest.dto.hr.dictionaries.atphrplaces.AtpHrPlacesFiltersDto
import com.x5.logistics.rest.dto.hr.dictionaries.atphrplaces.HrPlaceListDto
import com.x5.logistics.util.JwtToken
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.core.io.Resource
import org.springframework.http.ResponseEntity

interface AtpHrPlacesDictionaryControllerDoc {

    @Tag(name = "Словарь АТП - площадок")
    @Operation(summary = "Получение справочника АТП - площадок")
    @RequestBody(
        description = "Запрос справочника АТП - площадок",
        content = [Content(schema = Schema(implementation = AtpHrPlacesDictionaryListRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = PagedResponse::class))
                ]
            )
        ]
    )
    fun getList(req: AtpHrPlacesDictionaryListRequest): PagedResponse<AtpHrPlacesDictionaryDto>

    @Tag(name = "Словарь АТП - площадок")
    @Operation(summary = "Экспорт в XLSX справочника АТП - площадок")
    @RequestBody(
        description = "Запрос на экспорт справочника АТП - площадок",
        content = [Content(schema = Schema(implementation = AtpHrPlacesDictionaryListRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(type = "string", format = "binary")
                )]
            )
        ]
    )
    fun exportToXlsx(req: AtpHrPlacesDictionaryListRequest): ResponseEntity<Resource>

    @Tag(name = "Словарь АТП - площадок")
    @Operation(summary = "Создание записи справочника АТП - площадок")
    @RequestBody(
        description = "Запрос создания записи справочника АТП - площадок",
        content = [Content(schema = Schema(implementation = AtpHrPlacesDictionaryCreateRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = ResponseEntity::class))
                ]
            )
        ]
    )
    fun create(token: JwtToken?, req: AtpHrPlacesDictionaryCreateRequest): ResponseEntity<AtpHrPlacesDictionaryDto>

    @Tag(name = "Словарь АТП - площадок")
    @Operation(summary = "Обновление записи справочника АТП - площадок")
    @RequestBody(
        description = "Запрос обновления записи справочника АТП - площадок",
        content = [Content(schema = Schema(implementation = AtpHrPlacesDictionaryUpdateRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = PagedResponse::class))
                ]
            )
        ]
    )
    fun update(token: JwtToken?, req: AtpHrPlacesDictionaryUpdateRequest): ResponseEntity<AtpHrPlacesDictionaryDto>

    @Tag(name = "Словарь АТП - площадок")
    @Operation(summary = "Удаление записи справочника АТП - площадок")
    @RequestBody(
        description = "Запрос удаления записи справочника АТП - площадок",
        content = [Content(schema = Schema(implementation = AtpHrPlacesDictionaryDeleteRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = ResponseEntity::class))
                ]
            )
        ]
    )
    fun delete(token: JwtToken?, req: AtpHrPlacesDictionaryDeleteRequest): ResponseEntity<AtpHrPlacesDictionaryDto>

    @Tag(name = "Словарь АТП - площадок")
    @Operation(summary = "Получение списка площадок")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = List::class))
                ]
            )
        ]
    )
    fun getAll(): List<HrPlaceListDto>

    @Tag(name = "Словарь АТП - площадок")
    @Operation(summary = "Получение списка значений фильтров справочника АТП - площадок")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = List::class))
                ]
            )
        ]
    )
    fun getFilterValues(): AtpHrPlacesFiltersDto
}