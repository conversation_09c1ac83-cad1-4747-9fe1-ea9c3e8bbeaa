package com.x5.logistics.rest.controller.dictionary

import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.dictionary.GlobalFilterDictionaryValuesDto
import com.x5.logistics.service.dictionary.GlobalFilterDictionaryService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.ArraySchema
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/dictionary/filter")
class GlobalFilterDictionaryController(
    val service: GlobalFilterDictionaryService
) {

    @Tag(name = "Словари")
    @Operation(summary = "Получение значений для глобальных фильтров")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/json",
                    array = ArraySchema(schema = Schema(implementation = GlobalFilterDictionaryValuesDto::class))
                )
                ]
            )
        ]
    )
    @GetMapping("/geo")
    fun getFilterValues(): GlobalFilterDictionaryValuesDto = service.getGlobalFilterDictionary()

    @Tag(name = "Словари")
    @Operation(summary = "Получение списка ремзон.")
    @GetMapping("/repshops")
    fun process(): List<LabeledValue> {
        return service.getGlobalRepshopsDictionary()
    }
}
