package com.x5.logistics.rest.dto.operreport

import com.x5.logistics.rest.dto.GeoFilter
import java.time.LocalDate

data class OperReportMassFilterReq(
    val from: LocalDate,
    val to: LocalDate,
    val columns: List<OperReportColumn>,
    val filters: List<OperReportColumnFilter>,
    val geoFilter: GeoFilter,
    val request: Request
) {
    data class Request(
        val name: OperReportColumn,
        val value: List<String>
    )
}
