package com.x5.logistics.rest.dto

import com.fasterxml.jackson.databind.JsonNode
import com.x5.logistics.data.dictionary.UnitTable
import com.x5.logistics.repository.ArrayFunction
import com.x5.logistics.repository.Granularity
import com.x5.logistics.repository.JsonAgg
import com.x5.logistics.repository.generateDateSeriesWithInterval
import com.x5.logistics.repository.getGranularityFrom
import com.x5.logistics.repository.mapper
import com.x5.logistics.repository.plusInterval
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.TextColumnType
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.javatime.dateLiteral
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.Month
import java.time.format.DateTimeFormatter
import java.util.*

private const val LAST_WEEK_NUMBER = 52
private const val FIRST_WEEK_NUMBER = 1

fun produceSegments(granularity: Granularity, from: LocalDate, to: LocalDate): List<Pair<LocalDate, LocalDate>> {
    // Перемтотать дату начала до ближайшей округлённой даты
    val roundedFrom = findNextEndpoint(granularity, from)
    return produceEndpoints(granularity, roundedFrom).takeWhile {
        // Оставить только даты внутри отрезка
        it <= to
    }.zipWithNext { curr, next ->
        // Собрать из соседних дат временные отрезки
        curr to next
    }.toList()
}

fun produceLabels(granularity: Granularity, from: LocalDate, to: LocalDate): List<String> {
    val roundedFrom = findNextEndpoint(granularity, from)
    val fullEndpoints = produceEndpoints(granularity, roundedFrom).takeWhile { it <= to }
    return buildList {
        add(from)
        addAll(fullEndpoints)
    }.distinct().sorted()
        .map { produceGranularLabel(it, granularity) }
}

fun produceEndpoints(granularity: Granularity, from: LocalDate): Sequence<LocalDate> =
    generateSequence(from) { makeStep(granularity, it) }

private fun makeStep(granularity: Granularity, from: LocalDate): LocalDate = when (granularity) {
    Granularity.DAY -> from.plusDays(1)
    Granularity.WEEK -> from.plusWeeks(1)
    Granularity.REPORTING_WEEK -> from.plusWeeks(1)
    Granularity.MONTH -> from.plusMonths(1)
    Granularity.QUARTER -> from.plusMonths(3)
    Granularity.YEAR -> from.plusYears(1)
}

fun findNextEndpoint(granularity: Granularity, from: LocalDate): LocalDate = when (granularity) {
    Granularity.DAY -> from
    Granularity.WEEK -> {
        // Следующий понедельник
        var roundedFrom = from
        while (roundedFrom.dayOfWeek != DayOfWeek.MONDAY) {
            roundedFrom = roundedFrom.plusDays(1)
        }
        roundedFrom
    }

    Granularity.REPORTING_WEEK -> {
        // Следующая пятница
        var roundedFrom = from
        while (roundedFrom.dayOfWeek != DayOfWeek.FRIDAY) {
            roundedFrom = roundedFrom.plusDays(1)
        }
        roundedFrom
    }

    Granularity.MONTH -> {
        // Ближайшее начало месяца (или `from`, если выпало на 1-е число)
        if (from.dayOfMonth == 1) from
        else LocalDate.of(from.year, from.month, 1).plusMonths(1)
    }

    Granularity.QUARTER -> {
        // Ближайшее начало января, апреля, июля или октября (1, 4, 7 и 10 месяцы, с которых начинаются кварталы)
        val quarterStartMonths = setOf(Month.JANUARY, Month.APRIL, Month.JULY, Month.OCTOBER)
        if (from.dayOfMonth == 1 && quarterStartMonths.contains(from.month)) from
        else {
            var next = LocalDate.of(from.year, from.month, 1).plusMonths(1)
            while (!quarterStartMonths.contains(next.month)) next = next.plusMonths(1)
            next
        }
    }

    Granularity.YEAR -> {
        // Ближайшее 1 января
        if (from.dayOfMonth == 1 && from.month == Month.JANUARY) from
        else LocalDate.of(from.year + 1, Month.JANUARY, 1)
    }
}

fun isEdgesPartial(granularity: Granularity?, from: LocalDate, to: LocalDate): Pair<Boolean, Boolean> = when (granularity) {
    Granularity.DAY -> false to false
    Granularity.WEEK -> (from.dayOfWeek != DayOfWeek.MONDAY) to (to.dayOfWeek != DayOfWeek.SUNDAY)
    Granularity.REPORTING_WEEK -> (from.dayOfWeek != DayOfWeek.FRIDAY) to (to.dayOfWeek != DayOfWeek.THURSDAY)
    Granularity.MONTH -> (from.dayOfMonth != 1) to (to.dayOfMonth != to.month.length(to.isLeapYear))
    Granularity.QUARTER -> {
        val quarterStartMonths = setOf(Month.JANUARY, Month.APRIL, Month.JULY, Month.OCTOBER)
        val start = (from.dayOfMonth != 1) || (from.month !in quarterStartMonths)
        val quarterEndMonths = setOf(Month.MARCH, Month.JUNE, Month.SEPTEMBER, Month.DECEMBER)
        val end =
            (to.dayOfMonth != to.month.length(to.isLeapYear)) || (to.month !in quarterEndMonths)
        start to end
    }

    Granularity.YEAR -> {
        val start = (from.dayOfMonth != 1) || (from.month != Month.JANUARY)
        val end = (from.dayOfMonth != 31) || (from.month != Month.DECEMBER)
        start to end
    }

    null -> false to false
}

fun produceGranularLabel(date: LocalDate, granularity: Granularity): String {
    return when (granularity) {
        Granularity.DAY -> date.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))
        Granularity.WEEK -> "Н ${calcWeek(date)}.${date.year}"
        Granularity.REPORTING_WEEK -> formatReportingWeekDate(date)
        Granularity.MONTH -> date.format(DateTimeFormatter.ofPattern("LLL yyyy"))
        Granularity.QUARTER -> date.format(DateTimeFormatter.ofPattern("'Q' Q.yyyy"))
        Granularity.YEAR -> date.format(DateTimeFormatter.ofPattern("'Y' yyyy"))
    }
}

fun String.parseGranularity(
    granularityItem: Granularity?,
    from: LocalDate,
    to: LocalDate,
    mapJson: JsonNode.() -> Pair<String, Double?>
): List<GranularityItem> {
    val granularity = granularityItem ?: return emptyList()
    val segments = produceLabels(granularity, from, to)
    val items = mapper.readTree(this).elements().asSequence().map {
        it.mapJson()
    }.toMap()
    val res = segments.map {
        GranularityItem(
            label = it,
            value = items[it]
        )
    }
    val (startPartPeriod, endPartPeriod) = isEdgesPartial(granularity, from, to)
    res.firstOrNull()?.partPeriod = startPartPeriod
    res.lastOrNull()?.partPeriod = endPartPeriod
    return res
}

private fun formatReportingWeekDate(date: LocalDate): String {
    val weekNumber = calcWorkWeek(date)
    val year = when {
        weekNumber == FIRST_WEEK_NUMBER && date.month == Month.DECEMBER -> date.year + 1
        weekNumber == LAST_WEEK_NUMBER && date.month == Month.JANUARY -> date.year - 1
        else -> date.year
    }
    return "$weekNumber.$year"
}

private fun calcWeek(date: LocalDate): Int {
    val calendar = Calendar.getInstance()
    calendar.firstDayOfWeek = Calendar.MONDAY
    calendar.set(date.year, date.monthValue - 1, date.dayOfMonth)
    return calendar.get(Calendar.WEEK_OF_YEAR)
}

private fun calcWorkWeek(date: LocalDate): Int {
    val calendar = Calendar.getInstance()
    calendar.firstDayOfWeek = Calendar.FRIDAY
    calendar.set(date.year, date.monthValue - 1, date.dayOfMonth)
    return calendar.get(Calendar.WEEK_OF_YEAR)
}

class GranularitySubQueryData(
    granularity: Granularity,
    expression: Expression<*>,
    from: LocalDate,
    to: LocalDate
) {
    val startDay = generateDateSeriesWithInterval(
        start = getGranularityFrom(granularity, dateLiteral(from)),
        end = dateLiteral(to),
        interval = granularity.interval
    ).alias("s_date")

    val endDay = startDay.delegate.plusInterval(granularity.interval).alias("e_date")

    val daysQuery = UnitTable.select(startDay, endDay).alias("days")

    val granularExp = JsonAgg(ArrayFunction(listOf(startDay.aliasOnlyExpression(), expression), TextColumnType()))
}