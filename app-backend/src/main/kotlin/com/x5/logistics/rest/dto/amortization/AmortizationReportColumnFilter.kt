package com.x5.logistics.rest.dto.amortization

import com.x5.logistics.rest.dto.FilterCondition
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Описание фильтра для детального отчета по амортизации")
data class AmortizationReportColumnFilter(
    @Schema(description = "Колонка для фильтра")
    val name: AmortizationReportColumn,
    @Schema(description = "Тип фильтра")
    val condition: FilterCondition,
    @Schema(description = "Список значений")
    val value: List<Any>
)
