package com.x5.logistics.rest.controller.fuel

import com.x5.logistics.repository.fuel.FuelDetailsRepo
import com.x5.logistics.rest.dto.fuel.graph.FuelGraphReq
import com.x5.logistics.rest.dto.fuel.graph.FuelGraphResp
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController

@RestController
class FuelGraphController(
    val repo: FuelDetailsRepo
) {

    private val log = getLogger()

    @Tag(name = "Топливо")
    @Operation(summary = "Запрос графиков по топливу.")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос графиков по топливу.",
        content = [
            Content(
                schema = Schema(
                    implementation = FuelGraphReq::class
                )
            )
        ]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [
                    Content(
                        schema = Schema(
                            implementation = FuelGraphResp::class
                        )
                    )
                ]
            )
        ]
    )
    @PostMapping("api/fuel/graphs")
    fun getDetails(@RequestBody req: FuelGraphReq): FuelGraphResp {
        log.debug(
            "Get fuel graph data. [to={}, mvz={}, filters={}]",
            req.to, req.mvz, req.filters
        )

        return FuelGraphResp()
    }
}
