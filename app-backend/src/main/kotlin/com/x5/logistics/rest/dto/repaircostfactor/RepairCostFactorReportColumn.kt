package com.x5.logistics.rest.dto.repaircostfactor

import com.x5.logistics.data.PlsByDay
import com.x5.logistics.data.PlsByDayWithRate
import com.x5.logistics.data.RepairStructures
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksSubtypesTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksTypesTable
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.repository.castToString
import com.x5.logistics.rest.util.FilterGroup
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.ExpressionWithColumnType
import org.jetbrains.exposed.sql.alias

@Suppress("EnumEntryName")
enum class RepairCostFactorReportColumn(
    val columnTitle: String,
    val group: FilterGroup = FilterGroup.string,
    val aggregated: Boolean = false,
    val filterable: Boolean = true,
    val type: ColumnType = ColumnType.STRING,
    val exposedExpression: ExpressionWithColumnType<*>? = null,
    val plsExpression: ExpressionWithColumnType<*>? = null,
    val rateExpression: ExpressionWithColumnType<*>? = null,
    val repairExpression: ExpressionWithColumnType<*>? = null
) {
    vehicleLicense(
        columnTitle = "Гос. номер ТС",
        group = FilterGroup.stringSearch,
        plsExpression = PlsByDay.vehicleLicense,
        rateExpression = PlsByDayWithRate.vehicleLicense,
        repairExpression = RepairStructures.headLicense
    ),
    vehicleGroup(
        columnTitle = "Вид ТС",
        plsExpression = PlsByDay.tsGroup,
        rateExpression = PlsByDayWithRate.tsGroup,
        repairExpression = RepairStructures.headGroup
    ),
    vehicleType(
        columnTitle = "Тип ТС",
        plsExpression = PlsByDay.tsType,
        rateExpression = PlsByDayWithRate.tsType,
        repairExpression = RepairStructures.headType
    ),
    vehicleBrand(
        columnTitle = "Марка",
        plsExpression = PlsByDay.marka,
        rateExpression = PlsByDayWithRate.marka,
        repairExpression = RepairStructures.headMarka
    ),
    vehicleModel(
        columnTitle = "Модель",
        group = FilterGroup.stringSearch,
        plsExpression = PlsByDay.model,
        rateExpression = PlsByDayWithRate.model,
        repairExpression = RepairStructures.headModel
    ),
    vehicleTonnage(
        columnTitle = "Тоннаж",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        plsExpression = PlsByDay.plTonnage,
        rateExpression = PlsByDayWithRate.plTonnage,
        repairExpression = RepairStructures.vehicleTonnage
    ),
    vehicleYear(
        columnTitle = "Год выпуска",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        plsExpression = PlsByDay.year,
        rateExpression = PlsByDayWithRate.year,
        repairExpression = RepairStructures.headYear
    ),
    vehicleCreateDate(
        columnTitle = "Дата ввода в эксплуатацию",
        group = FilterGroup.date,
        type = ColumnType.DATE,
        plsExpression = PlsByDay.createDate,
        rateExpression = PlsByDayWithRate.createDate,
        repairExpression = RepairStructures.headCreateDate
    ),
    vehicleVin(
        columnTitle = "VIN номер",
        group = FilterGroup.stringSearch,
        plsExpression = PlsByDay.vin,
        rateExpression = PlsByDayWithRate.vin,
        repairExpression = RepairStructures.headFleetNum
    ),
    equnr(
        columnTitle = "Единица оборудования",
        group = FilterGroup.stringSearch,
        plsExpression = PlsByDay.equnr.castToString(),
        rateExpression = PlsByDayWithRate.equnr.castToString(),
        repairExpression = RepairStructures.headEqunr.castToString()
    ),
    gbo(
        columnTitle = "ГБО",
        plsExpression = PlsByDay.gboText,
        rateExpression = PlsByDayWithRate.gboText,
        repairExpression = RepairStructures.headGboText
    ),
    compartAmount(
        columnTitle = "Паллетовместимость",
        group = FilterGroup.number,
        type = ColumnType.NUMBER,
        plsExpression = PlsByDay.noCompart,
        rateExpression = PlsByDayWithRate.noCompart,
        repairExpression = RepairStructures.headNoCompart
    ),
    trailerLicenseNum(
        columnTitle = "Гос. номер прицепа",
        group = FilterGroup.stringSearch,
        plsExpression = PlsByDay.trailerLicenseNum,
        rateExpression = PlsByDayWithRate.trailerLicenseNum,
        repairExpression = RepairStructures.trailerLicenseNum
    ),
    ter(
        columnTitle = "Территория",
        filterable = false,
        plsExpression = OrganizationalUnitsTimelineTable.territoryName,
        rateExpression = OrganizationalUnitsTimelineTable.territoryName,
        repairExpression = OrganizationalUnitsTimelineTable.territoryName,
    ),
    mr(
        columnTitle = "Макрорегион",
        filterable = false,
        plsExpression = OrganizationalUnitsTimelineTable.mrName,
        rateExpression = OrganizationalUnitsTimelineTable.mrName,
        repairExpression = OrganizationalUnitsTimelineTable.mrName,
    ),
    atp(
        columnTitle = "АТП",
        filterable = false,
        plsExpression = OrganizationalUnitsTimelineTable.atpName,
        rateExpression = OrganizationalUnitsTimelineTable.atpName,
        repairExpression = OrganizationalUnitsTimelineTable.atpName,
    ),
    mvz(
        columnTitle = "МВЗ",
        filterable = false,
        group = FilterGroup.stringSearch,
        plsExpression = PlsByDay.mvz,
        rateExpression = PlsByDayWithRate.mvz,
        repairExpression = RepairStructures.vehicleMvz
    ),
    mvzName(
        columnTitle = "Название МВЗ",
        filterable = false,
        group = FilterGroup.stringSearch,
        plsExpression = PlsByDay.mvzName,
        rateExpression = PlsByDayWithRate.mvzName,
        repairExpression = RepairStructures.vehicleMvzName
    ),
    atpType(
        columnTitle = "Вид деятельности АТП",
        filterable = false,
        plsExpression = OrganizationalUnitsTimelineTable.atpType,
        rateExpression = OrganizationalUnitsTimelineTable.atpType,
        repairExpression = OrganizationalUnitsTimelineTable.atpType,
    ),
    mvzType(
        columnTitle = "Тип МВЗ",
        filterable = false,
        plsExpression = OrganizationalUnitsTimelineTable.mvzType,
        rateExpression = OrganizationalUnitsTimelineTable.mvzType,
        repairExpression = OrganizationalUnitsTimelineTable.mvzType,
    ),
    structureName(
        columnTitle = "Структура затрат",
        rateExpression = PlsByDayWithRate.structureName,
        repairExpression = RepairStructures.structureName,
    ),
    repairPlace(
        columnTitle = "Место ремонта",
        exposedExpression = RepairStructures.repairPlace,
    ),
    reqType(
        columnTitle = "Вид ремонта",
        repairExpression = ToroWorksTypesTable.name,
        rateExpression = PlsByDayWithRate.toroTypeName
    ),
    reqSubtype(
        columnTitle = "Подвид ремонта",
        repairExpression = ToroWorksSubtypesTable.name,
        rateExpression = PlsByDayWithRate.toroSubtypeName
    ),
    vrt(
        columnTitle = "Код ВРТ",
        repairExpression = RepairStructures.vrt,
        rateExpression = PlsByDayWithRate.vrt
    ),
    vrtName(
        columnTitle = "ВРТ",
        repairExpression = RepairStructures.vrtName,
        rateExpression = PlsByDayWithRate.vrtName,
    ),
    eventId(
        columnTitle = "Код события",
        exposedExpression = RepairStructures.eventId,
    ),
    eventText(
        columnTitle = "Событие",
        exposedExpression = RepairStructures.eventText,
    ),
    mileage(
        columnTitle = "Пробег за период, км",
        group = FilterGroup.number,
        aggregated = true,
        type = ColumnType.NUMBER
    ),
    repairExpensesFullPlan(
        columnTitle = "План. затраты, руб",
        group = FilterGroup.number,
        aggregated = true,
        type = ColumnType.NUMBER
    ),
    repairExpensesFull(
        columnTitle = "Затраты, руб",
        group = FilterGroup.number,
        aggregated = true,
        type = ColumnType.NUMBER
    ),
    repairExpensesFullDeviation(
        columnTitle = "Отклонение затрат от плана, руб",
        group = FilterGroup.number,
        aggregated = true,
        type = ColumnType.NUMBER
    ),
    repairRubPlan(
        columnTitle = "Тариф, руб/км",
        group = FilterGroup.number,
        aggregated = true,
        type = ColumnType.NUMBER
    ),
    repairRub(
        columnTitle = "Факт, руб/км",
        group = FilterGroup.number,
        aggregated = true,
        type = ColumnType.NUMBER
    ),
    repairRubDeviation(
        columnTitle = "Отклонение факта от тарифа, руб/км",
        group = FilterGroup.number,
        aggregated = true,
        type = ColumnType.NUMBER
    );

    fun getAlias(): ExpressionAlias<*>? {
        return this.exposedExpression?.alias(this.name)
    }

    fun getPlAlias(): ExpressionAlias<*>? {
        return this.plsExpression?.alias("pls_${this.name}")
    }

    fun getRateAlias(): ExpressionAlias<*>? {
        return this.rateExpression?.alias("rates_${this.name}")
    }

    fun getRepairAlias(): ExpressionAlias<*>? {
        return this.repairExpression?.alias("repair_${this.name}")
    }
}