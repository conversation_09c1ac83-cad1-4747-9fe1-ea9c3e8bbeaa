package com.x5.logistics.rest.controller.vehicle

import com.x5.logistics.repository.TsExposedRepo
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.PagedDetailedResponse
import com.x5.logistics.rest.dto.ktg.KtgDetailsQtyReq
import com.x5.logistics.rest.dto.vehicle.details.TsMassFilterReq
import com.x5.logistics.rest.dto.vehicle.details.TsQtyReq
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailedFilterRest
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailedFiltersFront
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailsMassFilterReq
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailsQtyReq
import com.x5.logistics.rest.dto.vehicle.details.VehicleDetailsQtyResponse
import com.x5.logistics.service.VehicleUsageService
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
@RequestMapping("/api/vehicle/details")
class VehicleDetailsQtyController(
    val service: VehicleUsageService,
    val repo: TsExposedRepo
) {
    private val log = getLogger()

    @Tag(name = "Траспортные средства")
    @Operation(summary = "Запрос на детальный отчет по количеству транспортных средств для заданного периода дат и МВЗ.")
    @SwaggerReqBody(
        description = "Запрос на детальный отчет по Количеству ТС.",
        content = [Content(schema = Schema(implementation = VehicleDetailsQtyReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = PagedDetailedResponse::class))]
            )
        ]
    )
    @PostMapping
    fun getDetailedReport(@RequestBody req: TsQtyReq) : VehicleDetailsQtyResponse {
        log.debug("Vehicle details qty. [req={}]", req)
        return repo.getDetailedReport(req)
    }

    @Tag(name = "Траспортные средства")
    @Operation(summary = "Получение названия столбцов на детальный отчет по количеству транспортных средств для заданного периода дат и МВЗ.")
    @GetMapping
    fun getDetailedReportColumns(): List<VehicleDetailedFilterRest> {
        return VehicleDetailedFiltersFront.entries.map {
            VehicleDetailedFilterRest(
                label = it.name,
                name = it.label,
                group = it.group.name,
                filterable = it.filterable
            )
        }
    }

    @Tag(name = "Траспортные средства")
    @Operation(summary = "Запрос на получение доступных значений фильтра с учетом уже выбранных фильтров.")
    @SwaggerReqBody(
        description = "Запрос на получение доступных значений фильтров.",
        content = [Content(schema = Schema(implementation = KtgDetailsQtyReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = LabeledValue::class))]
            )
        ]
    )
    @PostMapping("/filter")
    fun getFilterValues(@RequestBody req: TsMassFilterReq): List<LabeledValue> {
        log.debug("Vehicle filter [req={}]", req)
        return repo.getFilterValues(req)
    }

    @Tag(name = "Траспортные средства")
    @Operation(summary = "Запрос проверки данных для поиска по списку значений.")
    @SwaggerReqBody(
        description = "Запрос на получение доступных значений фильтров.",
        content = [Content(schema = Schema(implementation = VehicleDetailsMassFilterReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = MassFilterResp::class))]
            )
        ]
    )
    @PostMapping("/massfilter")
    fun getMassFilter(@RequestBody req: TsMassFilterReq): MassFilterResp {
        log.debug("Mass filter [req={}]", req)
        return repo.checkMassFilter(req)
    }
}
