package com.x5.logistics.rest.controller

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.kipfactor.GetKipFactorReportFilterValuesReq
import com.x5.logistics.rest.dto.kipfactor.GetKipFactorReportReq
import com.x5.logistics.rest.dto.kipfactor.GetKipFactorReportRes
import com.x5.logistics.rest.dto.kipfactor.KipFactorReportColumn
import com.x5.logistics.rest.dto.kipfactor.KipFactorReportColumnItem
import com.x5.logistics.service.KipFactorReportService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.checkFilterValues
import com.x5.logistics.util.moscowDateTime
import com.x5.logistics.service.settingssheet.ReportName
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URLEncoder

@RestController
@RequestMapping("api/report/kip-factor")
class KipFactorReportController(
    private val service: KipFactorReportService
) {
    @Tag(name = "Отчёт факторный анализ КИП и КТГ")
    @Operation(summary = "Получение списка колонок отчёта факторный анализ КИП и КТГ.")
    @GetMapping
    fun getKipFactorReportColumns(): List<KipFactorReportColumnItem> =
        service.getKipFactorReportColumns()

    @Tag(name = "Отчёт факторный анализ КИП и КТГ")
    @Operation(summary = "Получение значений для текстовых фильтров отчёта факторный анализ КИП и КТГ.")
    @PostMapping("filters")
    fun getKipFactorReportFilterValues(@RequestBody req: GetKipFactorReportFilterValuesReq): List<LabeledValue> =
        service.getKipFactorReportFilterValues(req)

    @Tag(name = "Отчёт факторный анализ КИП и КТГ")
    @Operation(summary = "Массовая проверка значений для текстовых фильтров отчёта факторный анализ КИП и КТГ.")
    @PostMapping("massfilter")
    fun getKipFactorReportMassFilterValues(@RequestBody req: GetKipFactorReportFilterValuesReq): MassFilterResp =
        checkFilterValues(
            valuesForCheck = req.request.value,
            actualValues = service.getKipFactorReportFilterValues(req)
        )

    @Tag(name = "Отчёт факторный анализ КИП и КТГ")
    @Operation(summary = "Получение отчёта факторный анализ КИП и КТГ.")
    @PostMapping
    @ExposedTransactional
    fun getKipFactorReport(
        @RequestBody req: GetKipFactorReportReq
    ): GetKipFactorReportRes {
        val totalItem = service.getKipFactorReportItems(req.copy(
            pageSize = 1,
            pageNumber = 0,
            granularity = null,
            columns = listOf(KipFactorReportColumn.VEHICLE_COUNT, KipFactorReportColumn.KTG_SHARE, KipFactorReportColumn.KIP_SHARE),
            sort = emptyList()
        )).firstOrNull()
        val items = if (req.columns.isEmpty()) emptyList() else service.getKipFactorReportItems(req)
        return GetKipFactorReportRes(
            count = service.getKipFactorReportCount(req),
            pageSize = req.pageSize,
            pageNumber = req.pageNumber,
            items = items,
            totalVehicleCount = totalItem?.vehicleCount ?: 0.0,
            totalKtgShare = totalItem?.ktgShare ?: 0.0,
            totalKipShare = totalItem?.kipShare ?: 0.0,
            pageTotal = items.size
        )
    }

    @Tag(name = "Отчёт факторный анализ КИП и КТГ")
    @Operation(summary = "Выгрузка отчёта факторный анализ КИП и КТГ.")
    @PostMapping("export")
    fun exportKipFactorReport(
        @RequestBody req: GetKipFactorReportReq,
        token: JwtToken?
    ): ResponseEntity<Resource> {
        val fileName = "${ReportName.KIP_FACTOR.title} ${moscowDateTime()}.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(service.exportKipFactorReportItems(req, token.username)))
    }

}
