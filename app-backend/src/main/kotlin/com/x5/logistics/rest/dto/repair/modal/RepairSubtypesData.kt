package com.x5.logistics.rest.dto.repair.modal

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.math.BigInteger

data class RepairSubtypesData(

    //AC-JRAAVTO-39-12
    @Schema(description = "ВРТ")
    val subtype: String,

    @Schema(description = "Суммарная стоимость ремонтов на собственных ремзонах, тыс. руб.")
    val ownWorkshopCost: BigDecimal,

    @Schema(description = "Количество ремонтов на собственных ремзонах")
    val ownWorkshopCount: BigInteger,

    @Schema(description = "Суммарная стоимость ремонтов на сторонних СТО, тыс. руб.")
    val serviceStationCost: BigDecimal,

    @Schema(description = "Количество ремонтов на сторонних СТО")
    val serviceStationCount: BigInteger
)
