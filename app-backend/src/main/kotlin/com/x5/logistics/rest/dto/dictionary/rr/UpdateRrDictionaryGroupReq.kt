package com.x5.logistics.rest.dto.dictionary.rr

import com.x5.logistics.data.dictionary.rr.Month
import java.math.BigDecimal

data class UpdateRrDictionaryGroupReq(
    val year: Int,
    val atpId: Long,
    val tonnage: BigDecimal,
    val details: List<UpdateRrDictionaryGroupReqDetailsDto>
)

data class UpdateRrDictionaryGroupReqDetailsDto(
    val month: Month,
    val vrtId: String,
    val rate: Double
)
