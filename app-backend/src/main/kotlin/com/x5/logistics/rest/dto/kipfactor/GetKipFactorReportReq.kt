package com.x5.logistics.rest.dto.kipfactor

import com.fasterxml.jackson.annotation.JsonProperty
import com.x5.logistics.repository.Granularity
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.util.FilterGroup
import java.time.LocalDate

data class GetKipFactorReportReq(
    val pageSize: Int,
    val pageNumber: Int,
    val from: LocalDate,
    val to: LocalDate,
    val granularity: Granularity? = null,
    val columns: List<KipFactorReportColumn> = emptyList(),
    val filters: List<KipFactorReportColumnFilter> = emptyList(),
    val sort: List<KipFactorReportColumnSortOrder> = emptyList(),
    val geoFilter: GeoFilter
)



enum class KipFactorReportColumn(val label: String, val group: FilterGroup, val filterable: Boolean) {
    @JsonProperty("mr") MR("Макрорегион", FilterGroup.string, false),
    @JsonProperty("atp") ATP("АТП", FilterGroup.string, false),
    @JsonProperty("mvz") MVZ("МВЗ", FilterGroup.string, false),
    @JsonProperty("mvzName") MVZ_NAME("Название МВЗ", FilterGroup.string, false),
    @JsonProperty("retailNetwork") RETAIL_NETWORK("Торговая сеть АТП", FilterGroup.string, false),
    @JsonProperty("atpType") ATP_TYPE("Вид деятельности АТП", FilterGroup.string, false),
    @JsonProperty("mvzType") MVZ_TYPE("Тип МВЗ", FilterGroup.string, false),
    @JsonProperty("vehicleId") VEHICLE_ID("Единица оборудования", FilterGroup.string, true),
    @JsonProperty("vehicleLicense") VEHICLE_LICENSE("Гос. номер ТС", FilterGroup.string, true),
    @JsonProperty("vehicleVin") VEHICLE_VIN("VIN номер", FilterGroup.string, true),
    @JsonProperty("vehicleGroup") VEHICLE_GROUP("Вид ТС", FilterGroup.string, true),
    @JsonProperty("vehicleType") VEHICLE_TYPE("Тип ТС", FilterGroup.string, true),
    @JsonProperty("vehicleBrand") VEHICLE_BRAND("Марка", FilterGroup.string, true),
    @JsonProperty("vehicleModel") VEHICLE_MODEL("Модель", FilterGroup.string, true),
    @JsonProperty("vehicleTonnage") VEHICLE_TONNAGE("Тоннаж", FilterGroup.number, true),
    @JsonProperty("vehicleCreateYear") VEHICLE_CREATE_YEAR("Год ввода в эксплуатацию", FilterGroup.number, true),
    @JsonProperty("vehicleCount") VEHICLE_COUNT("Ср. кол-во ТС, шт.", FilterGroup.number, true),
    @JsonProperty("ktgShare") KTG_SHARE("КТГ, %", FilterGroup.number, true),
    @JsonProperty("rgShare") RG_SHARE("РГ, %", FilterGroup.number, true),
    @JsonProperty("kipShare") KIP_SHARE("КИП, %", FilterGroup.number, true),
    @JsonProperty("kipPlan") KIP_PLAN("КИП план, мч", FilterGroup.number, true),
    @JsonProperty("ktgHours") KTG_HOURS("КТГ, мч", FilterGroup.number, true),
    @JsonProperty("rgHours") RG_HOURS("РГ, мч", FilterGroup.number, true),
    @JsonProperty("kipHours") KIP_HOURS("КИП факт, мч", FilterGroup.number, true),
    @JsonProperty("factor") FACTOR("Факторы", FilterGroup.string, true),
    @JsonProperty("terName") TERRITORY_NAME("Территория", FilterGroup.stringSearch, false)
}

class KipFactorReportColumnFilter(
    val name: KipFactorReportColumn,
    val condition: FilterCondition,
    val value: List<Any>
)

class KipFactorReportColumnSortOrder (
    val column: KipFactorReportColumn,
    val asc: Boolean = true
)
