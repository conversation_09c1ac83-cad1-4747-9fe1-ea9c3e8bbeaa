package com.x5.logistics.rest.dto.hr.dictionaries

import com.fasterxml.jackson.annotation.JsonFormat
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Названия итоговых подразделений")
data class HrTotalPlaceDto(
    @Schema(description = "Уникальный идентификатор в БД")
    val id: Int?,

    @Schema(description = "Название итогового подразделения")
    val name: String?,

    @Schema(description = "Сотрудник")
    val updatedBy: String?,

    @Schema(description = "Дата последнего редактирования записи")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val updatedAt: LocalDate?
)
