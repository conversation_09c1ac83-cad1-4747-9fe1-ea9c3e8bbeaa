package com.x5.logistics.rest.dto.vehicle.qty

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос на количество транспортных средств.")
data class VehicleQtyReq(
    @Schema(description = "Дата начала периода.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val from: LocalDate,

    @Schema(description = "Дата конца периода.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val to: LocalDate,

    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,
)
