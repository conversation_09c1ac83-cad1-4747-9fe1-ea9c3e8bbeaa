package com.x5.logistics.rest.dto.repair.parts

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.repository.Granularity
import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

data class RepairsPartsReq(
    @Schema(description = "Номер страницы.")
    val pageNumber: Int,

    @Schema(description = "Размер страницы.")
    val pageSize: Int,

    @Schema(description = "Дата начала периода.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val from: LocalDate,

    @Schema(description = "Дата конца периода.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val to: LocalDate,

    @Schema(description = "Гранулярность.")
    val granularity: Granularity?,

    @Schema(description = "Глобальный фильтр.")
    val geoFilter: <PERSON><PERSON><PERSON>ilt<PERSON>,

    @Schema(description = "Список колонок.")
    val columns: List<RepairPartsColumn>,

    @Schema(description = "Список сортировок.")
    val sort: List<RepairPartsSortOrder>,

    @Schema(description = "Список фильтров.")
    val filters: List<RepairPartsFilter>,
)
