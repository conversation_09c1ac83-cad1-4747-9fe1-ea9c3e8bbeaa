package com.x5.logistics.rest.controller.repair

import com.x5.logistics.rest.dto.repair.workspace.ToroWidgetReq
import com.x5.logistics.rest.dto.repair.workspace.ToroWidgetResp
import com.x5.logistics.service.ToroWidgetService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
class ToroWidgetController(
    val service: ToroWidgetService
) {
    @Tag(name = "Ремонт")
    @Operation(summary = "Запрос данных по ремонтам - виджет рабочего стола топ заказов и исполнителей.")
    @SwaggerReqBody(
        description = "Запрос данных по ремонтам - виджет рабочего стола топ заказов и исполнителей.",
        content = [Content(schema = Schema(implementation = ToroWidgetReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = ToroWidgetResp::class))]
            )
        ]
    )
    @PostMapping("api/repair/top")
    fun getDetails(@RequestBody req: ToroWidgetReq): ToroWidgetResp {
        return service.getWidget(req)
    }
}
