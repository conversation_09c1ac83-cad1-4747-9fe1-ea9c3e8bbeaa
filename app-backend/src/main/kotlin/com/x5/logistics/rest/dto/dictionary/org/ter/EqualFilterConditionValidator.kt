package com.x5.logistics.rest.dto.dictionary.org.ter

import com.x5.logistics.rest.dto.FilterCondition
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext

class EqualFilterConditionValidator : ConstraintValidator<EqualFilterCondition, List<OrgTerDictionaryColumnFilter>> {
    override fun isValid(value: List<OrgTerDictionaryColumnFilter>?, context: ConstraintValidatorContext): Boolean {
        return value?.all { it.condition == FilterCondition.equal } ?: true
    }
}
