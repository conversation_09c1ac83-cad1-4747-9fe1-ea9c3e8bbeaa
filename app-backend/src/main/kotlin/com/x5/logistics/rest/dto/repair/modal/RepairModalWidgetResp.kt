package com.x5.logistics.rest.dto.repair.modal

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Ответ модального виджета Затраты на ремонт.")
data class RepairModalWidgetResp(
    @Schema(description = "Итоговые значения всем ТС")
    val totalRepairInfo: RepairItem,
    @Schema(description = "Итоговые значения по Основным ТС")
    val totalRepairInfoGroupMain: RepairItem,
    @Schema(description = "Итоговые значения по Вспомогательным ТС")
    val totalRepairInfoGroupSub: RepairItem,
    @Schema(description = "руб/км отдельно Основное")
    val vehicleGroupMain: List<VehicleRepairItem>,
    @Schema(description = "руб/км отдельно Вспомогательное")
    val vehicleGroupSub: List<VehicleRepairItem>,
    @Schema(description = "руб/км по запрошенной группировке")
    val typeRepair: List<TypeRepairItem>,
)

data class RepairItem(
    val repairSum: Double,
    val repairRubKmFact: Double,
    val repairRubKmPlan: Double,
    val countExpensiveSum: Double,
    val countVehicleRepair: Double,
)

data class VehicleRepairItem(
    val brand: String,
    val tonnage: Float,
    val repairSum: Double,
    val repairRubKmFact: Double,
    val repairRubKmPlan: Double,
    val countExpensiveSum: Double,
    val countVehicleRepair: Double,
)
