package com.x5.logistics.rest.dto.vehicle.mileage

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос модального виджета по пробегу автопарка.")
data class VehicleMileageWidgetReq(
    @Schema(description = "Дата.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val day: LocalDate,

    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,
)