package com.x5.logistics.rest.dto.dictionary.rr.structure

import com.fasterxml.jackson.annotation.JsonIgnore
import com.x5.logistics.data.dictionary.rr.Month
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Schema(description = "Справочник \"Структура ремонтов (доля)\"")
data class RrStructureDictionaryResp(
    @Schema(description = "Общее количество записей")
    val count: Int,
    @Schema(description = "Размер страницы")
    val pageSize: Int,
    @Schema(description = "Номер страницы")
    val pageNumber: Int = 0,
    @Schema(description = "Значения")
    val items: List<RrStructureDictionaryItem>,
    @Schema(description = "Всего страниц")
    val pageTotal: Int = (count + pageSize - 1) / pageSize,
    @Schema(description = "Год")
    val year: Int
)

data class RrStructureDictionaryItem(
    val atpId: Long,
    val atpName: String,
    @JsonIgnore
    val year: Int?,
    val updatedBy: String?,
    val details: List<RrStructureDictionaryItemDetails>
)

data class RrStructureDictionaryItemDetails(
    val name: String,
    val label: String,
    val updatedBy: String?,
    val values: List<RrStructureDictionaryItemValue>
)

data class RrStructureDictionaryItemValue(
    val month: Month,
    val value: Double?
)

data class RrStructureDictionaryRow(
    val atpId: Long,
    val structureType: String,
    @JsonIgnore
    val year: Int?,
    val month: String,
    val name: String,
    val updatedBy: String?,
    @JsonIgnore
    val updatedAt: LocalDateTime?,
    val rate: Double?
)