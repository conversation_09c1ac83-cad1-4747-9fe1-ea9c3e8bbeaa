package com.x5.logistics.rest.controller.dictionary.org

import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryCreateLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryCreateRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryDeleteLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryFilters
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryItemDto
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryListRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryPagedResponse
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionarySelects
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryUpdateLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.mr.OrgMrDictionaryUpdateRequest
import com.x5.logistics.service.dictionary.org.OrgMrDictionaryService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.dateFormatter
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URLEncoder
import java.time.LocalDateTime
import java.time.ZoneId

@RestController
@RequestMapping("/api/dictionary/org")
class OrgMrDictionaryController(
    private val orgMrDictionaryService: OrgMrDictionaryService
) {

    @Tag(name = "Словари")
    @Operation(summary = "Получение содержимого справочника МР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на получение содержимого справочника МР",
        content = [Content(schema = Schema(implementation = OrgMrDictionaryListRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgMrDictionaryPagedResponse::class))]
            )]
    )
    @PostMapping("/mr")
    suspend fun getList(@Valid @RequestBody req: OrgMrDictionaryListRequest): OrgMrDictionaryPagedResponse {
        return orgMrDictionaryService.getList(req)
    }

    @Tag(name = "Словари")
    @Operation(summary = "Экспорт справочника МР-ТЕР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на экспорт справочника МР-ТЕР",
        content = [Content(schema = Schema(implementation = OrgMrDictionaryListRequest::class))]
    )
    @ApiResponses(
        value = [ApiResponse(
            responseCode = "200",
            description = "Успешный ответ.",
            content = [Content(
                mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                schema = Schema(type = "string", format = "binary")
            )]
        )]
    )
    @PostMapping("/mr/export")
    suspend fun export(@RequestBody req: OrgMrDictionaryListRequest): ResponseEntity<Resource> {
        val fileName = "Справочник МР ${dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))}.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${
                    withContext(Dispatchers.IO) {
                        URLEncoder.encode(fileName, "UTF-8")
                    }.replace("+", "%20")
                }"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(orgMrDictionaryService.export(req)))
    }

    @Tag(name = "Словари")
    @Operation(summary = "Создание нового МР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на создание нового МР",
        content = [Content(schema = Schema(implementation = OrgMrDictionaryCreateRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgMrDictionaryItemDto::class))]
            )]
    )
    @PostMapping("/mr-new")
    suspend fun createMr(token: JwtToken?, @Valid @RequestBody req: OrgMrDictionaryCreateRequest): OrgMrDictionaryItemDto {
        return newSuspendedTransaction { orgMrDictionaryService.createMr(req, token.username) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Редактирование МР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на редактирование МР",
        content = [Content(schema = Schema(implementation = OrgMrDictionaryUpdateRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgMrDictionaryItemDto::class))]
            )]
    )
    @PutMapping("/mr")
    suspend fun updateMr(token: JwtToken?, @Valid @RequestBody req: OrgMrDictionaryUpdateRequest): OrgMrDictionaryItemDto {
        return newSuspendedTransaction { orgMrDictionaryService.updateMr(req, token.username) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Создание новой связи МР-ТЕР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на создание новой связи МР-ТЕР",
        content = [Content(schema = Schema(implementation = OrgMrDictionaryCreateRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgMrDictionaryItemDto::class))]
            )]
    )
    @PostMapping("/mr-log")
    suspend fun createMrLogEntry(token: JwtToken?, @Valid @RequestBody req: OrgMrDictionaryCreateLogEntryRequest): OrgMrDictionaryItemDto {
        return newSuspendedTransaction { orgMrDictionaryService.createLogEntry(req, token.username) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Редактирование связи МР-ТЕР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на редактирование связи МР-ТЕР",
        content = [Content(schema = Schema(implementation = OrgMrDictionaryUpdateLogEntryRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgMrDictionaryItemDto::class))]
            )]
    )
    @PutMapping("/mr-log")
    suspend fun updateMrLogEntry(token: JwtToken?, @Valid @RequestBody req: OrgMrDictionaryUpdateLogEntryRequest): OrgMrDictionaryItemDto {
        return newSuspendedTransaction { orgMrDictionaryService.updateLogEntry(req, token.username) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Удаление связи МР-ТЕР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на удаление связи МР-ТЕР",
        content = [Content(schema = Schema(implementation = OrgMrDictionaryDeleteLogEntryRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgMrDictionaryItemDto::class))]
            )]
    )
    @DeleteMapping("/mr-log")
    suspend fun deleteLogEntry(token: JwtToken?, @RequestBody req: OrgMrDictionaryDeleteLogEntryRequest): OrgMrDictionaryItemDto {
        return newSuspendedTransaction { orgMrDictionaryService.deleteLogEntry(req, token.username) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Словари фильтров МР-ТЕР")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgMrDictionaryFilters::class))]
            )]
    )
    @GetMapping("/mr-filters")
    suspend fun getFilters(): OrgMrDictionaryFilters {
        return orgMrDictionaryService.getFilters()
    }

    @Tag(name = "Словари")
    @Operation(summary = "Словари селектов МР-ТЕР")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgMrDictionarySelects::class))]
            )]
    )
    @GetMapping("/mr")
    suspend fun getSelects(): OrgMrDictionarySelects {
        return orgMrDictionaryService.getSelects()
    }
}