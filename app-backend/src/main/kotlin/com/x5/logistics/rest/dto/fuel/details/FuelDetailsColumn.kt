package com.x5.logistics.rest.dto.fuel.details

import com.fasterxml.jackson.annotation.JsonProperty
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.fuel.PlFuelStatsTable
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.repository.castToDoubleDep
import com.x5.logistics.repository.castToFloat
import com.x5.logistics.repository.castToString
import com.x5.logistics.repository.extractYear
import com.x5.logistics.repository.safeDiv
import com.x5.logistics.repository.toNullable
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.GreaterOp
import org.jetbrains.exposed.sql.SqlExpressionBuilder.minus
import org.jetbrains.exposed.sql.SqlExpressionBuilder.times
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.sum

@Suppress("EnumEntryName")
enum class FuelDetailsColumn(
    val expression: Expression<*>,
    val grouping: Boolean,
    val columnTitle: String,
    val type: ColumnType = ColumnType.NUMBER
) {
    // ПЛ:
    numberWaybillStart(
        expression = PlFuelStatsTable.plStartDate,
        grouping = true,
        columnTitle = "Дата и время открытия ПЛ",
        type = ColumnType.INSTANT
    ),
    typeWB(
        expression = PlFuelStatsTable.typeWB,
        grouping = true,
        columnTitle = "Тип ПЛ",
        type = ColumnType.STRING
    ),
    maintenance(
        expression = PlFuelStatsTable.maintenance,
        grouping = true,
        columnTitle = "Ремонтный ПЛ",
        type = ColumnType.BOOLEAN
    ),
    numberWaybillEnd(
        expression = PlFuelStatsTable.plEndDate,
        grouping = true,
        columnTitle = "Дата и время закрытия ПЛ",
        type = ColumnType.INSTANT
    ),
    waybillNumber(
        expression = PlFuelStatsTable.qmnum,
        grouping = true,
        columnTitle = "Номер ПЛ",
        type = ColumnType.STRING
    ),
    runWaybill(
        expression = PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пробег по ПЛ"
    ),
    probegWithGas(
        expression = Sum(
            expr = Case().When(
                cond = (GreaterOp(PlFuelStatsTable.rashodGasFact, doubleLiteral(0.0))),
                result = PlFuelStatsTable.probeg.castToDoubleDep()
            ).Else(doubleLiteral(0.0)),
            columnType = DoubleColumnType()
        ),
        grouping = false,
        columnTitle = "Пробег с газом"
    ),
    mountainMileage(
        expression = PlFuelStatsTable.probegGorny.sum(),
        grouping = false,
        columnTitle = "Пробег горный"
    ),
    engineHoursFridgeHead(
        expression = PlFuelStatsTable.motoHoursHead.sum(),
        grouping = false,
        columnTitle = "Моточасы ХОУ (г), ч."
    ),
    engineHoursAcTrailer(
        expression = PlFuelStatsTable.motoHoursTrail.sum(),
        grouping = false,
        columnTitle = "Моточасы ХОУ (п), ч."
    ),
    normaDieselOn100Km(
        expression = (PlFuelStatsTable.k1P * PlFuelStatsTable.probeg).sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Норма ДТ/ 100 км (К1_П), л."
    ),
    normaFridgeHeadDieselPerHour(
        expression = (PlFuelStatsTable.normaHouHead * PlFuelStatsTable.motoHoursHead).sum() safeDiv PlFuelStatsTable.motoHoursHead.sum(),
        grouping = false,
        columnTitle = "Норма ХОУ (г) ДТ/ час, л."
    ),
    normaFridgeTrailerDieselPerHour(
        expression = (PlFuelStatsTable.normaHouTrail * PlFuelStatsTable.motoHoursTrail).sum() safeDiv PlFuelStatsTable.motoHoursTrail.sum(),
        grouping = false,
        columnTitle = "Норма ХОУ (п) ДТ / час, л."
    ),
    normaGasOn100Km(
        expression = (PlFuelStatsTable.normaGas * PlFuelStatsTable.probeg).sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Норма Газ/ 100 км, л."
    ),
    winterCoefficient(
        expression = (PlFuelStatsTable.coefZimny * PlFuelStatsTable.probeg).sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Зимний коэф. (К1)"
    ),
    mountainCoefficient(
        expression = (PlFuelStatsTable.coefGorny * PlFuelStatsTable.probegGorny.castToFloat()).sum() safeDiv PlFuelStatsTable.probegGorny.sum().castToFloat(),
        grouping = false,
        columnTitle = "Горный коэф. (S)"
    ),
    coefficientReductionDiesel(
        expression = (PlFuelStatsTable.coefReduceFuel * PlFuelStatsTable.rashodFuelPlan).sum() safeDiv PlFuelStatsTable.rashodFuelPlan.sum(),
        grouping = false,
        columnTitle = "Коэф. снижения ДТ (К8)"
    ),
    costDieselOnLiter(
        expression = (PlFuelStatsTable.fuelPrice * PlFuelStatsTable.rashodFuelFact).sum() safeDiv PlFuelStatsTable.rashodFuelFact.sum(),
        grouping = false,
        columnTitle = "Стоимость ДТ / л, руб."
    ),
    costGasOnLiter(
        expression = (PlFuelStatsTable.gasPrice * PlFuelStatsTable.rashodGasFact).sum() safeDiv PlFuelStatsTable.rashodGasFact.sum(),
        grouping = false,
        columnTitle = "Стоимость Газ / л, руб."
    ),

    // TC:
    terName(
        expression = OrganizationalUnitsTimelineTable.territoryName,
        grouping = true,
        columnTitle = "Территория",
        type = ColumnType.STRING
    ),
    macroRegion(
        expression = OrganizationalUnitsTimelineTable.mrName,
        grouping = true,
        columnTitle = "Макрорегион",
        type = ColumnType.STRING
    ),
    atp(
        expression = OrganizationalUnitsTimelineTable.atpName,
        grouping = true,
        columnTitle = "АТП",
        type = ColumnType.STRING
    ),
    mvz(
        expression = OrganizationalUnitsTimelineTable.mvzId,
        grouping = true,
        columnTitle = "Номер МВЗ",
        type = ColumnType.STRING
    ),
    mvzName(
        expression = OrganizationalUnitsTimelineTable.mvzName,
        grouping = true,
        columnTitle = "Наименование МВЗ",
        type = ColumnType.STRING
    ),
    vehicleType(
        expression = PlFuelStatsTable.tsType,
        grouping = true,
        columnTitle = "Тип ТС",
        type = ColumnType.STRING
    ),
    vehicleGroup(
        expression = PlFuelStatsTable.tsGroup,
        grouping = true,
        columnTitle = "Вид ТС",
        type = ColumnType.STRING
    ),
    brand(
        expression = PlFuelStatsTable.marka,
        grouping = true,
        columnTitle = "Марка ТС",
        type = ColumnType.STRING
    ),
    model(
        expression = PlFuelStatsTable.model,
        grouping = true,
        columnTitle = "Модель ТС",
        type = ColumnType.STRING
    ),
    tonnage(
        expression = PlFuelStatsTable.loadWgt,
        grouping = true,
        columnTitle = "Тоннаж ТС"
    ),
    fuelTypeFirst(
        expression = PlFuelStatsTable.fuelTypeFirst,
        grouping = true,
        columnTitle = "Первичное топливо ТС"
    ),

    @JsonProperty("year")
    modelYear(
        expression = PlFuelStatsTable.modelYear,
        grouping = true,
        columnTitle = "Год выпуска ТС"
    ),
    startDate(
        expression = PlFuelStatsTable.createDate.extractYear(),
        grouping = true,
        columnTitle = "Год начала эксплуатации ТС"
    ),
    tabNum(
        expression = PlFuelStatsTable.driverTabnum,
        grouping = true,
        columnTitle = "Табельный номер водителя",
        type = ColumnType.STRING
    ),
    licenseNum(
        expression = PlFuelStatsTable.licenseNum,
        grouping = true,
        columnTitle = "Гос. номер ТС",
        type = ColumnType.STRING
    ),
    vin(
        expression = PlFuelStatsTable.fleetNum,
        grouping = true,
        columnTitle = "VIN номер ТС",
        type = ColumnType.STRING
    ),
    eqUnit(
        expression = PlFuelStatsTable.equnr.castToString(),
        grouping = true,
        columnTitle = "Единица оборудования ТС",
        type = ColumnType.STRING
    ),
    licenseNumTrailer(
        expression = PlFuelStatsTable.licenseNumPri,
        grouping = true,
        columnTitle = "Гос. номер прицепа",
        type = ColumnType.STRING
    ),
    gbo(
        expression = PlFuelStatsTable.gbo,
        grouping = true,
        columnTitle = "ГБО",
        type = ColumnType.BOOLEAN
    ),
    planConsumptionDieselLiter(
        expression = PlFuelStatsTable.rashodFuelPlan.sum(),
        grouping = false,
        columnTitle = "Пл.расх. ДТ, л."
    ),
    planConsumptionHeadDieselLiter(
        expression = PlFuelStatsTable.rashodFuelPlanHeadTot.sum(),
        grouping = false,
        columnTitle = "Пл.расх (г) ДТ, л."
    ),
    planConsumptionFridgeHeadDieselLiter(
        expression = PlFuelStatsTable.rashodFuelPlanHeadHou.sum(),
        grouping = false,
        columnTitle = "Пл.расх  ХОУ (г) ДТ, л."
    ),
    planConsumptionNoFridgeHeadDieselLiter(
        expression = PlFuelStatsTable.rashodFuelPlanHead.sum(),
        grouping = false,
        columnTitle = "Пл.расх. без ХОУ (г) ДТ, л."
    ),
    planConsumptionFridgeTrailerDieselLiter(
        expression = PlFuelStatsTable.rashodFuelPlanTrailHou.sum(),
        grouping = false,
        columnTitle = "Пл.расх. ХОУ (п) ДТ, л."
    ),
    factConsumptionDieselLiter(
        expression = PlFuelStatsTable.rashodFuelFact.sum(),
        grouping = false,
        columnTitle = "Факт. расх. ДТ, л"
    ),
    factConsumptionHeadDieselLiter(
        expression = PlFuelStatsTable.rashodFuelFactHeadTot.sum(),
        grouping = false,
        columnTitle = "Факт. расх. (г) ДТ, л"
    ),

    factConsumptionFridgeHeadDieselLiter(
        expression = PlFuelStatsTable.rashodFuelFactHeadHou.sum(),
        grouping = false,
        columnTitle = "Факт. расх. ХОУ (г) ДТ, л"
    ),

    factConsumptionNoFridgeHeadDieselLiter(
        expression = PlFuelStatsTable.rashodFuelFactHead.sum(),
        grouping = false,
        columnTitle = "Факт. расх. без ХОУ (г) ДТ, л"
    ),

    factConsumptionFridgeTrailerDieselLiter(
        expression = PlFuelStatsTable.rashodFuelFactTrailHou.sum(),
        grouping = false,
        columnTitle = "Факт. расх. ХОУ (п) ДТ, л"
    ),

    burnoutEconomyDieselLiter(
        expression = PlFuelStatsTable.econFuel.sum(),
        grouping = false,
        columnTitle = "Пережог (+) /Экономия (-) ДТ, л."
    ),

    burnoutEconomyNoFridgeHeadDieselLiter(
        expression = PlFuelStatsTable.econFuelHead.sum(),
        grouping = false,
        columnTitle = "Пережог (+) /Экономия (-) без ХОУ (г) ДТ, л."
    ),

    burnoutEconomyFridgeTrailerDieselLiter(
        expression = PlFuelStatsTable.econFuelTrailHou.sum(),
        grouping = false,
        columnTitle = "Пережог (+) /Экономия (-) ХОУ (п) ДТ, л."
    ),

    planConsumptionOn100DieselLiter(
        expression = (PlFuelStatsTable.rashodFuelPlan.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл.расх./100 км. ДТ, л."
    ),

    planConsumptionOn100NoFridgeHeadDieselLiter(
        expression = (PlFuelStatsTable.rashodFuelPlanHead.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл.расх./ 100 км.без ХОУ (г) ДТ, л."
    ),

    factConsumptionOn100DieselLiter(
        expression = (PlFuelStatsTable.rashodFuelFact.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт.расх./100 км.ДТ, л."
    ),

    factConsumptionOn100NoFridgeHeadDieselLiter(
        expression = (PlFuelStatsTable.rashodFuelFactHead.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт.расх./ 100 км.без ХОУ (г) ДТ, л."
    ),

    planConsumptionDieselRub(
        expression = PlFuelStatsTable.rashodFuelPlanRub.sum(),
        grouping = false,
        columnTitle = "Пл.расх. ДТ, руб."
    ),

    planConsumptionHeadDieselRub(
        expression = PlFuelStatsTable.rashodFuelPlanHeadTotRub.sum(),
        grouping = false,
        columnTitle = "Пл.расх. (г) ДТ, руб."
    ),

    planConsumptionFridgeHeadDieselRub(
        expression = PlFuelStatsTable.rashodFuelPlanHeadHouRub.sum(),
        grouping = false,
        columnTitle = "Пл.расх. ХОУ (г) ДТ, руб."
    ),

    planConsumptionNoFridgeHeadDieselRub(
        expression = PlFuelStatsTable.rashodFuelPlanHeadRub.sum(),
        grouping = false,
        columnTitle = "Пл.расх.без ХОУ (г) ДТ, руб."
    ),

    planConsumptionFridgeTrailerDieselRub(
        expression = PlFuelStatsTable.rashodFuelPlanTrailHouRub.sum(),
        grouping = false,
        columnTitle = "Пл.расх. ХОУ (п) ДТ, руб."
    ),

    factConsumptionDieselRub(
        expression = PlFuelStatsTable.rashodFuelFactRub.sum(),
        grouping = false,
        columnTitle = "Факт.расх. ДТ, руб."
    ),

    factConsumptionHeadDieselRub(
        expression = PlFuelStatsTable.rashodFuelFactHeadTotRub.sum(),
        grouping = false,
        columnTitle = "Факт.расх. (г) ДТ, руб."
    ),

    factConsumptionFridgeHeadDieselRub(
        expression = PlFuelStatsTable.rashodFuelFactHeadHouRub.sum(),
        grouping = false,
        columnTitle = "Факт.расх. ХОУ (г) ДТ, руб."
    ),

    factConsumptionNoFridgeHeadDieselRub(
        expression = PlFuelStatsTable.rashodFuelFactHeadRub.sum(),
        grouping = false,
        columnTitle = "Факт.расх.без ХОУ (г) ДТ, руб."
    ),

    factConsumptionFridgeTrailerDieselRub(
        expression = PlFuelStatsTable.rashodFuelFactTrailHouRub.sum(),
        grouping = false,
        columnTitle = "Факт.расх.. ХОУ (п) ДТ, руб."
    ),

    overspendingEconomyDieselRub(
        expression = PlFuelStatsTable.econFuelRub.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-) ДТ, руб."
    ),

    overspendingEconomyNoFridgeHeadDieselRub(
        expression = PlFuelStatsTable.econFuelHeadRub.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-) без ХОУ (г) ДТ, руб."
    ),

    overspendingEconomyFridgeTrailerDieselRub(
        expression = PlFuelStatsTable.econFuelTrailHouRub.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-) ХОУ (п) ДТ, руб."
    ),

    planConsumptionDieselRubKm(
        expression = (PlFuelStatsTable.rashodFuelPlan * PlFuelStatsTable.fuelPrice).sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл.расх. ДТ, руб/км."
    ),

    factConsumptionDieselRubKm(
        expression = (PlFuelStatsTable.rashodFuelFact * PlFuelStatsTable.fuelPrice).sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт.расх. ДТ, руб/км."
    ),

    overspendingEconomyDieselRubKm(
        expression = PlFuelStatsTable.econFuelRub.sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-) ДТ, руб/км."
    ),

    planConsumptionGasLiter(
        expression = PlFuelStatsTable.rashodGasPlan.sum(),
        grouping = false,
        columnTitle = "Пл.расх. Газ, л."
    ),

    factConsumptionGasLiter(
        expression = PlFuelStatsTable.rashodGasFact.sum(),
        grouping = false,
        columnTitle = "Факт. расход ГАЗ, л"
    ),

    planConsumptionOn100GasLiter(
        expression = (PlFuelStatsTable.rashodGasPlan.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл.расх./100 км. Газ, л."
    ),

    factConsumptionOn100GasLiter(
        expression = (PlFuelStatsTable.rashodGasFact.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт.расх./100 км.Газ, л."
    ),

    burnoutEconomyGasLiter(
        expression = PlFuelStatsTable.econGas.sum(),
        grouping = false,
        columnTitle = "Пережог (+) /Экономия (-) ГАЗ, л."
    ),

    planConsumptionGasRub(
        expression = PlFuelStatsTable.rashodGasPlanRub.sum(),
        grouping = false,
        columnTitle = "Пл.расх. Газ, руб."
    ),

    factConsumptionGasRub(
        expression = PlFuelStatsTable.rashodGasFactRub.sum(),
        grouping = false,
        columnTitle = "Факт.расх. Газ, руб."
    ),

    overspendingEconomyGasRub(
        expression = PlFuelStatsTable.econGasRub.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-) Газ, руб."
    ),

    planConsumptionGasRubKm(
        expression = (PlFuelStatsTable.rashodGasPlan * PlFuelStatsTable.gasPrice).sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл.расх. Газ, руб/км."
    ),

    factConsumptionGasRubKm(
        expression = (PlFuelStatsTable.rashodGasFact * PlFuelStatsTable.gasPrice).sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт.расх. Газ, руб/км."
    ),

    overspendingEconomyGasRubKm(
        expression = (PlFuelStatsTable.econGas * PlFuelStatsTable.gasPrice).sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-) Газ, руб/км."
    ),

    planConsumptionLiter(
        expression = PlFuelStatsTable.rashodTotPlan.sum(),
        grouping = false,
        columnTitle = "Пл.расх., л."
    ),

    factConsumptionLiter(
        expression = PlFuelStatsTable.rashodTotFact.sum(),
        grouping = false,
        columnTitle = "Факт.расх., л."
    ),

    burnoutEconomyLiter(
        expression = PlFuelStatsTable.econTot.sum(),
        grouping = false,
        columnTitle = "Пережог (+) /Экономия (-), л."
    ),

    planConsumptionOn100Liter(
        expression = (PlFuelStatsTable.rashodTotPlan.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл.расх./ 100 км., л."
    ),

    factConsumptionOn100Liter(
        expression = (PlFuelStatsTable.rashodTotFact.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт.расх./ 100 км., л."
    ),

    planConsumptionRub(
        expression = PlFuelStatsTable.rashodTotPlanRub.sum(),
        grouping = false,
        columnTitle = "Пл.расх., руб."
    ),

    factConsumptionRub(
        expression = PlFuelStatsTable.rashodTotFactRub.sum(),
        grouping = false,
        columnTitle = "Факт.расх., руб."
    ),

    overspendingEconomyRub(
        expression = PlFuelStatsTable.econTotRub.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-), руб."
    ),

    planConsumptionRubKm(
        expression = PlFuelStatsTable.rashodTotPlanRub.sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл.расх., руб/км."
    ),

    factConsumptionRubKm(
        expression = PlFuelStatsTable.rashodTotFactRub.sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт.расх., руб/км."
    ),

    overspendingEconomyRubKm(
        expression = PlFuelStatsTable.econTotRub.sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-), руб/км."
    ),

    presumptiveConsumptionDieselOutGbo(
        expression = PlFuelStatsTable.rashodFuelWithoutGboAssumed.sum(),
        grouping = false,
        columnTitle = "Предпол.расход ДТ без ГБО, л"
    ),

    planEffectDieselWithGbo(
        expression = PlFuelStatsTable.effectFuelOfGboPlan.sum(),
        grouping = false,
        columnTitle = "Пл. эффект на ДТ от ГБО, л"
    ),

    factEffectDieselWithGbo(
        expression = PlFuelStatsTable.effectFuelOfGboFact.sum(),
        grouping = false,
        columnTitle = "Факт. эффект на ДТ с ГБО, л"
    ),

    planEffectDieselWithGboOn100(
        expression = (PlFuelStatsTable.effectFuelOfGboPlan.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл. эффект на ДТ от ГБО /100 км, л"
    ),

    factEffectDieselWithGboOn100(
        expression = (PlFuelStatsTable.effectFuelOfGboFact.sum() * 100F) safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт. эффект на ДТ с ГБО /100 км, л"
    ),

    planEffectWithGboRub(
        expression = PlFuelStatsTable.effectTotOfGboPlanRub.sum(),
        grouping = false,
        columnTitle = "Пл. эффект от ГБО, руб."
    ),

    factEffectWithGboRub(
        expression = PlFuelStatsTable.effectTotOfGboFactRub.sum(),
        grouping = false,
        columnTitle = "Факт. эффект от ГБО , руб"
    ),

    overspendingEconomyPlanEffectWithGbo(
        expression = PlFuelStatsTable.econTotOfGboRub.sum(),
        grouping = false,
        columnTitle = "Перерасход (+) /Экономия (-) от план.эффекта ГБО, руб."
    ),

    planEffectWithGboRubKm(
        expression = PlFuelStatsTable.effectTotOfGboPlanRub.sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Пл. эффект от ГБО, руб/км."
    ),

    factEffectWithGboRubKm(
        expression = PlFuelStatsTable.effectTotOfGboFactRub.sum() safeDiv PlFuelStatsTable.probeg.sum(),
        grouping = false,
        columnTitle = "Факт. эффект от ГБО, руб/км."
    ),

    planPercentDeclineDiesel(
        expression = (
                (PlFuelStatsTable.rashodFuelPlanHead.toNullable() - PlFuelStatsTable.rashodFuelWithoutGboAssumed).sum() * 100F
                ) safeDiv PlFuelStatsTable.rashodFuelWithoutGboAssumed.sum(),
        grouping = false,
        columnTitle = "Пл.% сниж.ДТ , %"
    ),

    factPercentDeclineDiesel(
        expression = (
                (PlFuelStatsTable.rashodFuelFactHead.toNullable() - PlFuelStatsTable.rashodFuelWithoutGboAssumed).sum() * 100F
                ) safeDiv PlFuelStatsTable.rashodFuelWithoutGboAssumed.sum(),
        grouping = false,
        columnTitle = "Факт.% сниж.ДТ , %"
    ),

    planCoefficientSubstitutionDieselByGas(
        expression = (PlFuelStatsTable.rashodGasPlan.sum() safeDiv PlFuelStatsTable.effectFuelOfGboPlan.sum()) * -1F,
        grouping = false,
        columnTitle = "Пл. Коэф. замещения ДТ Газом"
    ),

    factCoefficientSubstitutionDieselByGas(
        expression = (PlFuelStatsTable.rashodGasFact.sum() safeDiv PlFuelStatsTable.effectFuelOfGboFact.sum()) * -1F,
        grouping = false,
        columnTitle = "Факт. Коэф. замещения ДТ Газом"
    )
}
