package com.x5.logistics.rest.dto.waybill

import com.x5.logistics.data.Pls
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.repository.ColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.intLiteral

enum class WaybillItemDetailsColumn(
    val sqlColumn: String,
    val exposedExpression: ExpressionAlias<*>,
    val grouping: Boolean,
    val isCalculated: Boolean = false,
    val columnTitle: String,
    val type: ColumnType = ColumnType.STRING
) {
    labels(
        sqlColumn = "",
        exposedExpression = intLiteral(1).alias("labels"),
        grouping = true,
        isCalculated = true,
        columnTitle = "Метка ПЛ"
    ),
    number(
        sqlColumn = "pl_number",
        exposedExpression = Pls.qmnum.alias("qmnum"),
        grouping = true,
        columnTitle = "Номер ПЛ"
    ),
    labeledWB(
        sqlColumn = "labeled_quant",
        exposedExpression = intLiteral(1).alias("labeled_quant"),
        grouping = true,
        isCalculated = true,
        columnTitle = "ПЛ с метками",
        type = ColumnType.NUMBER
    ),
    status(
        sqlColumn = "user_status",
        exposedExpression = Pls.userStat.alias("user_stat"),
        grouping = true,
        columnTitle = "Статус ПЛ"
    ),
    atp(
        sqlColumn = "atp_name",
        exposedExpression = OrganizationalUnitsTimelineTable.atpName.alias("atp_name"),
        grouping = true,
        columnTitle = "АТП"
    ),
    mvz(
        sqlColumn = "mvz_id",
        exposedExpression = Pls.mvzId.alias("mvz_id"),
        grouping = true,
        columnTitle = "МВЗ"
    ),
    licenseNum(
        sqlColumn = "vehicle_license",
        exposedExpression = Pls.vehicleLicense.alias("vehicle_license"),
        grouping = true,
        columnTitle = "Гос. номер ТС"
    ),
    trailerLicenseNum(
        sqlColumn = "trailer_license_num",
        exposedExpression = Pls.trailerLicenseNum.alias("trailer_license_num"),
        grouping = true,
        columnTitle = "Гос. номер прицепа"
    ),
    dateOpen(
        sqlColumn = "pl_start_date",
        exposedExpression = Pls.startDateFact.alias("pl_start_date"),
        grouping = true,
        columnTitle = "Дата открытия",
        type = ColumnType.DATE
    ),
    timeOpen(
        sqlColumn = "pl_start_time",
        exposedExpression = Pls.startTimeFact.alias("pl_start_time"),
        grouping = true,
        columnTitle = "Время открытия",
        type = ColumnType.TIME
    ),
    dateClose(
        sqlColumn = "pl_end_date_plan",
        exposedExpression = Pls.endDatePlan.alias("pl_end_date_plan"),
        grouping = true,
        columnTitle = "Плановая дата закрытия",
        type = ColumnType.DATE
    ),
    timeClose(
        sqlColumn = "pl_end_time_plan",
        exposedExpression = Pls.endTimePlan.alias("pl_end_time_plan"),
        grouping = true,
        columnTitle = "Плановое время закрытия",
        type = ColumnType.TIME
    ),
    dateCloseReference(
        sqlColumn = "pl_end_date_fact",
        exposedExpression = Pls.endDateFact.alias("end_date_fact"),
        grouping = true,
        columnTitle = "Ссылочная дата закрытия",
        type = ColumnType.DATE
    ),
    timeCloseReference(
        sqlColumn = "pl_end_time_fact",
        exposedExpression = Pls.endTimeFact.alias("pl_end_time_fact"),
        grouping = true,
        columnTitle = "Ссылочное время закрытия",
        type = ColumnType.TIME
    ),
    driverNumber(
        sqlColumn = "driver_number",
        exposedExpression = Pls.driverNumber.alias("driver_number"),
        grouping = true,
        columnTitle = "Табельный номер водителя"
    ),
    driver(
        sqlColumn = "driver_number",
        exposedExpression = Pls.driverNumber.alias("driver_number"),
        grouping = true,
        columnTitle = "Водитель",
        type = ColumnType.NUMBER
    ),
    brand(
        sqlColumn = "marka",
        exposedExpression = Pls.marka.alias("marka"),
        grouping = true,
        columnTitle = "Марка"
    ),
    model(
        sqlColumn = "model",
        exposedExpression = Pls.model.alias("model"),
        grouping = true,
        columnTitle = "Модель"
    ),
    year(
        sqlColumn = "year",
        exposedExpression = Pls.year.alias("year"),
        grouping = true,
        columnTitle = "Год выпуска",
        type = ColumnType.NUMBER
    ),
    gbo(
        sqlColumn = "gbo",
        exposedExpression = Pls.gbo.alias("gbo"),
        grouping = true,
        columnTitle = "ГБО",
        type = ColumnType.BOOLEAN
    ),
    tonnage(
        sqlColumn = "load_wgt",
        exposedExpression = Pls.loadWgt.alias("load_wgt"),
        grouping = true,
        columnTitle = "Тоннаж",
        type = ColumnType.NUMBER
    ),
    eqUnit(
        sqlColumn = "equnr",
        exposedExpression = Pls.equnr.alias("equnr"),
        grouping = true,
        columnTitle = "Единица оборудования",
        type = ColumnType.NUMBER
    ),
    commissioningDate(
        sqlColumn = "commissioning_year",
        exposedExpression = Pls.commissioningYear.alias("commissioning_year"),
        grouping = true,
        columnTitle = "Год ввода в эксплуатацию",
        type = ColumnType.NUMBER
    ),
    vin(
        sqlColumn = "fleet_num",
        exposedExpression = Pls.fleetNum.alias("fleet_num"),
        grouping = true,
        columnTitle = "VIN номер"
    ),
    mileage(
        sqlColumn = "probeg",
        exposedExpression = Pls.mileage.alias("probeg"),
        grouping = true,
        isCalculated = true,
        columnTitle = "Пробег по ПЛ",
        type = ColumnType.NUMBER
    ),
    hours(
        sqlColumn = "hours",
        exposedExpression = Pls.hours.alias("hours"),
        grouping = true,
        isCalculated = true,
        columnTitle = "Часы по ПЛ",
        type = ColumnType.NUMBER
    ),
    motoHours(
        sqlColumn = "moto_hours",
        exposedExpression = Pls.motoHours.alias("moto_hours"),
        grouping = true,
        isCalculated = true,
        columnTitle = "Моточасы ХОУ",
        type = ColumnType.NUMBER
    ),
    primaryFuel(
        sqlColumn = "fuel_pri",
        exposedExpression = Pls.fuelPri.alias("fuel_pri"),
        grouping = true,
        isCalculated = true,
        columnTitle = "Первичное топливо",
        type = ColumnType.NUMBER
    ),
    secondaryFuel(
        sqlColumn = "fuel_sec",
        exposedExpression = Pls.fuelSec.alias("fuel_sec"),
        grouping = true,
        isCalculated = true,
        columnTitle = "Вторичное топливо",
        type = ColumnType.NUMBER
    ),
    vehicleGroup(
        sqlColumn = "ts_group",
        exposedExpression = Pls.tsGroup.alias("ts_group"),
        grouping = true,
        columnTitle = "Вид ТС"
    ),
    vehicleType(
        sqlColumn = "ts_type",
        exposedExpression = Pls.tsType.alias("ts_type"),
        grouping = true,
        columnTitle = "Тип ТС"
    ),
    mvzName(
        sqlColumn = "mvz_name",
        exposedExpression = Pls.mvzName.alias("mvz_name"),
        grouping = true,
        columnTitle = "Название МВЗ"
    ),
    mvzNameInWB(
        sqlColumn = "pl_mvz_name",
        exposedExpression = Pls.plMvzName.alias("pl_mvz_name"),
        grouping = true,
        columnTitle = "Название МВЗ в ПЛ"
    ),
    terName(
        sqlColumn = "territory_name",
        exposedExpression = OrganizationalUnitsTimelineTable.territoryName.alias("territory_name"),
        grouping = true,
        columnTitle = "Территория"
    ),
    mrName(
        sqlColumn = "mr_name",
        exposedExpression = OrganizationalUnitsTimelineTable.mrName.alias("mr_name"),
        grouping = true,
        columnTitle = "Название макрорегиона"
    ),
    waybillQty(
        sqlColumn = "pl_quant",
        exposedExpression = intLiteral(1).alias("pl_quant"),
        grouping = true,
        isCalculated = true,
        columnTitle = "Кол-во ПЛ",
        type = ColumnType.NUMBER
    ),
    maintenance(
        sqlColumn = "msaus",
        exposedExpression = Pls.msaus.alias("msaus"),
        grouping = true,
        columnTitle = "Признак ремонтного ПЛ",
        type = ColumnType.BOOLEAN
    ),
    mvzInWB(
        sqlColumn = "pl_mvz",
        exposedExpression = Pls.plMvzId.alias("pl_mvz_id"),
        grouping = true,
        columnTitle = "Номер МВЗ в ПЛ"
    ),
    typeWB(
        sqlColumn = "pl_type",
        exposedExpression = Pls.plType.alias("pl_type"),
        grouping = true,
        columnTitle = "Тип ПЛ"
    ),
    commerce(
        sqlColumn = "pl_commerce",
        exposedExpression = Pls.plCommerce.alias("pl_commerce"),
        grouping = true,
        type = ColumnType.BOOLEAN,
        columnTitle = "Признак коммерция"
    ),
    tod(
        sqlColumn = "pl_tod",
        exposedExpression = Pls.plTod.alias("pl_tod"),
        grouping = true,
        type = ColumnType.BOOLEAN,
        columnTitle = "Признак командировка"
    ),
    transit(
        sqlColumn = "pl_transit",
        exposedExpression = Pls.plTransit.alias("pl_transit"),
        grouping = true,
        type = ColumnType.BOOLEAN,
        columnTitle = "Признак перегон"
    );

    @Suppress("UNCHECKED_CAST")
    fun <T> getExpression(): Expression<T?> {
        return exposedExpression.aliasOnlyExpression() as Expression<T?>
    }
}
