package com.x5.logistics.rest.dto.temperature

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.common.CharacterDeliveryForRetailNetwork
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос на виджет температурный режим в рейсе")
data class TemperatureWidgetReq(
    @Schema(description = "Дата начала периода")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val from: LocalDate,

    @Schema(description = "Дата конца периода")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val to: LocalDate,

    @Schema(description = "Глобальные фильтры")
    val geoFilter: GeoFilter,

    @Schema(description = "Цепочки поставок для торговых сетей")
    val characterDeliveryForRetailNetwork: List<CharacterDeliveryForRetailNetwork>
) {

}

