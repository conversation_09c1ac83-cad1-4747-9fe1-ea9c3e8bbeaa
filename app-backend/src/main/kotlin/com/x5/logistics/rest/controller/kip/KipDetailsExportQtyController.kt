package com.x5.logistics.rest.controller.kip

import com.x5.logistics.repository.KipExposedRepo
import com.x5.logistics.rest.dto.kip.details.KipDetailsColumn
import com.x5.logistics.rest.dto.kip.details.KipDetailsQtyItem
import com.x5.logistics.rest.dto.kip.details.KipDetailsQtyReq
import com.x5.logistics.rest.exception.ExportTimeoutException
import com.x5.logistics.service.RowBuilder
import com.x5.logistics.service.VehicleUsageService
import com.x5.logistics.service.WbBuilder
import com.x5.logistics.service.settingssheet.ExportRequest
import com.x5.logistics.service.settingssheet.Filter
import com.x5.logistics.service.settingssheet.ReportName
import com.x5.logistics.service.settingssheet.SettingsSheetService
import com.x5.logistics.service.settingssheet.SortOrder
import com.x5.logistics.service.streamWorkbook
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import com.x5.logistics.util.EXPORT_TIMEOUT
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.moscowDateTime
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.apache.poi.hssf.util.HSSFColor
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.FillPatternType
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import java.net.URLEncoder
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
class KipDetailsExportQtyController(
    val service: VehicleUsageService,
    val settingsSheetService: SettingsSheetService,
    val repo: KipExposedRepo
) {
    private val log = getLogger()

    @Tag(name = "КИП данные.")
    @Operation(summary = "Запрос на экспорт в XLSX детального отчета КИП по количеству транспортных средств для заданного периода дат и МВЗ.")
    @SwaggerReqBody(
        description = "Запрос на экспорт детального отчета по КИП в Xlsx.",
        content = [Content(schema = Schema(implementation = KipDetailsQtyReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(
                        type = "string",
                        format = "binary"
                    )
                )]
            )
        ]
    )
    @PostMapping("/api/kip/details/export")
    fun process(@RequestBody req: KipDetailsQtyReq, token: JwtToken?): ResponseEntity<Resource> {
        val timeout = System.currentTimeMillis() + EXPORT_TIMEOUT
        log.debug("Kip details qty export to Xlsx. [req={}]", req)

        val singleItem = repo.getReport(req.copy(pageNumber = 0, pageSize = 1))
        val count = repo.getReport(req.copy(pageNumber = 0, pageSize = 1)).count
        val granularDates = singleItem.items.firstOrNull()?.granularityAvgKip
        var columns = req.columns
        val avgKipFlag = columns.contains(KipDetailsColumn.avgKip)
        val avgKipNoReserveFlag = columns.contains(KipDetailsColumn.kipNoReserve)
        columns = columns.filter { it != KipDetailsColumn.avgKip  && it != KipDetailsColumn.kipNoReserve }
        log.debug("Started generating Excel")
        val out = streamWorkbook {
            val greyFont = wb.createFont()
            greyFont.color = HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.index
            val blackFont = wb.createFont()
            blackFont.color = HSSFColor.HSSFColorPredefined.BLACK.index

            val coloredStyles = createColoredStyles(this)
            val headerStyle = ExcelStyles.Header.style(this)
            val generalStyle = ExcelStyles.General.style(this)
            val floatStyle = ExcelStyles.Float.style(this)
            val dateStyle = ExcelStyles.Date.style(this)
            val styles = mapOf(
                "general" to generalStyle,
                "float" to floatStyle,
                "date" to dateStyle
            )
            val percentFormat = this.wb.createDataFormat().getFormat("0.0%")
            val percentStyle = this.wb.createCellStyle()
            percentStyle.dataFormat = percentFormat
            style {
                alignment = HorizontalAlignment.CENTER
                verticalAlignment = VerticalAlignment.CENTER
            }
            val reportReq = ExportRequest(
                from = req.from,
                to = req.to,
                granularitySupported = true,
                granularityValue = req.granularity?.description,
                userName = token.username,
                geoFilter = req.geoFilter,
                columns = req.columns.map { it.columnTitle },
                filters = req.filters.map { Filter(it.name.columnTitle, it.condition.description, it.value) },
                sort = req.sort.map { SortOrder(it.column.columnTitle, it.asc) }
            )
            settingsSheetService.addSettingsSheet(this, reportReq)
            sheet(ReportName.KIP.title) {
                header {
                    currStyle = headerStyle
                    columns.map { if (it == KipDetailsColumn.gbo) "ГБО" else it.columnTitle }.forEach { head(it) }
                    if (avgKipFlag) {
                        if (req.granularity != null) {
                            head("КИП, %") {
                                granularDates?.forEach {
                                    style {
                                        if (it.partPeriod) {
                                            setFont(greyFont)
                                        } else {
                                            setFont(blackFont)
                                        }
                                    }
                                    head(it.label)
                                    style { setFont(blackFont) }
                                }
                            }
                        }
                        head("КИП, %")
                    }
                    if (avgKipNoReserveFlag) {
                        if (req.granularity != null) {
                            head("КИП без резерв. ПЛ, %") {
                                granularDates?.forEach {
                                    style {
                                        if (it.partPeriod) {
                                            setFont(greyFont)
                                        } else {
                                            setFont(blackFont)
                                        }
                                    }
                                    head(it.label)
                                    style { setFont(blackFont) }
                                }
                            }
                        }
                        head("КИП без резерв. ПЛ, %")
                    }
                }
                for (pageNumber in 0..(count / EXPORT_BATCH_SIZE)) {
                    if (System.currentTimeMillis() > timeout) throw ExportTimeoutException("export timeout")
                    val data = repo.getReport(req.copy(pageNumber = pageNumber, pageSize = EXPORT_BATCH_SIZE))
                    data.items.forEach { item ->
                        row {
                            columns.forEach { column -> cellByColumnType(this, styles, column, item) }
                            val optionalValues: MutableList<Double?> = ArrayList()
                            if (avgKipFlag) {
                                if (req.granularity != null) {
                                    item.granularityAvgKip?.map { optionalValues.add(it.value as Double?) }
                                }
                                item.avgKip.let { optionalValues.add(it) }
                                optionalValues.forEach { value ->
                                    cell(value?.div(100), cc = {
                                        it.cellStyle = coloredStyles.findColoredStyle(value)
                                    })
                                }
                            }
                            val optionalValuesNoReserve = mutableListOf<Double?>()
                            if (avgKipNoReserveFlag) {
                                if (req.granularity != null) {
                                    item.granularityKipNoReserve?.map { optionalValuesNoReserve.add(it.value as Double?) }
                                }
                                item.kipNoReserve.let { optionalValuesNoReserve.add(it) }
                                optionalValuesNoReserve.forEach { value ->
                                    cell(value?.div(100), cc = {
                                        it.cellStyle = coloredStyles.findColoredStyle(value)
                                    })
                                }
                            }
                        }
                        (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
                    }
                }
                row {
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Всего строк:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(singleItem.count)
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Итоговый КИП за период:")
                    style { alignment = HorizontalAlignment.LEFT }
                    currStyle = percentStyle
                    cell(singleItem.totalAvg / 100)
                }
            }.autosize()
            setActiveSheet(ReportName.KIP.title)
            log.debug("Data loaded into Excel workbook")
        }.toInputStream()
        val fileName = "${ReportName.KIP.title} ${moscowDateTime()}.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${URLEncoder.encode(fileName, "UTF-8").replace("+", "%20")}"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(out))
    }

    private fun Map<Double?, CellStyle>.findColoredStyle(value: Double?): CellStyle {
        if (value == null) return getValue(null)
        return getValue(keys.asSequence().filterNotNull().filter { value >= it }.max())
    }

    private fun createColoredStyles(wb: WbBuilder) =
        buildMap(colorBoundaries.size + 1) {
            colorBoundaries.forEach { (boundary, color) ->
                put(boundary, ExcelStyles.Percent.style(wb).apply {
                    val customColor = wb.wb.creationHelper.createExtendedColor()
                    customColor.argbHex = color
                    setFillForegroundColor(customColor)
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                })
            }
            put(null, ExcelStyles.Percent.style(wb))
        }

    private val colorBoundaries = mapOf(
        90.0 to "FFFFFFFF",
        80.0 to "FFFFD2D2",
        70.0 to "FFFFA49E",
        60.0 to "FFFB7B6F",
        Double.NEGATIVE_INFINITY to "FFF45948"
    )

    fun cellByColumnType(
        rb: RowBuilder,
        styles: Map<String, CellStyle>,
        column: KipDetailsColumn,
        item: KipDetailsQtyItem
    ) = when (column) {
        KipDetailsColumn.vehicleGroup -> {
            rb.cell(item.vehicleGroup).cellStyle = styles["general"]
        }

        KipDetailsColumn.vehicleType -> {
            rb.cell(item.vehicleType).cellStyle = styles["general"]
        }

        KipDetailsColumn.atp -> {
            rb.cell(item.atp).cellStyle = styles["general"]
        }

        KipDetailsColumn.mvz -> {
            rb.cell(item.mvz).cellStyle = styles["general"]
        }

        KipDetailsColumn.mvzName -> {
            rb.cell(item.mvzName).cellStyle = styles["general"]
        }

        KipDetailsColumn.mvzType -> {
            rb.cell(item.mvzType).cellStyle = styles["general"]
        }

        KipDetailsColumn.brand -> {
            rb.cell(item.brand).cellStyle = styles["general"]
        }

        KipDetailsColumn.model -> {
            rb.cell(item.model).cellStyle = styles["general"]
        }

        KipDetailsColumn.year -> {
            rb.cell(item.year).cellStyle = styles["general"]
        }

        KipDetailsColumn.tonnage -> {
            rb.cell(item.tonnage).cellStyle = styles["float"]
        }

        KipDetailsColumn.mileage -> {
            rb.cell(item.mileage).cellStyle = styles["float"]
        }

        KipDetailsColumn.licenseNum -> {
            rb.cell(item.licenseNum).cellStyle = styles["general"]
        }

        KipDetailsColumn.eqUnit -> {
            rb.cell(item.eqUnit).cellStyle = styles["general"]
        }

        KipDetailsColumn.vin -> {
            rb.cell(item.vin).cellStyle = styles["general"]
        }

        KipDetailsColumn.gbo -> {
            rb.cell(if (item.gbo == true) "Есть" else "").cellStyle = styles["general"]
        }

        KipDetailsColumn.retailNetwork -> {
            rb.cell(item.retailNetwork).cellStyle = styles["general"]
        }

        KipDetailsColumn.mrName -> {
            rb.cell(item.mrName).cellStyle = styles["general"]
        }

        KipDetailsColumn.commissioningDate -> {
            rb.cell(item.commissioningDate).cellStyle = styles["general"]
        }

        KipDetailsColumn.terName -> {
            rb.cell(item.terName).cellStyle = styles["general"]
        }

        else -> rb.cell("").cellStyle = styles["general"]
    }
}

