create schema ts;
create schema airflow_monitoring;

create or replace view unit as select 1;--

            create or replace function getFirstWeekDayOfYear(year int, sday int) returns date
            language plpgsql immutable as $$
            declare
                firstDayOfYear date;
            begin
                firstDayOfYear := (year || '-01-01')::date;
                return firstDayOfYear - mod(extract(dow from firstDayOfYear)::int + 7 - sday, 7);
            end;
            $$;--

            drop type if exists granularity_week_number cascade;--
            create type granularity_week_number as (
                week smallint,
                year int
            );--

            create or replace function get_granularity_week_number(d date, sday int) returns granularity_week_number
            language plpgsql immutable as $$
            declare
                y int;
                sd date;
            begin
                y := extract(year from d)::int + 1;
                sd := getFirstWeekDayOfYear(y, sday);
                while sd > d loop
                    y := y - 1;
                    sd := getFirstWeekDayOfYear(y, sday);
                end loop;
                return ((d - sd) / 7 + 1, y)::granularity_week_number;
            end;
            $$;--

            create or replace function getReportingWeek(d date) returns text
            language plpgsql immutable parallel safe as $$
            declare
                w granularity_week_number;
            begin
                w := get_granularity_week_number(d, 5);
                return w.week || '.' || w.year;
            end;
            $$;--

            create or replace function getGranularityWeek(d date) returns text
            language plpgsql immutable parallel safe as $$
            declare
                w granularity_week_number;
                wstr text;
            begin
                w := get_granularity_week_number(d, 1);
                if w.week < 10 then
                    wstr := '0' || w.week;
                else
                    wstr := '' || w.week;
                end if;
                return 'H ' || wstr || '.' || w.year;
            end;
            $$;--

            create or replace function getGranularityLabel(granularity text, d date) returns text
            language plpgsql immutable parallel safe as $$
            begin
                case granularity
                    when 'DAY' then return to_char(d, 'DD.MM.YYYY');
                    when 'WEEK' then return getGranularityWeek(d);
                    when 'REPORTING_WEEK' then return getReportingWeek(d);
                    when 'MONTH' then return to_char(d, 'Mon YYYY');
                    when 'QUARTER' then return to_char(d, '"Q" Q.YYYY');
                    when 'YEAR' then return to_char(d, '"Y" YYYY');
                    else raise 'invalid granularity: %', granularity;
                end case;
            end;
            $$;--

            create or replace function getGranularityBorder(granularity text, d date, delta int) returns date
            language plpgsql immutable parallel safe as $$
            declare
                nextd date;
                lab text;
            begin
                lab = getGranularityLabel(granularity, d);
                nextd := d + delta;
                while lab = getGranularityLabel(granularity, nextd) loop
                    d := nextd;
                    nextd := d + delta;
                end loop;
                return d;
            end;
            $$;--

            create or replace function getGranularityFrom(granularity text, d date) returns date
            language plpgsql immutable parallel safe as $$
            begin
                return getGranularityBorder(granularity, d, -1);
            end;
            $$;--

            create or replace function getGranularityTo(granularity text, d date) returns date
            language plpgsql immutable parallel safe as $$
            begin
                return getGranularityBorder(granularity, d, +1);
            end;
            $$;--


CREATE TABLE ts.repairs_rub_km_plan (
    date_from date NULL,
    date_to date NULL,
    coef numeric NULL
);

CREATE TABLE ts.nh_vehicles (
                                "type" varchar NOT NULL,
                                brand varchar NOT NULL,
                                vehicle_tonnage float8 NOT NULL,
                                vehicle_create_year int4 NOT NULL,
                                id bigserial NOT NULL,
                                CONSTRAINT vehicle_pk PRIMARY KEY (id)
);

CREATE TABLE ts.toro_works (
                               id varchar NOT NULL,
                               "name" varchar NULL,
                               subtype_id int4 NULL,
                               type_id int4 NULL,
                               updated_by varchar NULL,
                               updated_at timestamptz NULL
);

CREATE TABLE ts.hr_places (
                              "name" varchar NULL,
                              created_at timestamp NULL,
                              created_by varchar NULL,
                              updated_at timestamp NULL,
                              updated_by varchar NULL,
                              deleted bool NULL DEFAULT false,
                              id serial4 NOT NULL,
                              CONSTRAINT hr_places_pkey PRIMARY KEY (id)
);



CREATE TABLE ts.hr_total_places (
                                    "name" varchar NULL,
                                    id serial4 NOT NULL,
                                    created_at timestamp NULL,
                                    created_by varchar NULL,
                                    updated_at timestamp NULL,
                                    updated_by varchar NULL,
                                    aggregated bool NULL,
                                    deleted bool NULL DEFAULT false,
                                    CONSTRAINT hr_total_places_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX name_idx ON ts.hr_total_places USING btree (name) WHERE (deleted = false);

CREATE TABLE ts.hr_places_tplaces_log (
                                          id serial4 NOT NULL,
                                          p_id int4 NULL,
                                          tp_id int4 NULL,
                                          start_date date NULL,
                                          created_at timestamp NULL,
                                          created_by varchar NULL,
                                          updated_at timestamp NULL,
                                          updated_by varchar NULL,
                                          deleted bool NULL DEFAULT false,
                                          CONSTRAINT hr_places_tplaces_log_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX p_id_date_idx ON ts.hr_places_tplaces_log USING btree (p_id, start_date);
ALTER TABLE ts.hr_places_tplaces_log ADD CONSTRAINT hr_places_fk FOREIGN KEY (p_id) REFERENCES ts.hr_places(id);
ALTER TABLE ts.hr_places_tplaces_log ADD CONSTRAINT hr_total_places_fk FOREIGN KEY (tp_id) REFERENCES ts.hr_total_places(id);

CREATE TABLE ts.mvz_codes (
                              uid varchar NOT NULL,
                              "name" varchar NULL,
                              "type" varchar NULL,
                              CONSTRAINT mvz_codes_pkey PRIMARY KEY (uid)
);

CREATE TABLE ts.repair_base_properties_atp (
                                               id int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                               atp_id int8 NULL,
                                               property varchar(50) NULL,
                                               "year" int4 NULL,
                                               "month" varchar(20) NULL,
                                               value numeric NULL,
                                               created_at timestamptz NULL,
                                               created_by varchar(100) NULL,
                                               updated_at timestamptz NULL,
                                               updated_by varchar(100) NULL,
                                               CONSTRAINT rapair_base_properties_atp_pk PRIMARY KEY (id)
);

CREATE TABLE ts.repshops (
                             id serial4 NOT NULL,
                             "name" varchar NOT NULL,
                             created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP(0)::timestamp without time zone,
                             created_by varchar NOT NULL DEFAULT 'system'::character varying,
                             updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP(0)::timestamp without time zone,
                             updated_by varchar NOT NULL DEFAULT 'system'::character varying,
                             deleted bool NOT NULL DEFAULT false,
                             has_properties bool NOT NULL DEFAULT true,
                             CONSTRAINT repshops_pk PRIMARY KEY (id)
);

CREATE TABLE pl_fuel_stats (
                               msaus BOOLEAN NOT NULL,
                               pl_type TEXT NOT NULL,
                               mr_id BIGINT NOT NULL,
                               mr_name TEXT NOT NULL,
                               atp_id BIGINT NOT NULL,
                               atp_name TEXT NOT NULL,
                               mvz_id TEXT NOT NULL,
                               mvz_name TEXT NOT NULL,
                               retail_network TEXT NOT NULL,
                               atp_type TEXT NOT NULL,
                               mvz_type TEXT NOT NULL,
                               date DATE NOT NULL,
                               probeg FLOAT NULL,
                               rashod_gas_plan FLOAT NOT NULL,
                               rashod_gas_fact FLOAT NOT NULL,
                               pl_start_date TIMESTAMP NOT NULL,
                               pl_end_date TIMESTAMP NOT NULL,
                               qmnum TEXT NOT NULL,
                               probeg_gorny INT NOT NULL,
                               moto_hours_head FLOAT NULL,
                               moto_hours_trail FLOAT NULL,
                               k1_p FLOAT NULL,
                               norma_hou_head FLOAT NULL,
                               norma_hou_trail FLOAT NULL,
                               norma_gas FLOAT NULL,
                               coef_zimny FLOAT NULL,
                               coef_gorny FLOAT NULL,
                               coef_reduce_fuel FLOAT NULL,
                               fuel_price FLOAT NOT NULL,
                               gas_price FLOAT NOT NULL,
                               ts_type TEXT NOT NULL,
                               ts_group TEXT NOT NULL,
                               marka TEXT NOT NULL,
                               model TEXT NOT NULL,
                               load_wgt FLOAT NOT NULL,
                               model_year INT NULL,
                               create_date DATE NOT NULL,
                               driver_tabnum TEXT NULL,
                               license_num TEXT NOT NULL,
                               fuel_type_first TEXT NULL,
                               fleet_num TEXT NOT NULL,
                               equnr TEXT NOT NULL,
                               license_num_pri TEXT NULL,
                               gbo BOOLEAN NOT NULL,
                               rashod_fuel_plan FLOAT NOT NULL,
                               rashod_fuel_plan_rub FLOAT NOT NULL,
                               rashod_fuel_plan_head_tot FLOAT NOT NULL,
                               rashod_fuel_plan_head_hou FLOAT NOT NULL,
                               rashod_fuel_plan_head FLOAT NOT NULL,
                               rashod_fuel_plan_trail_hou FLOAT NOT NULL,
                               rashod_fuel_fact FLOAT NOT NULL,
                               rashod_fuel_fact_head_tot FLOAT NOT NULL,
                               rashod_fuel_fact_head_hou FLOAT NOT NULL,
                               rashod_fuel_fact_head FLOAT NOT NULL,
                               rashod_fuel_fact_trail_hou FLOAT NOT NULL,
                               econ_fuel FLOAT NOT NULL,
                               econ_fuel_head FLOAT NOT NULL,
                               econ_fuel_trail_hou FLOAT NOT NULL,
                               rashod_fuel_plan_trail_hou_rub FLOAT NOT NULL,
                               rashod_fuel_plan_head_tot_rub FLOAT NOT NULL,
                               rashod_fuel_plan_head_hou_rub FLOAT NOT NULL,
                               rashod_fuel_plan_head_rub FLOAT NOT NULL,
                               rashod_fuel_fact_rub FLOAT NOT NULL,
                               rashod_fuel_fact_head_tot_rub FLOAT NOT NULL,
                               rashod_fuel_fact_head_hou_rub FLOAT NOT NULL,
                               rashod_fuel_fact_head_rub FLOAT NOT NULL,
                               rashod_fuel_fact_trail_hou_rub FLOAT NOT NULL,
                               econ_fuel_rub FLOAT NOT NULL,
                               econ_fuel_head_rub FLOAT NOT NULL,
                               econ_fuel_trail_hou_rub FLOAT NOT NULL,
                               econ_gas FLOAT NOT NULL,
                               rashod_gas_plan_rub FLOAT NOT NULL,
                               rashod_gas_fact_rub FLOAT NOT NULL,
                               econ_gas_rub FLOAT NOT NULL,
                               rashod_tot_plan FLOAT NOT NULL,
                               rashod_tot_fact FLOAT NOT NULL,
                               econ_tot FLOAT NOT NULL,
                               rashod_tot_plan_rub FLOAT NOT NULL,
                               rashod_tot_fact_rub FLOAT NOT NULL,
                               econ_tot_rub FLOAT NOT NULL,
                               rashod_fuel_without_gbo_assumed FLOAT NOT NULL,
                               effect_fuel_of_gbo_plan FLOAT NOT NULL,
                               effect_fuel_of_gbo_fact FLOAT NOT NULL,
                               effect_tot_of_gbo_plan_rub FLOAT NOT NULL,
                               effect_tot_of_gbo_fact_rub FLOAT NOT NULL,
                               econ_tot_of_gbo_rub FLOAT NOT NULL
);

INSERT INTO pl_fuel_stats (
    msaus, pl_type, mr_id, mr_name, atp_id, atp_name, mvz_id, mvz_name, retail_network,
    atp_type, mvz_type, date, probeg, rashod_gas_plan, rashod_gas_fact, pl_start_date,
    pl_end_date, qmnum, probeg_gorny, moto_hours_head, moto_hours_trail, k1_p, norma_hou_head,
    norma_hou_trail, norma_gas, coef_zimny, coef_gorny, coef_reduce_fuel, fuel_price, gas_price,
    ts_type, ts_group, marka, model, load_wgt, model_year, create_date, driver_tabnum,
    license_num, fuel_type_first, fleet_num, equnr, license_num_pri, gbo, rashod_fuel_plan,
    rashod_fuel_plan_rub, rashod_fuel_plan_head_tot, rashod_fuel_plan_head_hou, rashod_fuel_plan_head,
    rashod_fuel_plan_trail_hou, rashod_fuel_fact, rashod_fuel_fact_head_tot, rashod_fuel_fact_head_hou,
    rashod_fuel_fact_head, rashod_fuel_fact_trail_hou, econ_fuel, econ_fuel_head, econ_fuel_trail_hou,
    rashod_fuel_plan_trail_hou_rub, rashod_fuel_plan_head_tot_rub, rashod_fuel_plan_head_hou_rub,
    rashod_fuel_plan_head_rub, rashod_fuel_fact_rub, rashod_fuel_fact_head_tot_rub,
    rashod_fuel_fact_head_hou_rub, rashod_fuel_fact_head_rub, rashod_fuel_fact_trail_hou_rub,
    econ_fuel_rub, econ_fuel_head_rub, econ_fuel_trail_hou_rub, econ_gas, rashod_gas_plan_rub,
    rashod_gas_fact_rub, econ_gas_rub, rashod_tot_plan, rashod_tot_fact, econ_tot, rashod_tot_plan_rub,
    rashod_tot_fact_rub, econ_tot_rub, rashod_fuel_without_gbo_assumed, effect_fuel_of_gbo_plan,
    effect_fuel_of_gbo_fact, effect_tot_of_gbo_plan_rub, effect_tot_of_gbo_fact_rub, econ_tot_of_gbo_rub
) VALUES
      (true, 'Type1', 1, 'Region1', 101, 'ATP1', 'MVZ001', 'MVZ Name 1', 'Retail1', 'Type1', 'TypeA', '2023-01-01', 100.0, 15.0, 14.5, '2023-01-01 08:00:00', '2023-01-01 17:00:00', 'QM001', 10, 15.5, 20.0, 0.8, 10.5, 12.0, 15.0, 0.9, 0.7, 0.85, 1.5, 2.0, 'TypeX', 'GroupA', 'Brand1', 'Model1', 1500.0, 2020, '2023-01-01', 'DT001', 'LIC001', 'Diesel', 'F001', 'EQ001', 'PRI001', TRUE, 20.5, 25.0, 10.0, 5.5, 3.0, 30.5, 35.0, 40.5, 45.5, 50.5, 12.5, 0.9, 0.7, 0.8, 1.9, 1.2, 1.1, 2.5, 2.0, 1.7, 1.9, 1.4, 2.5, 2.0, 35.5, 30.0, 28.5, 15.5, 14.0, 12.5, 1.9, 20.5, 18.0, 17.5, 11.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1),
      (true, 'Type1', 2, 'Region2', 102, 'ATP2', 'MVZ002', 'MVZ Name 2', 'Retail2', 'Type2', 'TypeB', '2023-01-02', 101.0, 16.0, 0, '2023-01-02 08:00:00', '2023-01-02 17:00:00', 'QM002', 11, 16.5, 21.0, 0.85, 11.5, 13.0, 16.0, 0.95, 0.75, 0.9, 1.55, 2.05, 'TypeY', 'GroupB', 'Brand2', 'Model2', 1600.0, 2021, '2023-01-02', 'DT002', 'LIC002', 'Gasoline', 'F002', 'EQ002', 'PRI002', FALSE, 21.5, 26.0, 11.0, 6.5, 4.0, 31.5, 36.0, 41.5, 46.5, 51.5, 13.5, 0.95, 0.75, 0.85, 2.0, 1.3, 1.2, 2.6, 2.1, 1.8, 2.0, 1.5, 2.6, 2.1, 36.5, 31.0, 29.5, 16.5, 15.0, 13.5, 2.0, 21.5, 19.0, 18.5, 12.1, 2.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1),
      (true, 'Type1', 3, 'Region3', 103, 'ATP3', 'MVZ003', 'MVZ Name 3', 'Retail3', 'Type3', 'TypeC', '2023-01-03', 102.0, 17.0, 0, '2023-01-03 08:00:00', '2023-01-03 17:00:00', 'QM003', 12, 17.5, 22.0, 0.9, 12.5, 14.0, 17.0, 1.0, 0.8, 0.95, 1.6, 2.1, 'TypeZ', 'GroupC', 'Brand3', 'Model3', 1700.0, 2022, '2023-01-03', 'DT003', 'LIC003', 'Electric', 'F003', 'EQ003', 'PRI003', TRUE, 22.5, 27.0, 12.0, 7.5, 5.0, 32.5, 37.0, 42.5, 47.5, 52.5, 14.5, 1.0, 0.8, 0.9, 2.1, 1.4, 1.3, 2.7, 2.2, 1.9, 2.1, 1.6, 2.7, 2.2, 37.5, 32.0, 30.5, 17.5, 16.0, 14.5, 2.1, 22.5, 20.0, 19.5, 13.1, 2.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1),
      (true, 'Type1', 4, 'Region4', 104, 'ATP4', 'MVZ004', 'MVZ Name 4', 'Retail4', 'Type4', 'TypeD', '2023-01-04', 103.0, 18.0, 0, '2023-01-04 08:00:00', '2023-01-04 17:00:00', 'QM004', 13, 18.5, 23.0, 0.95, 13.5, 15.0, 18.0, 1.05, 0.85, 1.0, 1.65, 2.15, 'TypeA', 'GroupD', 'Brand4', 'Model4', 1800.0, 2023, '2023-01-04', 'DT004', 'LIC004', 'Diesel', 'F004', 'EQ004', 'PRI004', FALSE, 23.5, 28.0, 13.0, 8.5, 6.0, 33.5, 38.0, 43.5, 48.5, 53.5, 15.5, 1.05, 0.85, 0.95, 2.2, 1.5, 1.4, 2.8, 2.3, 2.0, 2.2, 1.7, 2.8, 2.3, 38.5, 33.0, 31.5, 18.5, 17.0, 15.5, 2.2, 23.5, 21.0, 20.5, 14.1, 3.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1),
      (true, 'Type1', 5, 'Region5', 105, 'ATP5', 'MVZ005', 'MVZ Name 5', 'Retail5', 'Type5', 'TypeE', '2023-01-05', 104.0, 19.0, 18.5, '2023-01-05 08:00:00', '2023-01-05 17:00:00', 'QM005', 14, 19.5, 24.0, 1.0, 14.5, 16.0, 19.0, 1.1, 0.9, 1.05, 1.7, 2.2, 'TypeB', 'GroupE', 'Brand5', 'Model5', 1900.0, NULL, '2023-01-05', 'DT005', 'LIC005', 'Gasoline', 'F005', 'EQ005', 'PRI005', TRUE, 24.5, 29.0, 14.0, 9.5, 7.0, 34.5, 39.0, 44.5, 49.5, 54.5, 16.5, 1.1, 0.9, 1.0, 2.3, 1.6, 1.5, 2.9, 2.4, 2.1, 2.3, 1.8, 2.9, 2.4, 39.5, 34.0, 32.5, 19.5, 18.0, 16.5, 2.3, 24.5, 22.0, 21.5, 15.1, 4.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1);

CREATE TABLE organizational_units_timeline (
                                               start_date DATE NOT NULL,
                                               end_date DATE NOT NULL,
                                               territory_id BIGINT NOT NULL,
                                               territory_name VARCHAR(255) NOT NULL,
                                               mr_id BIGINT NOT NULL,
                                               atp_id BIGINT NOT NULL,
                                               mvz_id VARCHAR(255) NOT NULL,
                                               retail_network VARCHAR(255) NOT NULL,
                                               atp_type VARCHAR(255) NOT NULL,
                                               mvz_type VARCHAR(255),
                                               repshop_id BIGINT NOT NULL,
                                               repshop_name VARCHAR(255) NOT NULL,
                                               atp_name VARCHAR(255) NOT NULL,
                                               mvz_name VARCHAR(255) NOT NULL,
                                               mr_name VARCHAR(255) NOT NULL
);

INSERT INTO organizational_units_timeline (
    start_date,
    end_date,
    territory_id,
    territory_name,
    mr_id,
    atp_id,
    mvz_id,
    retail_network,
    atp_type,
    mvz_type,
    repshop_id,
    repshop_name,
    atp_name,
    mvz_name,
    mr_name
) VALUES
      ('2013-01-01', '2083-12-31', 1, 'Ter1', 1, 101, 'MVZ001', 'NetworkA', 'TypeX', 'Subtype1', 201, 'Repshop Alpha', 'ATP Alpha', 'MVZ Alpha', 'MR Alpha'),
      ('2013-02-01', '2083-11-30', 2, 'Ter2', 2, 102, 'MVZ002', 'NetworkB', 'TypeY', 'Subtype2', 202, 'Repshop Beta', 'ATP Beta', 'MVZ Beta', 'MR Beta'),
      ('2013-03-01', '2083-10-31', 3, 'Ter3', 3, 103, 'MVZ003', 'NetworkC', 'TypeZ', NULL, 203, 'Repshop Gamma', 'ATP Gamma', 'MVZ Gamma', 'MR Gamma'),
      ('2013-04-01', '2083-09-30', 4, 'Ter4', 4, 104, 'MVZ004', 'NetworkD', 'TypeX', 'Subtype1', 204, 'Repshop Delta', 'ATP Delta', 'MVZ Delta', 'MR Delta'),
      ('2013-05-01', '2083-08-31', 5, 'Ter5', 5, 105, 'MVZ005', 'NetworkE', 'TypeY', 'Subtype3', 205, 'Repshop Epsilon', 'ATP Epsilon', 'MVZ Epsilon', 'MR Epsilon');
