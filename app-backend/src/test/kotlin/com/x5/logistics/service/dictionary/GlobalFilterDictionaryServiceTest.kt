package com.x5.logistics.service.dictionary

import com.x5.logistics.repository.RepairShopsDictionaryRepository
import com.x5.logistics.repository.dictionary.GlobalFilterDictionaryRepository
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.ParentLabeledValue
import com.x5.logistics.rest.dto.dictionary.GlobalFilterDictionaryValuesDto
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*

class GlobalFilterDictionaryServiceTest {
    // Arrange
    private val mockRepo: GlobalFilterDictionaryRepository = mock(GlobalFilterDictionaryRepository::class.java)
    private val mockRepshopsRepo: RepairShopsDictionaryRepository = mock(RepairShopsDictionaryRepository::class.java)
    private val mockRes = GlobalFilterDictionaryValuesDto(
        mr = ParentLabeledValue(
            label = "Макрорегион",
            value = "",
            children = mutableListOf(LabeledValue("1", 1))
        ),
        atp = ParentLabeledValue(
            label = "АТП",
            value = "",
            children = mutableListOf(LabeledValue("1", 1))
        ),
        mvz = ParentLabeledValue(
            label = "МВЗ",
            value = "",
            children = mutableListOf(LabeledValue("1", 1))
        ),
        retailNetwork = ParentLabeledValue(
            label = "Торг. сеть",
            value = "",
            children = mutableListOf(LabeledValue("1", 1))
        ),
        atpType = ParentLabeledValue(
            label = "Вид деятельности ∙ ВД",
            value = "",
            children = mutableListOf(LabeledValue("1", 1))
        ),
        mvzType = ParentLabeledValue(
            label = "Тип МВЗ",
            value = "",
            children = mutableListOf(LabeledValue("1", 1))
        ),
        territory = ParentLabeledValue(
            label = "Территория",
            value = "",
            children = mutableListOf(LabeledValue("1", 1))
        )
    )


    @Test
    fun testGetGlobalFilterDictionaryMr() {
        `when`(mockRepo.getGlobalFilterDictionary()).thenReturn(mockRes)
        val service = GlobalFilterDictionaryService(mockRepo, mockRepshopsRepo)
        val result = service.getGlobalFilterDictionary()
        assert(result.mr.children.first().value == 1)
    }

    @Test
    fun testGetGlobalFilterDictionaryAtp() {
        `when`(mockRepo.getGlobalFilterDictionary()).thenReturn(mockRes)
        val service = GlobalFilterDictionaryService(mockRepo, mockRepshopsRepo)
        val result = service.getGlobalFilterDictionary()
        assert(result.atp.children.first().value == 1)
    }

    @Test
    fun testLastElementsInGlobalFilterDictionary() {
        // Arrange
        `when`(mockRepo.getGlobalFilterDictionary()).thenReturn(mockRes)
        val service = GlobalFilterDictionaryService(mockRepo, mockRepshopsRepo)

        // Act
        val result = service.getGlobalFilterDictionary()

        // Assert
        // Check territory.children
        val lastTerritory = result.territory.children.last()
        assert(lastTerritory.label == "Не определено")
        assert(lastTerritory.value == -1)

        // Check mr.children
        val lastMr = result.mr.children.last()
        assert(lastMr.label == "Не определено")
        assert(lastMr.value == -1)

        // Check atp.children
        val lastAtp = result.atp.children.last()
        assert(lastAtp.label == "Не определено")
        assert(lastAtp.value == -1)

        // Check mvzType.children
        val lastMvzType = result.mvzType.children.last()
        assert(lastMvzType.label == "Не определено")
        assert(lastMvzType.value == "Не определено")
    }
}
