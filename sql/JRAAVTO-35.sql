--JRAAVTO-35
-- 642 ����� ��� ����� � ������

with 
 -- ��� ������� ����� ����� �� ������������ ���/���/�� �� ��������� ������
 -- ����� ��� ���-�� ��
ts_timeline as ( select equnr
                   , mr_id
                   , atp_id
                   , mvz_id
                   , start_date
                   , end_date
              from ts.ts_usage_timeline
             where start_date <= cast(:end_date as date)
             and   end_date > cast(:start_date as date)
           )
           
-- ������� ����� �� ������� (����� ������������ �� ������ ����� ������� :end_date)
, ts_max_probeg as (
            select
                distinct on (tph.equnr)
                        tph.equnr
                      , max(readg) as probeg_max
            from ts_probeg_history tph
            where
            date_time <= cast(:end_date as date)
            group by tph.equnr
        )
        
-- ������� ����� ��� �����/������
, q_load_wgt as (
                select distinct 
                      marka
                    , model
                    , gbo
                    , first_value(load_wgt)
                      over (partition by marka, marka, gbo order by count desc, load_wgt desc) as load_wgt
                from (
                    select marka, model, gbo, load_wgt, count(*) as count
                    from ts.ts_data
                    group by marka, model, gbo, load_wgt) _
 )
 
 -- ��� ������ �� ������ � ���������, ��������, ������, �������, ��� � ��������
 -- !! ���� ������ ��� ����� � ������� ������ null �� ���� ����� ������������
 , ts_timeline_all_info as (
                select ts_timeline.*
                     , extract(years from age(cast(:end_date as date), ts_data.create_date))::int * 12 +
                       extract(month from age(cast(:end_date as date), ts_data.create_date))::int as ts_age_months
                     , ts_data.marka
                     , ts_data.model
                     , ts_data.gbo
                     , w.load_wgt
                     , coalesce(ts_max_probeg.probeg_max, 0) as probeg_max
                from ts_timeline
                join ts.ts_data on ts_data.equnr = ts_timeline.equnr
                left join q_load_wgt w on (w.model, w.marka, w.gbo) = (ts_data.model, ts_data.marka, ts_data.gbo)
                left join ts_max_probeg on ts_max_probeg.equnr = ts_timeline.equnr
 )
 
 -- ��� ������� � �������
 , repairs as (
            select   r.order_id
                   , r.vehicle_mr_id
                   , r.vehicle_mr
                   , r.vehicle_atp_id
                   , r.vehicle_atp
                   , r.vehicle_mvz
                   , r.repair_type_id
                   , r.repair_type_name
                   , r.repair_subtype_id
                   , r.repair_subtype_name
                   , r.repair_subtype_color
                   , r.repair_start_date
                   , r.equnr
                   , r.ts_marka
                   , r.ts_model
                   , r.ts_gbo
                   , w.load_wgt as ts_load_wgt
                   , extract(years from age(cast(:end_date as date), r.ts_create_date))::int * 12 +
                     extract(month from age(cast(:end_date as date), r.ts_create_date))::int as ts_age_months
                   , r.repair_expenses
                   , coalesce(ts_max_probeg.probeg_max, 0) as probeg_max
            from ts.repair r
            join q_load_wgt w on (w.model, w.marka, w.gbo) = (r.ts_model, r.ts_marka, r.ts_gbo)
            left join ts_max_probeg on ts_max_probeg.equnr = r.equnr
            where repair_start_date between cast(:start_date as date) and cast(:end_date as date)
            and r.vehicle_mr_id is not null
)

-- ����������� �����
-- ����� ����� �� ��������� ��� �� ������
, ts_count_ur_mvz as ( select count(equnr) as equnr_count
                            , avg(ts_age_months) as ts_age_months_avg
                            , avg(probeg_max) as probeg_max_avg
        from (
              select distinct on (equnr)
               equnr, ts_age_months, probeg_max
              from ts_timeline_all_info
              where (mr_id, atp_id, mvz_id) in (:mvz_triples)
            ) _
           )
           
-- ����� ����� �� ����� �� �� ������
, ts_count_be as ( select count(equnr) as equnr_count
                            , avg(ts_age_months) as ts_age_months_avg
                            , avg(probeg_max) as probeg_max_avg
        from (
              select distinct on (equnr)
               equnr, ts_age_months, probeg_max
              from ts_timeline_all_info
            ) _
           )
           
-- ����� ����� ����� �� � ������� �� �� ������ (��� ������� ������ ��������)
, ts_count_mr as ( select     mr_id
                            , count(equnr) as equnr_count
                            , avg(ts_age_months) as ts_age_months_avg
                            , avg(probeg_max) as probeg_max_avg
        from (
              select distinct on (mr_id, equnr)
               mr_id, equnr, ts_age_months, probeg_max
              from ts_timeline_all_info
            ) _
            group by mr_id
           )
           
-- ����� ����� ����� ��, ������� ����������� ����, � ������� ��/���.������� �� ������ (��� ������� ������ ��������)
, ts_count_mr_toro as ( select vehicle_mr_id
                            , repair_subtype_id
                            , count(equnr) as equnr_count
                            , avg(ts_age_months) as ts_age_months_avg
                            , avg(probeg_max) as probeg_max_avg
        from (
              select distinct on (vehicle_mr_id, repair_subtype_id, equnr)
               vehicle_mr_id, repair_subtype_id, equnr, ts_age_months, probeg_max
              from repairs
            ) _
            group by vehicle_mr_id
                   , repair_subtype_id
           )
           
-- ����� ����� ��� ���������� �� � ������� ��� �� ������ (��� ������� ������ ��������)
, ts_count_atp as ( select     atp_id
                            , count(equnr) as equnr_count
                            , avg(ts_age_months) as ts_age_months_avg
                            , avg(probeg_max) as probeg_max_avg
        from (
              select distinct on (atp_id, equnr)
               atp_id, equnr, ts_age_months, probeg_max
              from ts_timeline_all_info
              where mr_id = :mr_id
            ) _
            group by atp_id
           )
-- ����� ����� ��� ���������� ��, ������� ����������� ����, � ������� ���/���.������� �� ������ (��� ������� ������ ��������)
, ts_count_atp_toro as ( select vehicle_atp_id
                            , repair_subtype_id
                            , count(equnr) as equnr_count
                            , avg(ts_age_months) as ts_age_months_avg
                            , avg(probeg_max) as probeg_max_avg
        from (
              select distinct on (vehicle_atp_id, repair_subtype_id, equnr)
               vehicle_atp_id, repair_subtype_id, equnr, ts_age_months, probeg_max
              from repairs
              where vehicle_mr_id = :mr_id
            ) _
            group by vehicle_atp_id
                   , repair_subtype_id
           )
           


-- ����� ����� �������
-- ����� �������� � ����� ������ ��� ��������� ��� � ������� ��� 
, subtypes_ur_mvz as (select
                          repair_type_id
                        , repair_type_name
                        , repair_subtype_id
                        , repair_subtype_name
                        , repair_subtype_color
                        , count(order_id) as repair_count
                        , sum(repair_expenses) as repair_expenses
                        from repairs
                        where (vehicle_mr_id, vehicle_atp_id, vehicle_mvz) in (:mvz_triples)
                        group by 
                          repair_type_id
                        , repair_type_name
                        , repair_subtype_id
                        , repair_subtype_name
                        , repair_subtype_color
)

-- ����� �������� � ����� ������ ��� ����� �� � ������� ��� 
, subtypes_be as (select
                          repair_type_id
                        , repair_type_name
                        , repair_subtype_id
                        , repair_subtype_name
                        , repair_subtype_color
                        , count(order_id) as repair_count
                        , sum(repair_expenses) as repair_expenses
                        from repairs
                        group by 
                          repair_type_id
                        , repair_type_name
                        , repair_subtype_id
                        , repair_subtype_name
                        , repair_subtype_color
)

-- ����� �������� � ����� ������ ��� ����� �� � ������� ���/�� (��� ������� ������ ��������) 
, subtypes_mr as (select  vehicle_mr_id
                        , vehicle_mr
                        , repair_subtype_id
                        , repair_subtype_name
                        , repair_subtype_color
                        , count(order_id) as repair_count
                        , sum(repair_expenses) as repair_expenses
                        from repairs
                        group by 
                          vehicle_mr_id
                        , vehicle_mr
                        , repair_subtype_id
                        , repair_subtype_name
                        , repair_subtype_color
)
, subtypes_atp as (select vehicle_atp_id
                        , vehicle_atp
                        , repair_subtype_id
                        , repair_subtype_name
                        , repair_subtype_color
                        , count(order_id) as repair_count
                        , sum(repair_expenses) as repair_expenses
                        from repairs
                        where vehicle_mr_id = :mr_id
                        group by 
                          vehicle_atp_id
                        , vehicle_atp
                        , repair_subtype_id
                        , repair_subtype_name
                        , repair_subtype_color
)

-- ������ ��� ������ �� ������
, subtypes_all_info_left as ( 
              select  
              bs.repair_type_id
            , bs.repair_type_name
            , bs.repair_subtype_id
            , bs.repair_subtype_name
            , s.repair_subtype_color
            , coalesce(s.repair_count, 0) as repair_count
            , bs.repair_count as repair_count_be
            , coalesce(s.repair_expenses, 0) as repair_expenses
            , bs.repair_expenses as repair_expenses_be
      from subtypes_be bs
      left join subtypes_ur_mvz s on bs.repair_type_id = s.repair_type_id
      and bs.repair_type_id = s.repair_type_id
      and bs.repair_subtype_id = s.repair_subtype_id
      and bs.repair_type_id = s.repair_type_id
            and bs.repair_subtype_color = s.repair_subtype_color
)

, subtypes_mr_all_info_right as ( 
              select  
              mr.vehicle_mr_id
            , mr.vehicle_mr
            , mr.repair_subtype_id
            , mr.repair_subtype_name
            , mr.repair_subtype_color
            , cast(mr.repair_count as real) / ts_count_mr.equnr_count as repair_ts
            , mr.repair_expenses / ts_count_mr.equnr_count as expenses_ts
            , ts_count_mr_toro.ts_age_months_avg
            , ts_count_mr_toro.probeg_max_avg
      from subtypes_mr mr
      join ts_count_mr on ts_count_mr.mr_id = mr.vehicle_mr_id
      join ts_count_mr_toro on ts_count_mr_toro.vehicle_mr_id = mr.vehicle_mr_id
      and ts_count_mr_toro.repair_subtype_id = mr.repair_subtype_id
)
, subtypes_atp_all_info_right as ( 
              select  
              atp.vehicle_atp_id
            , atp.vehicle_atp
            , atp.repair_subtype_id
            , atp.repair_subtype_name
            , atp.repair_subtype_color
            , cast(atp.repair_count as real) / ts_count_atp.equnr_count as repair_ts
            , atp.repair_expenses / ts_count_atp.equnr_count as expenses_ts
            , ts_count_atp_toro.ts_age_months_avg
            , ts_count_atp_toro.probeg_max_avg
      from subtypes_atp atp
      join ts_count_atp on ts_count_atp.atp_id = atp.vehicle_atp_id
      join ts_count_atp_toro on ts_count_atp_toro.vehicle_atp_id = atp.vehicle_atp_id
      and ts_count_atp_toro.repair_subtype_id = atp.repair_subtype_id
)

-- ����� ����� �������
-- ������������� ���-�� 
select repair_type_id
       , repair_type_name
       , repair_subtype_id
       , repair_subtype_name
       , repair_subtype_color
       , cast(repair_count as real) / coalesce(mvz.equnr_count, 1) as repair_ts -- ���-�� ������� �� ��
       , cast(repair_count as real) / coalesce(mvz.equnr_count, 1) - cast(repair_count_be as real) / coalesce(be.equnr_count, 1) as repair_ts_delta -- ���������� �� ��
from subtypes_all_info_left, ts_count_ur_mvz mvz, ts_count_be be
order by repair_count desc

-- ����� ����� �������
-- ������������� ���������  
--select repair_type_id
--       , repair_type_name
--       , repair_subtype_id
--       , repair_subtype_name
--       , repair_subtype_color
--       , repair_expenses / coalesce(mvz.equnr_count, 1) as expenses_ts -- ��������� ������� �� ��, ���
--       , repair_expenses / coalesce(mvz.equnr_count, 1) - cast(repair_expenses_be as real) / coalesce(be.equnr_count, 1) as expenses_ts_delta -- ���������� �� ��
--from subtypes_all_info_left, ts_count_ur_mvz mvz, ts_count_be be
--order by repair_expenses desc

-- ������ ����� ������� ������� ������� ��������
-- ������������� ���-�� 
--select   vehicle_mr_id
--       , vehicle_mr
--       , repair_subtype_id
--       , repair_subtype_name
--       , repair_subtype_color
--       , repair_ts -- ���-�� ������� �� ��
--       , mr.ts_age_months_avg -- ������� ������� �� �������������� ������ ����, ������
--       , mr.probeg_max_avg -- ������� ������ �� �������������� ������ ����, ��
--       , mr.ts_age_months_avg - be.ts_age_months_avg as age_delta -- ���������� �������� �� ����� ��, ������
--       , mr.probeg_max_avg - be.probeg_max_avg as probeg_delta -- ���������� ������� �� ����� ��, ��
--from subtypes_mr_all_info_right mr, ts_count_be be

-- ������ ����� ������� ������� ������� ��������
-- ������������� ���������
--select   vehicle_mr_id
--       , vehicle_mr
--       , repair_subtype_id
--       , repair_subtype_name
--       , repair_subtype_color
--       , expenses_ts -- ��������� ������� �� ��, ���
--       , mr.ts_age_months_avg -- ������� ������� �� �������������� ������ ����, ������
--       , mr.probeg_max_avg -- ������� ������ �� �������������� ������ ����, ��
--       , mr.ts_age_months_avg - be.ts_age_months_avg as age_delta -- ���������� �������� �� ����� ��, ������
--       , mr.probeg_max_avg - be.probeg_max_avg as probeg_delta -- ���������� ������� �� ����� ��, ��
--from subtypes_mr_all_info_right mr, ts_count_be be

-- ������ ����� ������� ������ ������� �������� (�� ���)
-- ������������� ���-�� 
--select   atp.vehicle_atp_id
--       , atp.vehicle_atp
--       , atp.repair_subtype_id
--       , atp.repair_subtype_name
--       , repair_subtype_color
--       , atp.repair_ts -- ���-�� ������� �� ��
--       , atp.ts_age_months_avg -- ������� ������� �� �������������� ������ ����, ������
--       , atp.probeg_max_avg -- ������� ������ �� �������������� ������ ����, ��
--       , atp.ts_age_months_avg - be.ts_age_months_avg as age_delta -- ���������� �������� �� ����� ��, ������
--       , atp.probeg_max_avg - be.probeg_max_avg as probeg_delta -- ���������� ������� �� ����� ��, ��
--from subtypes_atp_all_info_right atp, ts_count_be be

-- ������ ����� ������� ������ ������� �������� (�� ���)
-- ������������� ���������
--select   atp.vehicle_atp_id
--       , atp.vehicle_atp
--       , atp.repair_subtype_id
--       , atp.repair_subtype_name
--       , repair_subtype_color
--       , expenses_ts -- ��������� ������� �� ��, ���
--       , atp.ts_age_months_avg -- ������� ������� �� �������������� ������ ����, ������
--       , atp.probeg_max_avg -- ������� ������ �� �������������� ������ ����, ��
--       , atp.ts_age_months_avg - be.ts_age_months_avg as age_delta -- ���������� �������� �� ����� ��, ������
--       , atp.probeg_max_avg - be.probeg_max_avg as probeg_delta -- ���������� ������� �� ����� ��, ��
--from subtypes_atp_all_info_right atp, ts_count_be be




