with pls as (
            select qmnum
	            , equnr
	            , mr_id
	            , atp_id
	            , mvz_id
            	, bool_or( remont_flag = 1) as remont_flag
            	, bool_or( idle_flag = 1) as idle_flag
            	, bool_or( no_trips_flag = 1) as no_trips_flag
            	, bool_or( wrong_pl_mvz_flag = 1) as wrong_pl_mvz_flag
            from ts_usage_timeline3 tut
            where (tut.mr_id, tut.atp_id, tut.mvz_id) IN (:mvz_triples)
            	and start_date < cast(:end_date as date) and cast(:start_date as date) < end_date
            group by qmnum
	            , equnr
	            , mr_id
	            , atp_id
	            , mvz_id
        ), filtered_data as (
            select
                pl_dt.user_stat as user_status,
                pl_dt.qmnum as pl_number,
                strmn as pl_start_date,
                strur as pl_start_time,
                ltrmn as pl_end_date_plan,
                ltrur as pl_end_time_plan,
                bezdt as pl_end_date_fact,
                bezur as pl_end_time_fact,
                mvz as pl_mvz,
                mvz_pl.name as pl_mvz_name,
                tab1 as driver_number,
                msaus,
                marka,
                model,
                baujj as year,
                load_wgt,
                td.license_num as vehicle_license,
                td.equnr,
                chassis_num,
                gbo,
                zz_fuel_fact as fuel_pri,
                zz_gas_fact as fuel_sec,
                probeg,
                t.ts_group,
                t.ts_type,
                atp.name as atp_name,
                mvz.name as mvz_name,
                mvz.uid as mvz_id
                , remont_flag 
                , idle_flag 
                , no_trips_flag 
                , wrong_pl_mvz_flag 
            from pls
	            join pl_dt on pls.qmnum = pl_dt.qmnum
	            join ts_data td on pl_dt.equnr = td.equnr
	            join types as t on t.ts_type = td.ts_type
	            join macro_region mr on mr.id = pls.mr_id
	            join atp on atp.id = pls.atp_id
	            join (select distinct uid, name from mvz_log) mvz on mvz.uid = pls.mvz_id
	            join (select distinct uid, name from mvz_log) mvz_pl on mvz_pl.uid = pl_dt.mvz
            where 1 = 1
        )
        select *, count(1) over () as counter
        from filtered_data
        :order_by