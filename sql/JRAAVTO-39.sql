--JRAAVTO-39
-- Топ ремонтов + распределение на СТО
with repair_ur as (select
                          repair.order_id
                        , repair.equnr
                        , ts_data.license_num
                        , repair.our_workshop
                        , repair.repair_subtype_id
                        , repair.repair_subtype_name
                        , repair.repair_expenses
                        from ts.repair
                        join ts.ts_data on ts_data.equnr = repair.equnr
                        where repair_start_date between cast(:start_date as date) and  cast(:end_date as date)
                        and (vehicle_mr_id, vehicle_atp_id, vehicle_mvz) in (:mvz_triples)
           )
-- Максимальные и минимальные стоимости ремонтов
, max_min_expenses as (select  
                       max(repair_expenses) as max_expenses
                     , min(repair_expenses) as min_expenses
                       from repair_ur
           )
-- Группировка всех заказов ТОРО
, expenses_group_all as (select
                           our_workshop
                         , repair_subtype_id
                         , repair_subtype_name
                         , count(order_id) as repair_count_all
                         , sum(repair_expenses) as repair_expenses_all
                       from repair_ur
                       group by 
                           our_workshop
                         , repair_subtype_id
                         , repair_subtype_name
)

-- Группировка заказов ТОРО, где стоимость ремонта отфильтрована
, expenses_group_filter as (select
                           our_workshop
                         , repair_subtype_id
                         , repair_subtype_name
                         , count(order_id) as repair_count_filter
                         , sum(repair_expenses) as repair_expenses_filter
                       from repair_ur, max_min_expenses
                       where repair_expenses >= coalesce(:filter_min, min_expenses)
                       and   repair_expenses <= coalesce(:filter_max, max_expenses)
                       group by 
                           our_workshop
                         , repair_subtype_id
                         , repair_subtype_name
)

-- ЛЕВАЯ ЧАСТЬ
-- AC-JRAAVTO-39-5 значения максимума и минимума по заказам           
-- select  max_expenses -- Максимальная стоимость заказа ТОРО, руб
--      , min_expenses -- Минимальная стоимость заказа ТОРО, руб
-- from max_min_expenses

-- ЛЕВАЯ ЧАСТЬ
-- AC-JRAAVTO-39-6 распред. затрат по подвидам и месту выполнения
select  egl.our_workshop  -- РЕМЗОНА - true СТО - false
     , egl.repair_subtype_id -- id подвида ремонта
     , egl.repair_subtype_name -- Название подвида ремонта
     , repair_expenses_all -- Затраты в рублях всего (КРАСИТЬ ПОЛУПРОЗРАЧНЫМ)
     , coalesce(repair_expenses_filter, 0) as repair_expenses_filter -- Затраты в рублях по фильтру (КРАСИТЬ НЕПРОЗРАЧНЫМ, ОТОБРАЖАТЬ В ПОДСКАЗКЕ)
     , repair_count_all -- Кол-во ремонтов всего (отображать в подсказке. второе число через /)
     , coalesce(repair_count_filter, 0) as repair_count_filter -- Кол-во ремонтов по фильтру (отображать в подсказке. первое число через /)
from  expenses_group_all egl
left join expenses_group_filter egf
on egl.our_workshop = egf.our_workshop
and egl.repair_subtype_id = egf.repair_subtype_id
and egl.repair_subtype_name = egf.repair_subtype_name


-- ПРАВАЯ ЧАСТЬ
-- AC-JRAAVTO-39-9 ТОП ЗАКАЗОВ, руб          
--select  order_id -- Заказ ТОРО
--      , license_num -- Гос номер ТС
--      , repair_expenses -- Затраты, руб
--from repair_ur, max_min_expenses
--where repair_expenses >= coalesce(:filter_min, min_expenses)
--and   repair_expenses <= coalesce(:filter_max, max_expenses)
--order by repair_expenses DESC
--limit 10